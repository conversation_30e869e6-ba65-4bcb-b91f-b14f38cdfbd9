import { NextRequest, NextResponse } from "next/server";
import { verifyRefreshToken } from "@/utils/auth";
import {
  getJWTPayloadInfo,
  validateAccountRoleAccess,
  validateUserInAccountRoleAccess,
} from "@/utils/roles";
import getEnv from "@/hooks/getEnv";

const publicPages = ["/login"];

const protectedPages = ["/portfolio/:path*", "/imports/:path*", "/page-test"];

function testPagesRegex(pages: string[], pathname: string) {
  const regex = `^(${pages.map((p) => p.replace("/:path*", "(/.*)?")).join("|")})$`;
  return new RegExp(regex, "i").test(pathname);
}

const handleAuth = async (
  req: NextRequest,
  isPublicPage: boolean,
  isProtectedPage: boolean
) => {
  if (getEnv("SERVICE_MOCK_RESPONSE")) {
    return NextResponse.next();
  }
  const pathname = req.nextUrl.pathname;

  const token = req.cookies.get("digai.refreshToken")?.value;

  if (!token && isProtectedPage) {
    let from = req.nextUrl.pathname;
    if (req.nextUrl.search) {
      from += req.nextUrl.search;
    }

    console.warn("Missing refreshToken cookie. Redirecting to login.");
    return NextResponse.redirect(
      new URL(`/?from=${encodeURIComponent(from)}`, req.url)
    );
  }

  let isTokenActive = false;

  if (token) {
    try {
      isTokenActive = await verifyRefreshToken(token);
    } catch (error) {
      console.warn("Error verifying refreshToken:", error);
    }
  }

  const isAuth = !!token && isTokenActive;

  // When the user tries to access protected routes without being authenticated
  if (!isAuth && isProtectedPage) {
    let from = req.nextUrl.pathname;
    if (req.nextUrl.search) {
      from += req.nextUrl.search;
    }

    console.warn("Client is not authenticated. Redirecting to login page.");
    return NextResponse.redirect(
      new URL(`/?from=${encodeURIComponent(from)}`, req.url)
    );
  }

  const jwtInfo = getJWTPayloadInfo(token!);

  // When the account is authenticated but does not have the proper permissions to access this route.

  const redirectUserNotValidAccountRole = validateAccountRoleAccess(
    jwtInfo.accountRole,
    pathname
  );

  if (isAuth && redirectUserNotValidAccountRole && isProtectedPage) {
    console.warn(
      `Account does not have the proper permissions to access this route. AccoundID ${jwtInfo.accountId}`
    );
    // TODO - definir uma pagina padrão para redirecionar o usuário em uma area logada
    return NextResponse.redirect(new URL("/portfolio", req.url).toString());
  }

  // When the user is authenticated but does not have the proper permissions in the account to access this route.
  const redirectUserNotValidUserInAccountRole = validateUserInAccountRoleAccess(
    jwtInfo.userRoleInAccount,
    pathname
  );

  if (isAuth && redirectUserNotValidUserInAccountRole && isProtectedPage) {
    console.warn(
      `User does not have the proper permissions in account to access this route. UserID ${jwtInfo.userId} - AccountID ${jwtInfo.accountId}`
    );
    // TODO - definir uma pagina padrão para redirecionar o usuário em uma area logada
    return NextResponse.redirect(new URL("/portfolio", req.url).toString());
  }

  // When the authenticated user tries to access public routes
  if (isAuth && isPublicPage) {
    // TODO - definir uma pagina padrão para redirecionar o usuário em uma area logada
    return NextResponse.redirect(new URL("/portfolio", req.url).toString());
  }

  return NextResponse.next();
};

export default async function middleware(req: NextRequest) {
  const isProtectedPage = testPagesRegex(protectedPages, req.nextUrl.pathname);
  const isPublicPage = testPagesRegex(publicPages, req.nextUrl.pathname);

  return handleAuth(req, isPublicPage, isProtectedPage);
}

export const config = {
  matcher: ["/imports/:path*", "/page-test", "/portfolio/:path*"],
};
