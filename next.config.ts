import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  compiler: {
    styledComponents: true,
  },
  async rewrites() {
    const serviceUrl = process.env.NEXT_PUBLIC_TRANSCENDENCE_SERVICE_URL;
    
    // Only add rewrites if the service URL is defined
    if (!serviceUrl) {
      console.warn('NEXT_PUBLIC_TRANSCENDENCE_SERVICE_URL is not defined, skipping API rewrites');
      return [];
    }
    
    return [
      {
        source: "/api/v1/:path*",
        destination: `${serviceUrl}/v1/:path*`,
      },
    ];
  },
};

export default nextConfig;
