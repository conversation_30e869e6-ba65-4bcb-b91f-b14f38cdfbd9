{"name": "digai-negocia", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3390", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/charts": "^2.6.1", "@ant-design/plots": "^2.6.2", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@radix-ui/react-select": "^2.2.5", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "^5.80.2", "@tanstack/react-table": "^8.21.3", "antd": "^5.25.2", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.509.0", "next": "15.3.2", "papaparse": "^5.5.3", "postcss": "^8.5.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "recharts": "^2.15.4", "styled-components": "^6.1.18", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/papaparse": "^5.3.16", "@types/react": "^19", "@types/react-dom": "^19", "@types/styled-components": "^5.1.34", "eslint": "^9", "eslint-config-next": "15.3.2", "prettier": "^3.5.3", "tw-animate-css": "^1.3.6", "typescript": "^5"}}