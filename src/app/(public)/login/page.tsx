"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";
import Button from "@/components/Button";
import Input from "@/components/LoginInput";
import {
  <PERSON><PERSON>,
  Wrapper,
  FlexWrapper,
  ErrorMessage,
  HeadTitle,
} from "./styles";
import { Fetch } from "@/interceptors";
import { DigaiLogo } from "@/assets/logos/DigaiLogo";
import { Container } from "@/components/Container";
import { Form } from "antd";

type LoginValues = {
  email: string;
  password: string;
};

export default function LoginPage() {
  const [showError, setShowError] = useState("");
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleFinish = async (values: LoginValues) => {
    setLoading(true);
    setShowError("");

    try {
      const api = Fetch();
      const response = await api.post(`api/v1/auth/session/login`, values);
      localStorage.setItem("customer", JSON.stringify(response));

      router.push("/dashboard");
    } catch (err: unknown) {
      if (err instanceof Error) {
        setShowError(err.message || "Erro ao fazer login.");
      } else {
        setShowError("Erro desconhecido ao fazer login.");
      }
      setLoading(false);
    }
  };

  return (
    <Wrapper>
      <Header>
        <DigaiLogo />
      </Header>
      <Container>
        <FlexWrapper>
          <Form
            onFinish={handleFinish}
            autoComplete="off"
            className="custom-form"
            name="basic"
            initialValues={{ remember: true }}
          >
            <HeadTitle>Bem-vindo(a) de volta</HeadTitle>

            <Input
              label="Email"
              name="email"
              type="email"
              rules={[{ required: true, message: "Email é obrigatório" }]}
            />
            <Input
              label="Senha"
              name="password"
              passwordInput
              rules={[{ required: true, message: "Senha é obrigatória" }]}
            />
            {showError && <ErrorMessage>{showError}</ErrorMessage>}

            <Button
              type="submit"
              label="Entrar"
              variant="primary"
              size="md"
              loading={loading}
              style={{ width: "100%", cursor: "pointer" }}
            />
          </Form>
        </FlexWrapper>
      </Container>
    </Wrapper>
  );
}
