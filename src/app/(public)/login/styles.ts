"use client";
import styled from "styled-components";

export const Wrapper = styled.div`
  padding-top: 100px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fefefe;
  flex-direction: column;
`;

export const Header = styled.div`
  position: sticky;
  top: 0;
  z-index: 100;
`;

export const HeadTitle = styled.h1`
  font-family: Switzer;
  color: #0f1681;
  font-size: 32px;
  line-height: 47px;
  font-weight: 500;
  margin-bottom: 12px;
`;

export const Button = styled.button`
  width: 100%;
  padding: 0.75rem;
  background-color: #222;
  color: white;
  border: none;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;

  &:disabled {
    background-color: #888;
    cursor: not-allowed;
  }
`;

export const ErrorMessage = styled.div`
  color: #b00020;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  text-align: center;
  font-family: <PERSON>witz<PERSON>;
`;

export const FlexWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 16px 0;
  text-align: center;
  width: 100%;
  .ant-modal-root > .ant-modal-mask {
    background-color: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(2px);
  }

  p {
    display: flex;
    justify-content: center;
    max-width: 460px;
    color: var(--primary-color);
    color: inherit;
    font-family: var(--font-roboto);
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 24px;
  }
  .custom-form {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 345px;
    width: 100%;
    gap: 16px;
    font-family: var(--font-inter);

    h2 {
      font-size: 16px;
      color: var(--primary-color);
    }
    &:not(.modal) {
      min-height: calc(75vh - 32px);
    }
    b {
      font-family: var(--font-inter);
    }
  }
  .score-button {
    background: transparent;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
  .button-wrapper {
    display: flex;
    align-items: center;
    justify-content: right;
    gap: 8px;
    width: 100%;
  }

  & .ant-modal {
    padding: unset;
  }

  .ant-slider .ant-slider-track {
    background-color: #111c9d;
  }
  .double-colored .ant-slider-track,
  .double-colored .ant-slider-rail {
    background-color: #111c9d;
  }

  @media only screen and (max-width: 576px) {
    & form {
      padding-top: 16px;
    }
  }
`;
