/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import Header from "@/components/Header";
import { ChartPie } from "lucide-react";
import { Container } from "@/components/Container";
import DateRangeSelector from "@/components/DateRangeSelector";
import { ChartBarMultiple } from "@/components/Charts/BarChart";
import CardsMetrics from "@/components/DashboardPage/CardsMetrics";
import { styled } from "styled-components";
import { useDateRange } from "@/hooks/useDateRange";

const DateRangeContainer = styled.div`
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  gap: 16px;
`;

const ChartWrapper = styled.div`
  width: 100%;
  height: 296px;
`;

const DashboardPage = () => {
  const { selection, startDate, endDate, onChange } = useDateRange("7");

  return (
    <>
      <Header
        title="Dashboard"
        icon={<ChartPie size={24} />}
        isSidebarOpen={true}
      />
      <Container>
        <div style={{ display: "flex", flexDirection: "column", gap: 24 }}>
          <DateRangeContainer>
            <DateRangeSelector initialValue={selection} onChange={onChange} />
          </DateRangeContainer>
          <CardsMetrics startDate={startDate} endDate={endDate} />
        </div>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 196,
            marginBottom: 100,
          }}
        >
          <ChartWrapper>
            <ChartBarMultiple startDate={startDate} endDate={endDate} />
          </ChartWrapper>
        </div>
      </Container>
    </>
  );
};

export default DashboardPage;
