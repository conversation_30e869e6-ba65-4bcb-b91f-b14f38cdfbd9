"use client";

import { usePathname } from "next/navigation";
import Header from "@/components/Header";
import { Download } from "lucide-react";
import { Container } from "@/components/Container";
import TopSection from "@/components/ImportsPage/TopSection";
import { useEffect, useState, useCallback } from "react";
import ImportsFilters from "@/components/ImportsPage/ImportsFilters";
import ImportsCardList from "@/components/ImportsPage/ImportsCardList";
import FailModal from "@/components/ImportsPage/FailModal";
import { usePortfoliosImports } from "@/hooks/usePortfoliosImports";
import { useImportFailRows } from "@/hooks/useImportFailRows";
import { useImportsFilters } from "@/hooks/usePortfolioImportsFilters";
import { downloadFile } from "@/utils/downloadFile";
import { downloadImportErrorsFile } from "@/services/portfolioService";

export default function ImportsPageClient() {
  const pathname = usePathname();
  const title = pathname === "/imports/create" ? "Importar" : "Importações";

  const [failModalOpen, setFailModalOpen] = useState(false);
  const [selectedFailId, setSelectedFailId] = useState<string | null>(null);
  const [failPageIndex, setFailPageIndex] = useState(0);
  const [failPageSize, setFailPageSize] = useState(10);

  const {
    failRows,
    loading: failRowsLoading,
    fetchFailRows,
  } = useImportFailRows();

  const handleFailClick = useCallback(
    (id: string) => {
      setFailModalOpen(true);
      setSelectedFailId(id);
      setFailPageIndex(0);
      setFailPageSize(10);
      fetchFailRows(id);
    },
    [fetchFailRows]
  );

  const { data: cardList, loadingInitial } = usePortfoliosImports({
    onFailClick: handleFailClick,
  });

  const {
    tabCounts,
    currentTab,
    setCurrentTab,
    selectedEmbedded,
    setSelectedEmbedded,
    selectedExecutionStatus,
    setSelectedExecutionStatus,
    searchTerm,
    setSearchTerm,
    filterCards,
    getInitialTab,
  } = useImportsFilters(cardList);

  useEffect(() => {
    const idealTab = getInitialTab(tabCounts);
    if (currentTab === "em-andamento" && tabCounts.emAndamento === 0) {
      setCurrentTab(idealTab);
    }
    if (currentTab === "concluidas" && tabCounts.concluidas === 0) {
      setCurrentTab(idealTab);
    }
  }, [cardList, tabCounts, currentTab, setCurrentTab, getInitialTab]);

  const filteredCards = filterCards(currentTab, cardList);

  const handleDownloadFailCsv = async () => {
    if (!selectedFailId) return;
    try {
      const blob = await downloadImportErrorsFile(selectedFailId);
      downloadFile(blob, `import-fails-${selectedFailId}.csv`);
    } catch (err) {
      console.error("Erro ao baixar CSV de falhas.", err);
      alert("Erro ao baixar CSV de falhas.");
    }
  };

  const failRowsPaginated = failRows.slice(
    failPageIndex * failPageSize,
    failPageIndex * failPageSize + failPageSize
  );

  return (
    <>
      <Header
        title={title}
        icon={<Download size={24} />}
        isSidebarOpen={true}
      />
      <Container>
        <TopSection
          description={
            <p style={{ display: "flex", flexWrap: "wrap" }}>
              Para iniciar as negociações realizadas pela nossa IA, importe seu
              portfólio nos formatos .xml ou .csv, seguindo os padrões de
              importação. Acompanhe o desempenho em tempo real e, assim que o
              processo for concluído, as negociações automatizadas serão
              ativadas.
            </p>
          }
          tabs={{
            items: [
              {
                label: "Em andamento",
                value: "em-andamento",
                count: tabCounts.emAndamento,
              },
              {
                label: "Concluídas",
                value: "concluidas",
                count: tabCounts.concluidas,
              },
              { label: "Todas", value: "todas", count: tabCounts.todas },
            ],
            value: currentTab,
            onChange: setCurrentTab,
          }}
        />
        <ImportsFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          selectedEmbedded={selectedEmbedded}
          setSelectedEmbedded={setSelectedEmbedded}
          selectedExecutionStatus={selectedExecutionStatus}
          setSelectedExecutionStatus={setSelectedExecutionStatus}
          currentTab={currentTab}
        />
        <ImportsCardList
          data={filteredCards}
          allData={cardList}
          isLoading={loadingInitial}
        />
        <FailModal
          isOpen={failModalOpen}
          onClose={() => setFailModalOpen(false)}
          failRows={failRows}
          failRowsLoading={failRowsLoading}
          failRowsPaginated={failRowsPaginated}
          failPageIndex={failPageIndex}
          failPageSize={failPageSize}
          setFailPageIndex={setFailPageIndex}
          setFailPageSize={setFailPageSize}
          onDownload={handleDownloadFailCsv}
        />
      </Container>
    </>
  );
}
