/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { useState } from "react";
import Breadcrumb from "@/components/Breadcrumb";
import { useBreadcrumbs } from "@/hooks/useBreadcrumb";
import Button from "@/components/Button";
import Step1 from "@/components/steps/Step1";
import Step2 from "@/components/steps/Step2";
import styled from "styled-components";
import { getCustomer } from "@/services/customerService";
import { MdOutlineKeyboardDoubleArrowRight } from "react-icons/md";
import { FiCheckCircle } from "react-icons/fi";
import { LuDownload } from "react-icons/lu";
import Papa from "papaparse";
import {
  downloadTemplateCsv,
  getAllWorkflows,
  getWorkflowVariables,
} from "@/services/workflowService";
import { createPortfolio } from "@/services/portfolioService";
import { CardWorkflowProps } from "@/components/Card/CardWorkflow/CardWorkflow";
import { useRouter } from "next/navigation";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/useToast";
import Header from "@/components/Header";
import { useCsvDelimiter } from "@/hooks/useCsvDelimiter";

export default function CreatePortfolioPage() {
  const [step, setStep] = useState(0);
  const crumbs = useBreadcrumbs();
  const [title, setTitle] = useState("");
  const [workflow, setWorkflow] = useState<string | undefined>(undefined);
  const [executeImmediately, setExecuteImmediately] = useState<boolean>(false);
  const [file, setFile] = useState<File | null>(null);
  const [communicationChannel, setCommunicationChannel] = useState<string>("");
  const customer = getCustomer();
  const customerId = customer.id;
  const { addToast, clearToasts } = useToast();
  const delimiter = useCsvDelimiter();

  const router = useRouter();

  const { data: workflows = [], isLoading: isLoadingWorkflows } = useQuery({
    queryKey: ["workflows", customerId],
    queryFn: () => getAllWorkflows(customerId),
  });

  const {
    data: workflowVariables = [],
    isLoading: isLoadingWorkflowVariables,
  } = useQuery({
    queryKey: ["workflow-variables", workflow],
    queryFn: () =>
      workflow ? getWorkflowVariables(workflow) : Promise.resolve([]),
    enabled: !!workflow,
    staleTime: 5 * 60 * 1000,
  });

  const { mutate: submitPortfolio, isPending } = useMutation({
    mutationFn: () =>
      createPortfolio({
        file: file!,
        name: title,
        workflowId: workflow!,
        executeImmediately: executeImmediately,
        communicationChannel: communicationChannel,
      }),
    onSuccess: () => {
      clearToasts();
      addToast({
        message: "Portfólio criado com sucesso!",
        variant: "success",
      });
      router.push("/imports");
    },
    onError: (error) => {
      addToast({
        message: "Erro ao criar portfólio!",
        variant: "error",
        actionText: "Ver detalhes",
        persistent: true,
        onAction: () => {
          addToast({
            variant: "error",
            message: String(error),
            persistent: true,
          });
        },
      });
    },
  });

  const handleSubmit = () => {
    if (!file || !title || !workflow || !communicationChannel) return;
    submitPortfolio();
  };

  const handleSelectWorkflow = (id: string) => {
    setWorkflow(id);
  };

  const mappedWorkflows: CardWorkflowProps[] = workflows.map((wf) => ({
    id: wf.workflowId,
    title: wf.workflowName,
    workflowDescription: wf.workflowDescription || "",
    categoryLabel: "Atendimento",
    templateColumns: wf.workflowId === workflow ? workflowVariables : [],
    selected: workflow === wf.workflowId,
    onSelect: handleSelectWorkflow,
  }));

  const selectedWorkflow =
    mappedWorkflows.find((w) => w.id === workflow)?.title ?? "";
  const handleNext = () => {
    setStep((s) => s + 1);
  };
  const handleDownloadTemplate = async (id: string) => {
    try {
      const blob = await downloadTemplateCsv(id);
      let finalBlob = blob;

      try {
        const text = await blob.text();
        const parsed = Papa.parse<string[]>(text, { header: false });

        const csv = Papa.unparse(parsed.data as any, {
          delimiter,
          newline: "\r\n",
          quotes: false,
        });
        finalBlob = new Blob([csv], { type: "text/csv;charset=utf-8" });
      } catch (reformatErr) {
        console.warn(
          "Falha ao reformatar CSV pelo delimitador preferido:",
          reformatErr
        );
      }

      const url = window.URL.createObjectURL(finalBlob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "template.csv";
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (e) {
      console.error("Erro ao baixar template:", e);
      addToast({ message: "Erro ao baixar template", variant: "error" });
    }
  };

  const canNext = () => {
    if (step === 0) {
      return title.trim() !== "" && !!workflow && !!communicationChannel;
    }
    if (step === 1) {
      return file !== null;
    }
    return true;
  };

  const steps = [
    {
      label: "Dados Gerais",
      component: (
        <Step1
          title={title}
          onChangeTitleAction={setTitle}
          workflow={workflow}
          onSelectWorkflowAction={handleSelectWorkflow}
          onDownloadTemplateAction={handleDownloadTemplate}
          workflows={mappedWorkflows}
          isLoading={isLoadingWorkflows || isLoadingWorkflowVariables}
          communicationChannel={communicationChannel}
          onChangeCommunicationChannelAction={setCommunicationChannel}
          executeImmediately={executeImmediately}
          onChangeExecuteImmediatelyAction={setExecuteImmediately}
        />
      ),
    },
    {
      label: "Upload",
      component: (
        <Step2
          title={title}
          workflow={workflow}
          workflowTitle={selectedWorkflow}
          file={file}
          onChangeFileAction={setFile}
          onDownloadTemplateAction={handleDownloadTemplate}
          communicationChannel={communicationChannel}
          executeImmediately={executeImmediately}
        />
      ),
    },
  ];

  const isLastStep = step === steps.length - 1;
  const isDisabled = step === 0 ? !canNext() : step === 1 && !file;

  return (
    <>
      <Header
        title="Importar"
        icon={<LuDownload size={24} />}
        isSidebarOpen={true}
      />
      <MainContainer>
        <Wrapper>
          <Breadcrumb items={crumbs} />
          <Content>
            <SideNav>
              {steps.map((s, i) => (
                <Button
                  key={i}
                  variant={i === step ? "gray" : "ghost"}
                  size="xs"
                  label={s.label}
                  onClick={() => setStep(i)}
                  disabled={i > step}
                />
              ))}
            </SideNav>

            <StepContainer>{steps[step].component}</StepContainer>
          </Content>
          <Actions>
            <Button
              variant="outlined"
              label="Voltar"
              onClick={() => {
                if (step === 0) {
                  router.push("/imports");
                } else {
                  setStep(step - 1);
                }
              }}
              disabled={false}
            />
            <Button
              variant={isDisabled ? "disabled" : "primary"}
              label={isLastStep ? "Salvar importação" : "Próximo passo"}
              icon={
                isLastStep ? (
                  <FiCheckCircle size={16} />
                ) : (
                  <MdOutlineKeyboardDoubleArrowRight size={16} />
                )
              }
              onClick={isLastStep ? handleSubmit : handleNext}
              disabled={isDisabled || isPending}
            />
          </Actions>
        </Wrapper>
      </MainContainer>
    </>
  );
}

const MainContainer = styled.div`
  height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const SideNav = styled.nav`
  display: flex;
  flex-direction: column;
  gap: 8px;

  @media (max-width: 1024px) {
    position: static;
  }
`;

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
`;

const Content = styled.div`
  display: grid;
  grid-template-columns: 220px minmax(600px, 1fr);
  padding: 0 28px;
  flex: 1;
  overflow: auto;
  align-items: start;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
`;

const StepContainer = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
`;

const Actions = styled.div`
  border-top: 1px solid ${({ theme }) => theme.colors.neutral400};
  background-color: ${({ theme }) => theme.colors.neutral200};
  max-width: 100vw;
  min-height: 85px;
  padding: 0 28px;
  display: flex;
  justify-content: flex-end;
  flex-direction: row;
  flex-shrink: 0;
  position: sticky;
  bottom: 0;
  z-index: 100;
  gap: 16px;
  align-items: center;
`;
