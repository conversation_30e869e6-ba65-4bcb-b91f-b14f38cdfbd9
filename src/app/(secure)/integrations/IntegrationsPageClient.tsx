"use client";
import Header from "@/components/Header";
import { Settings } from "lucide-react";
import { Container } from "@/components/Container";
import CardSearch from "@/components/Card/CardSearch/CardSearch";
import { Cobransaas } from "@/assets/images/CobranSaaS";
import { Saleszap } from "@/assets/images/SalesZap";
import { Fideleasy } from "@/assets/images/Fideleasy";
import styled from "styled-components";
import { Switch } from "antd";

const CardList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(310px, 359px));
  gap: 16px;
  margin-top: 40px;
`;

const IntegrationsDescription = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  justify-content: center;
  margin-left: 50px;

  p {
    margin: 0;
    font-size: 14px;
    font-weight: 400;
  }
`;
export default function IntegrationsPageClient() {
  return (
    <>
      <Header
        title="Integrações"
        icon={<Settings size={24} />}
        isSidebarOpen={true}
      />
      <Container>
        <CardList>
          <CardSearch
            key={1}
            maxHeight="200px"
            headerPadding="14px 24px 10px 75px;"
            alignItems="center"
            header={{
              title: "CobranSaaS ",
              image: <Cobransaas />,
              actions: (
                <Switch checked={true} style={{ cursor: "not-allowed" }} />
              ),
            }}
            sections={[
              <IntegrationsDescription key={1}>
                <p>
                  A CobranSaaS é uma plataforma especializada em gestão e
                  automação de processos de cobrança, ajudando empresas a
                  recuperar crédito de forma eficiente.{" "}
                </p>
              </IntegrationsDescription>,
            ]}
          />
          <CardSearch
            key={2}
            maxHeight="200px"
            headerPadding="14px 24px 10px 75px;"
            alignItems="center"
            header={{
              title: "SalesZap ",
              image: <Saleszap />,
              actions: (
                <Switch checked={true} style={{ cursor: "not-allowed" }} />
              ),
            }}
            sections={[
              <IntegrationsDescription key={1}>
                <p>
                  Conecte SalesZap à nossa plataforma para automatizar o
                  processo de vendas e cobrança de forma rápida e eficiente.
                  Integrando o SalesZap ao nosso sistema, você potencializa suas
                  interações
                </p>
              </IntegrationsDescription>,
            ]}
          />
          <CardSearch
            key={3}
            maxHeight="200px"
            headerPadding="14px 24px 10px 75px;"
            alignItems="center"
            header={{
              title: "Fideleasy ",
              image: <Fideleasy />,
              actions: (
                <Switch checked={true} style={{ cursor: "not-allowed" }} />
              ),
            }}
            sections={[
              <IntegrationsDescription key={1}>
                <p>
                  Fideleasy melhora o relacionamento com clientes e recupera
                  dívidas de forma mais eficiente. A integração permite que a IA
                  busque seus clientes, oferecendo opções de negociação e
                  solucionando pendências.
                </p>
              </IntegrationsDescription>,
            ]}
          />
        </CardList>
      </Container>
    </>
  );
}
