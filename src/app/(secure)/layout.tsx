import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { verifyRefreshToken } from "@/utils/auth";
import type { ReactNode } from "react";
import ClientLayout from "../ClientLayout";

export default async function ProtectedLayout({
  children,
}: {
  children: ReactNode;
}) {
  const cookieStore = await cookies();
  const token = cookieStore.get("digai.refreshToken")?.value;

  if (!token) {
    redirect("/login");
  }

  const isValid = Boolean(token) && (await verifyRefreshToken(token));

  if (!isValid) {
    redirect("/login");
  }

  return <ClientLayout>{children}</ClientLayout>;
}
