/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Container } from "@/components/Container";
import SelectInput from "@/components/Form/SelectInput";
import TextInput from "@/components/Form/Input";
import Header from "@/components/Header";
import TopSection from "@/components/ImportsPage/TopSection";
import { theme } from "@/styles/theme";
import { FiMessageCircle, FiSearch } from "react-icons/fi";
import { Switch } from "antd";
import { Table } from "@/components/Table";
import StatusTag from "@/components/StatusTag";
import { ExternalLink } from "lucide-react";
import { IPortfolioItem, PortfolioItemStatus } from "@/types/portfolio";
import { formatDateDefault } from "@/utils/formatDate";
import { portfolioItemStatusMap } from "@/constants/portfolioItemStatusMap";
import { formatPhone, onlyNumbers } from "@/utils/formatPhone";
import styled from "styled-components";
import { useNegotiations } from "@/hooks/useNegotiations";
import Link from "next/link";
import { useEffect, useState, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
export const FiltersWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
  width: 100%;
`;

export const SwitchWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  min-width: 180px;
  padding: 0 10px;

  p {
    font-size: 14px;
    font-weight: 500;
    color: ${theme.colors.neutral900};
    margin: 0;
  }
`;
export const NegotiationsTableWrapper = styled.div`
  table th:nth-child(1),
  table td:nth-child(1) {
    width: 40%;
  }
  table th:nth-child(2),
  table td:nth-child(2) {
    width: 50%;
  }
  table th:nth-child(3),
  table td:nth-child(3) {
    width: 45%;
  }
  table th:nth-child(4),
  table td:nth-child(4) {
    width: 36%;
  }
  table th:nth-child(5),
  table td:nth-child(5) {
    width: 20%;
  }

  display: flex;
  flex-wrap: wrap;
  min-width: 300px;
`;

export default function NegotiationsPageClient() {
  const searchParams = useSearchParams();

  const router = useRouter();
  const [hydrated, setHydrated] = useState(false);

  const initialTab =
    (searchParams.get("tab") as "negociacoes" | "concluidas") || "negociacoes";
  const initialPageIndex = Math.max(
    0,
    Number(searchParams.get("page") || 1) - 1
  );
  const initialPageSize = Number(searchParams.get("limit")) || 10;
  const initialPhone = searchParams.get("phone") || "";
  const initialStatus =
    (searchParams.get("status") as PortfolioItemStatus | "todos") || "todos";
  const initialOnlyInteraction = searchParams.get("onlyInteraction") === "true";
  const initialWaitingBusinessUserResponse =
    searchParams.get("waitingBusinessUserResponse") === "true";

  const [tab, setTab] = useState<"negociacoes" | "concluidas">(initialTab);

  const {
    items,
    pageIndex,
    pageSize,
    setPageIndex,
    setPageSize,
    searchPhone,
    setSearchPhone,
    statusFilter,
    setStatusFilter,
    onlyInteraction,
    setOnlyInteraction,
    waitingBusinessUserResponse,
    setWaitingBusinessUserResponse,
    loading,
    filteredTotal,
  } = useNegotiations(tab);

  useEffect(() => {
    setPageIndex(initialPageIndex);
    setPageSize(initialPageSize);
    setSearchPhone(initialPhone);
    setStatusFilter(initialStatus);
    setOnlyInteraction(initialOnlyInteraction);
    setWaitingBusinessUserResponse(initialWaitingBusinessUserResponse);
    setTab(initialTab);
    setHydrated(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const updateUrl = useCallback(
    (params: Record<string, string | number | boolean | undefined>) => {
      const current = new URLSearchParams(window.location.search);

      for (const [key, value] of Object.entries(params)) {
        if (value !== undefined && value !== "" && value !== false) {
          current.set(key, String(value));
        } else {
          current.delete(key);
        }
      }

      const newQs = current.toString();
      if (newQs !== window.location.search.replace(/^\?/, "")) {
        router.replace(`?${newQs}`, { scroll: false });
      }
    },
    [router]
  );

  useEffect(() => {
    if (!hydrated) return;
    updateUrl({
      tab,
      page: pageIndex + 1,
      limit: pageSize,
      phone: searchPhone,
      status: statusFilter,
      onlyInteraction,
      waitingBusinessUserResponse,
    });
  }, [
    hydrated,
    tab,
    pageIndex,
    pageSize,
    searchPhone,
    statusFilter,
    onlyInteraction,
    waitingBusinessUserResponse,
    updateUrl,
  ]);

  useEffect(() => {
    setTab(
      (searchParams.get("tab") as "negociacoes" | "concluidas") || "negociacoes"
    );
    setPageIndex(Math.max(0, Number(searchParams.get("page") || 1) - 1));
    setPageSize(Number(searchParams.get("limit")) || 10);
    setSearchPhone(searchParams.get("phone") || "");
    setStatusFilter(
      (searchParams.get("status") as PortfolioItemStatus | "todos") || "todos"
    );
    setOnlyInteraction(searchParams.get("onlyInteraction") === "true");
    setWaitingBusinessUserResponse(
      searchParams.get("waitingBusinessUserResponse") === "true"
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]);

  const handleOpenChat = (item: IPortfolioItem) => {
    const p = new URLSearchParams();

    if (searchPhone) p.set("phone", searchPhone);
    if (statusFilter && statusFilter !== "todos") p.set("status", statusFilter);
    if (onlyInteraction) p.set("onlyInteraction", String(onlyInteraction));
    if (waitingBusinessUserResponse) {
      p.set("waitingBusinessUserResponse", String(waitingBusinessUserResponse));
    }

    p.set("page", String(pageIndex + 1));

    p.set("negotiationId", item.id);
    router.push(`/negotiations/list?${p.toString()}`, { scroll: false });
  };

  return (
    <>
      <Header
        title="Negociações"
        icon={<FiMessageCircle size={24} />}
        isSidebarOpen={true}
      />
      <Container
        style={{
          display: "flex",
          flexDirection: "column",
          gap: 24,
          flexWrap: "wrap",
        }}
      >
        <TopSection
          tabs={{
            items: [
              {
                label: "Negociações",
                value: "negociacoes",
                count: filteredTotal,
              },
            ],
            value: tab,
            onChange: (v) => {
              setTab(v as any);
              setPageIndex(0);
            },
          }}
        />
        <FiltersWrapper>
          <div style={{ flex: "1 1 0", width: "100%" }}>
            <TextInput
              placeholder="Pesquisar por telefone"
              iconLeft={<FiSearch size={24} color={theme.colors.labelInput} />}
              style={{ width: "100%" }}
              selectLeft={
                <SelectInput
                  variant="embedded"
                  value={""}
                  placeholder="Telefone"
                  options={[{ label: "Telefone", value: "telefone" }]}
                  onChange={() => {}}
                />
              }
              value={searchPhone}
              onChange={(e) => {
                setSearchPhone(onlyNumbers(e.target.value));
                setPageIndex(0);
              }}
            />
          </div>
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              gap: 20,
              minWidth: "200px",
            }}
          >
            <SelectInput
              variant="standalone"
              labelInside="Status"
              options={[
                { label: "Todos", value: "todos" },
                ...Object.entries(portfolioItemStatusMap).map(
                  ([key, conf]) => ({
                    label: conf.label,
                    value: key,
                  })
                ),
              ]}
              value={statusFilter}
              onChange={(val) => {
                setStatusFilter(val as any);
                setPageIndex(0);
              }}
              disabled={tab === "concluidas"}
            />
          </div>
          <div
            style={{
              display: "flex",
              flexDirection: "row",
            }}
          >
            <SwitchWrapper>
              <Switch
                checked={onlyInteraction}
                onChange={(checked) => {
                  setOnlyInteraction(checked);
                  setPageIndex(0);
                }}
              />
              <p>Apenas com interação</p>
            </SwitchWrapper>
            <SwitchWrapper>
              <Switch
                checked={waitingBusinessUserResponse}
                onChange={(checked) => {
                  setWaitingBusinessUserResponse(checked);
                  setPageIndex(0);
                }}
              />
              <p>Aguardando atendimento</p>
            </SwitchWrapper>
          </div>
        </FiltersWrapper>

        <NegotiationsTableWrapper>
          <Table
            totalCount={filteredTotal}
            pageIndex={pageIndex}
            pageSize={pageSize}
            isLoading={loading}
            onPageChange={(newPage, newSize) => {
              setPageIndex(newPage);
              setPageSize(newSize);
            }}
            columns={[
              {
                header: "Telefone",
                accessorKey: "phoneNumber",
                cell: (ctx) => formatPhone(ctx.row.original.phoneNumber || ""),
              },
              {
                header: "Última mensagem recebida",
                accessorKey: "lastInteraction",
                cell: (ctx) => {
                  const value = ctx.row.original.lastInteraction;
                  return value ? formatDateDefault(value) : "Não há interação";
                },
              },
              {
                header: "Status",
                accessorKey: "currentStatus",
                cell: (ctx) => {
                  const cfg =
                    portfolioItemStatusMap[
                      ctx.row.original.currentStatus as PortfolioItemStatus
                    ];
                  return (
                    <StatusTag
                      label={cfg.label}
                      variant={cfg.variant}
                      icon={cfg.icon}
                      tooltip={cfg.tooltip}
                      size="medium"
                    />
                  );
                },
              },
              {
                header: "Portfólio",
                accessorKey: "portfolioName",
                cell: (ctx) => {
                  const { portfolioId, portfolioName } = ctx.row.original;
                  return (
                    <Link href={`/portfolio/${portfolioId}`} legacyBehavior>
                      <a
                        style={{
                          color: theme.colors.link,
                          textDecoration: "underline",
                        }}
                      >
                        {portfolioName}
                      </a>
                    </Link>
                  );
                },
              },
              {
                header: "Chat",
                accessorKey: "buttonChat",
                cell: (ctx) => {
                  const row = ctx.row.original;
                  return (
                    <ExternalLink
                      size={24}
                      color={theme.colors.link}
                      style={{ cursor: "pointer", marginLeft: 8 }}
                      onClick={() => handleOpenChat(row)}
                    />
                  );
                },
              },
            ]}
            data={items}
          />
        </NegotiationsTableWrapper>
      </Container>
    </>
  );
}
