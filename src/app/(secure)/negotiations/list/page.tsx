/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, useEffect, useMemo, useRef } from "react";
import { Container } from "@/components/Container";
import { FiMessageCircle, FiSearch } from "react-icons/fi";
import Header from "@/components/Header";
import { styled } from "styled-components";
import { theme } from "@/styles/theme";
import TextInput from "@/components/Form/Input";
import SelectInput from "@/components/Form/SelectInput";
import { portfolioItemStatusMap } from "@/constants/portfolioItemStatusMap";
import { Switch } from "antd";
import NegotiationsBoard from "@/components/NegotiationsBoard";
import { onlyNumbers } from "@/utils/formatPhone";
import { PortfolioItemStatus } from "@/types/portfolio";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import EmptyState from "@/components/EmptyState";

const FiltersWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
  width: 100%;
`;

const SwitchWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  min-width: 180px;
  padding: 0 10px;

  p {
    font-size: 14px;
    font-weight: 500;
    color: ${theme.colors.neutral900};
    margin: 0;
  }
`;

const PageContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

export default function NegotiationsListPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const source = searchParams.get("source");

  const debounceRef = useRef<number | undefined>(undefined);

  const scheduleUrl = (updates: Record<string, any>, delay = 0) => {
    if (debounceRef.current) window.clearTimeout(debounceRef.current);
    if (delay > 0) {
      debounceRef.current = window.setTimeout(() => updateUrl(updates), delay);
    } else {
      updateUrl(updates);
    }
  };

  const [phone, setPhone] = useState(searchParams.get("phone") || "");
  const [status, setStatus] = useState<"todos" | PortfolioItemStatus>(
    (searchParams.get("status") as any) || "todos"
  );
  const [onlyInteraction, setOnlyInteraction] = useState(
    searchParams.get("onlyInteraction") === "true"
  );
  const [onlyFirstMessageSent, setOnlyFirstMessageSent] = useState(
    searchParams.get("onlyFirstMessageSent") === "true"
  );
  const pathname = usePathname();
  const [waitingBusinessUserResponse, setWaitingBusinessUserResponse] =
    useState(searchParams.get("waitingBusinessUserResponse") === "true");
  const [page, setPage] = useState(Number(searchParams.get("page")) || 1);
  const [negotiationId, setNegotiationId] = useState(
    searchParams.get("negotiationId") || ""
  );
  const [searchType, setSearchType] = useState<"telefone" | "nome">("telefone");
  const [searchValue, setSearchValue] = useState("");
  function updateUrl(params: Record<string, any>) {
    const current = new URLSearchParams(window.location.search);

    for (const [key, value] of Object.entries(params)) {
      const shouldDelete =
        value === undefined ||
        value === false ||
        (typeof value === "string" && value.trim() === "") ||
        (typeof value === "number" && Number.isNaN(value));

      if (shouldDelete) current.delete(key);
      else current.set(key, String(value));
    }

    const qs = current.toString();
    router.replace(`${pathname}${qs ? `?${qs}` : ""}`, { scroll: false });
  }

  const searchOptions = [
    { label: "Telefone", value: "telefone" },
    { label: "Nome", value: "nome" },
  ];

  const filtersMemo = useMemo(
    () => ({
      phone: searchType === "telefone" ? searchValue : "",
      name: searchType === "nome" ? searchValue : "",
      status,
      onlyInteraction,
      waitingBusinessUserResponse,
      onlyFirstMessageSent,
    }),
    [
      searchType,
      searchValue,
      status,
      onlyInteraction,
      waitingBusinessUserResponse,
      onlyFirstMessageSent,
    ]
  );

  useEffect(() => {
    const urlPage = Number(searchParams.get("page")) || 1;
    if (page !== urlPage) {
      setPage(urlPage);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]);

  useEffect(() => {
    const urlPhone = searchParams.get("phone") || "";
    if (urlPhone !== phone) setPhone(urlPhone);
  }, [searchParams]); // eslint-disable-line

  useEffect(() => {
    setSearchValue("");
    scheduleUrl({
      phone: searchType === "telefone" ? "" : undefined,
      name: searchType === "nome" ? "" : undefined,
      page: 1,
      negotiationId: undefined,
    });
  }, [searchType]);

  const handleNegotiationChange = (id: string) => {
    setNegotiationId(id);
    updateUrl({ negotiationId: id });
  };

  const handlePageChange = (newPage: number) => {
    updateUrl({ page: newPage });
  };

  const handleFilterPatch = (patch: Record<string, any>) => {
    if (!Object.prototype.hasOwnProperty.call(patch, "onlyFirstMessageSent")) {
      updateUrl({
        negotiationId: undefined,
        page: 1,
        onlyFirstMessageSent: false,
        ...patch,
      });
    } else {
      updateUrl({ negotiationId: undefined, page: 1, ...patch });
    }
  };

  return (
    <>
      <Header
        title="Negociações"
        icon={<FiMessageCircle size={24} />}
        isSidebarOpen={true}
      />
      <Container>
        <PageContent>
          <FiltersWrapper>
            <div style={{ flex: "1 1 0", width: "100%" }}>
              <TextInput
                placeholder={
                  searchType === "telefone"
                    ? "Pesquisar por telefone"
                    : "Pesquisar por nome"
                }
                iconLeft={
                  <FiSearch size={24} color={theme.colors.labelInput} />
                }
                style={{ width: "100%" }}
                selectLeft={
                  <SelectInput
                    variant="embedded"
                    value={searchType}
                    placeholder={
                      searchType === "telefone" ? "Telefone" : "Nome"
                    }
                    options={searchOptions}
                    onChange={(val) =>
                      setSearchType(val as "telefone" | "nome")
                    }
                  />
                }
                value={searchValue}
                onChange={(e) => {
                  const value =
                    searchType === "telefone"
                      ? onlyNumbers(e.target.value)
                      : e.target.value;
                  setSearchValue(value);
                  scheduleUrl({
                    [searchType === "telefone" ? "phone" : "name"]: value,
                    [searchType === "telefone" ? "name" : "phone"]: undefined,
                    page: 1,
                    negotiationId: undefined,
                  });
                }}
              />
            </div>

            <div
              style={{
                display: "flex",
                flexDirection: "row",
                gap: 20,
                minWidth: "200px",
              }}
            >
              <SelectInput
                variant="standalone"
                labelInside="Status"
                options={[
                  { label: "Todos", value: "todos" },
                  ...Object.entries(portfolioItemStatusMap).map(
                    ([key, conf]) => ({
                      label: conf.label,
                      value: key,
                    })
                  ),
                ]}
                value={status}
                onChange={(val) => {
                  setStatus(val as any);
                  handleFilterPatch({ status: val });
                }}
              />
            </div>

            <div style={{ display: "flex", flexDirection: "row" }}>
              <SwitchWrapper>
                <Switch
                  checked={onlyFirstMessageSent}
                  onChange={(val) => {
                    setOnlyFirstMessageSent(val);
                    handleFilterPatch({ onlyFirstMessageSent: val });
                  }}
                />
                <p>Cobranças iniciadas</p>
              </SwitchWrapper>
              <SwitchWrapper>
                <Switch
                  checked={onlyInteraction}
                  onChange={(val) => {
                    setOnlyInteraction(val);
                    handleFilterPatch({ onlyInteraction: val });
                  }}
                />
                <p>Apenas com interação</p>
              </SwitchWrapper>

              <SwitchWrapper>
                <Switch
                  checked={waitingBusinessUserResponse}
                  onChange={(val) => {
                    setWaitingBusinessUserResponse(val);
                    updateUrl({ waitingBusinessUserResponse: val, page: 1 });
                  }}
                />
                <p>Aguardando atendimento</p>
              </SwitchWrapper>
            </div>
          </FiltersWrapper>

          <NegotiationsBoard
            filters={filtersMemo}
            page={page}
            source={source || ""}
            setPage={handlePageChange}
            negotiationId={negotiationId}
            setNegotiationId={handleNegotiationChange}
            renderEmpty={
              <div style={{ padding: 16 }}>
                <EmptyState
                  title="Não encontramos nenhuma negociação com esses filtros"
                  subtitle="Tente alterar os filtros ou procurar por outra negociação."
                />
              </div>
            }
          />
        </PageContent>
      </Container>
    </>
  );
}
