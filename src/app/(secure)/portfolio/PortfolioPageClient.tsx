/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { Container } from "@/components/Container";
import TopSection from "@/components/ImportsPage/TopSection";
import PortfolioFilters from "@/components/PortfoliosPage/PortfolioFilters";
import { usePortfolioTabs } from "@/hooks/usePortfolioTabs";
import { usePortfolioFilters } from "@/hooks/usePortfolioFilters";
import PortfolioCardList from "@/components/PortfoliosPage/PortfolioCardList";
import Header from "@/components/Header";
import { Wallet } from "lucide-react";
import { useMemo, useState, useEffect } from "react";
import { Pagination } from "antd";
import { PaginationContainer } from "@/components/Table/styles";
import { usePortfoliosWithQueueFlag } from "@/hooks/usePortfoliosWithQueueFlag";
export default function PortfolioPage() {
  const [page, setPage] = useState(1);
  const pageSize = 10;

  const { data: portfolios, loading } = usePortfoliosWithQueueFlag();

  const {
    filters,
    setSelectedExecutionStatus,
    setSelectedServiceQueue,
    setSearchTerm,
    filteredPortfolios,
  } = usePortfolioFilters(portfolios);

  const { currentTab, setCurrentTab, tabCounts, filterByTab } =
    usePortfolioTabs(filteredPortfolios);

  const finalList = useMemo(
    () => filterByTab(filteredPortfolios),
    [filteredPortfolios, filterByTab]
  );

  useEffect(() => {
    setPage(1);
  }, [filters.executionStatus, filters.serviceQueue, currentTab]);

  const paginatedList = useMemo(() => {
    const start = (page - 1) * pageSize;
    return finalList.slice(start, start + pageSize);
  }, [finalList, page, pageSize]);

  return (
    <>
      <Header
        title="Portfólios"
        icon={<Wallet size={24} />}
        isSidebarOpen={true}
      />
      <Container>
        <TopSection
          description="Visualize e gerencie todas as negociações realizadas pela nossa IA de negociação. Acompanhe em tempo real o valor recuperado das dívidas, negociações em andamento, fila de atendimento e o número de portfólios importados. Tudo em um só lugar, facilitando a gestão estratégica e o monitoramento completo da performance da sua operação."
          tabs={{
            items: [
              {
                label: "Ativas",
                value: "ativas",
                count: tabCounts.ativas,
              },
              {
                label: "Concluídas",
                value: "concluidas",
                count: tabCounts.concluidas,
              },
              {
                label: "Arquivadas",
                value: "arquivadas",
                count: tabCounts.arquivadas,
              },
            ],
            value: currentTab,
            onChange: setCurrentTab,
          }}
        />
        <PortfolioFilters
          searchTerm={filters.searchTerm}
          setSearchTerm={setSearchTerm}
          selectedEmbedded="all"
          setSelectedEmbedded={() => {}}
          selectedExecutionStatus={filters.executionStatus}
          setSelectedExecutionStatus={setSelectedExecutionStatus}
          selectedServiceQueue={filters.serviceQueue}
          setSelectedServiceQueue={setSelectedServiceQueue}
        />

        <PortfolioCardList
          loading={loading}
          portfolios={portfolios}
          filteredPortfolios={paginatedList}
        />
        {finalList.length >= pageSize && finalList.length > 0 && (
          <PaginationContainer
            style={{ backgroundColor: "transparent", padding: "26px 0" }}
          >
            <Pagination
              current={page}
              pageSize={pageSize}
              total={finalList.length}
              showSizeChanger={false}
              onChange={(p) => setPage(p)}
            />
          </PaginationContainer>
        )}
      </Container>
    </>
  );
}
