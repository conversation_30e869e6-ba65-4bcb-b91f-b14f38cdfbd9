/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { useState, useEffect, useMemo, useCallback } from "react";
import { useParams, useSearchParams, useRouter } from "next/navigation";
import Header from "@/components/Header";
import { Wallet } from "lucide-react";
import { useBreadcrumbs } from "@/hooks/useBreadcrumb";
import PortfolioDetailSkeleton from "@/components/PortfolioDetailPage/PortfolioDetailSkeleton";
import { Container } from "@/components/Container";
import {
  DataContainer,
  ContentGrid,
  RightRail,
} from "@/components/PortfolioDetailPage/styles";
import { PortfolioHeaderSection } from "@/components/PortfolioDetailPage/PortfolioHeaderSection";
import { PortfolioCardsSection } from "@/components/PortfolioDetailPage/PortfolioCardsSection";
import { PortfolioServiceQueue } from "@/components/PortfolioDetailPage/PortfolioServiceQueue";
import {
  getPortfolio,
  getAllPortfolioItems,
  getPortfolioAverageTicket,
} from "@/services/portfolioService";
import { useDebounce } from "@/hooks/useDebounce";
import {
  PortfolioItemStatus,
  Portfolio,
  Paginated,
  IPortfolioItem,
} from "@/types/portfolio";
import { PortfolioFilters } from "@/components/PortfolioDetailPage/PortfolioFilters";
import { PortfolioTable } from "@/components/PortfolioDetailPage/PortfolioTable";
import { formatDateDefault } from "@/utils/formatDate";
import { getPortfolioFirstMessageMetrics } from "@/services/metricsService";
import { useQueueName } from "@/hooks/useQueueName";

export default function PortfolioDetailPage() {
  const crumbs = useBreadcrumbs();
  const { id } = useParams<{ id: string }>();
  const [portfolio, setPortfolio] = useState<Portfolio | null>(null);
  const [portfolioItems, setPortfolioItems] = useState<
    Paginated<IPortfolioItem>
  >({
    items: [],
    total: 0,
    limit: 10,
    page: 1,
    totalPages: 1,
  });
  const [loadingPortfolio, setLoadingPortfolio] = useState(true);
  const [loadingTable, setLoadingTable] = useState(false);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalNegociacoes, setTotalNegociacoes] = useState<number>(0);
  const [totalConcluidas, setTotalConcluidas] = useState(0);
  const [statusFilter, setStatusFilter] = useState<
    PortfolioItemStatus | "todos"
  >("todos");
  const [phoneFilter, setPhoneFilter] = useState("");
  const debouncedPhoneFilter = useDebounce(phoneFilter, 1800);
  const [tab, setTab] = useState<"negociacoes" | "concluidas">("negociacoes");
  const [onlyInteraction, setOnlyInteraction] = useState(false);

  const searchParams = useSearchParams();
  const router = useRouter();
  const [dealValue, setDealValue] = useState<string>("R$ 0,00");
  const [loadingCards, setLoadingCards] = useState(false);
  const [dateFilter, setDateFilter] = useState("all");
  const [customRange, setCustomRange] = useState<
    [string, string] | undefined
  >();

  const [portfolioAverageTicket, setPortfolioAverageTicket] =
    useState<any>(null);
  const [portfolioFirstMessageMetrics, setPortfolioFirstMessageMetrics] =
    useState<any>(null);
  const { items: queueItems, isLoading: queueLoading } = useQueueName();

  function goToNegotiationsList(negotiationId: string, phoneNumber?: string) {
    const qs = new URLSearchParams();
    qs.set("negotiationId", negotiationId);
    qs.set("source", "portfolio");

    if (phoneNumber) qs.set("phone", phoneNumber);
    if (statusFilter && statusFilter !== "todos")
      qs.set("status", statusFilter);
    if (onlyInteraction) qs.set("onlyInteraction", "true");

    router.push(`/negotiations/list?${qs.toString()}`);
  }

  function getDateRangeValues(
    filter: string,
    customRange?: [string, string]
  ): {
    startDate?: string;
    endDate?: string;
  } {
    const now = new Date();
    if (filter === "today") {
      const start = new Date(now.setHours(0, 0, 0, 0));
      const end = new Date(now.setHours(23, 59, 59, 999));
      return { startDate: start.toISOString(), endDate: end.toISOString() };
    }
    if (filter === "7") {
      const start = new Date();
      start.setDate(start.getDate() - 7);
      return {
        startDate: start.toISOString(),
        endDate: new Date().toISOString(),
      };
    }
    if (filter === "15") {
      const start = new Date();
      start.setDate(start.getDate() - 15);
      return {
        startDate: start.toISOString(),
        endDate: new Date().toISOString(),
      };
    }
    if (filter === "30") {
      const start = new Date();
      start.setDate(start.getDate() - 30);
      return {
        startDate: start.toISOString(),
        endDate: new Date().toISOString(),
      };
    }
    if (filter === "all") {
      return { startDate: undefined, endDate: undefined };
    }
    if (
      filter === "custom" &&
      customRange &&
      customRange[0] &&
      customRange[1]
    ) {
      return { startDate: customRange[0], endDate: customRange[1] };
    }
    return {};
  }

  const onlyDate = (dateStr?: string) => (dateStr ? dateStr.split("T")[0] : "");
  const { startDate, endDate } = useMemo(
    () => getDateRangeValues(dateFilter, customRange),
    [dateFilter, customRange]
  );

  const firstMessageDateRange = useMemo(() => {
    if (dateFilter === "all") {
      const end = new Date();
      const start = new Date();
      start.setFullYear(end.getFullYear() - 1);
      return {
        startDate: start.toISOString(),
        endDate: end.toISOString(),
      };
    }
    return { startDate, endDate };
  }, [dateFilter, startDate, endDate]);

  useEffect(() => {
    if (!id) return;
    setLoadingPortfolio(true);
    (async () => {
      try {
        const fetchedPortfolio = await getPortfolio(id);
        setPortfolio(fetchedPortfolio);
        const [totalRes, concluidasRes] = await Promise.all([
          getAllPortfolioItems({ portfolioId: id }, { page: 1, limit: 1 }, [
            fetchedPortfolio,
          ]),
          getAllPortfolioItems(
            { portfolioId: id, currentStatus: PortfolioItemStatus.SUCCEED },
            { page: 1, limit: 1 },
            [fetchedPortfolio]
          ),
        ]);
        setTotalNegociacoes(totalRes.total);
        setTotalConcluidas(concluidasRes.total);
      } finally {
        setLoadingPortfolio(false);
      }
    })();
  }, [id]);

  useEffect(() => {
    if (!id) return;
    setLoadingCards(true);

    getPortfolio(id, startDate, endDate)
      .then((portfolio) => setDealValue(portfolio.dealValue || "R$ 0,00"))
      .finally(() => setLoadingCards(false));
  }, [id, dateFilter, customRange]);

  useEffect(() => {
    if (!id) return;
    setLoadingCards(true);
    getPortfolioAverageTicket(id, startDate || "", endDate || "")
      .then((data) => setPortfolioAverageTicket(data))
      .finally(() => setLoadingCards(false));
  }, [id, startDate, endDate]);

  useEffect(() => {
    (async () => {
      getPortfolioFirstMessageMetrics(
        id,
        onlyDate(firstMessageDateRange.startDate),
        onlyDate(firstMessageDateRange.endDate)
      )
        .then((data) => setPortfolioFirstMessageMetrics(data))
        .finally(() => setLoadingCards(false));
    })();
  }, [id, firstMessageDateRange.startDate, firstMessageDateRange.endDate]);

  useEffect(() => {
    if (!id || loadingPortfolio || !portfolio) return;
    setLoadingTable(true);
    (async () => {
      try {
        const statusBody =
          statusFilter !== "todos"
            ? statusFilter
            : tab === "negociacoes"
              ? undefined
              : PortfolioItemStatus.SUCCEED;
        const filter: any = {
          portfolioId: id,
          phoneNumber: debouncedPhoneFilter || undefined,
          currentStatus: statusBody,
        };
        if (onlyInteraction) {
          filter.lastInteraction = { not: null };
        }
        const items = await getAllPortfolioItems(filter, { page, limit }, [
          portfolio,
        ]);
        setPortfolioItems(items);
      } finally {
        setLoadingTable(false);
      }
    })();
  }, [
    id,
    portfolio,
    tab,
    statusFilter,
    debouncedPhoneFilter,
    page,
    limit,
    loadingPortfolio,
    onlyInteraction,
  ]);

  const updateUrl = useCallback(
    (params: Record<string, string | number | undefined>) => {
      const current = new URLSearchParams(window.location.search);
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== "") {
          current.set(key, String(value));
        } else {
          current.delete(key);
        }
      });
      router.replace(`?${current.toString()}`);
    },
    [router]
  );

  useEffect(() => {
    updateUrl({
      page,
      limit,
      status: statusFilter,
      phone: phoneFilter,
      tab,
      onlyInteraction: onlyInteraction ? "true" : undefined,
    });
  }, [page, limit, statusFilter, phoneFilter, tab, onlyInteraction, updateUrl]);

  useEffect(() => {
    setPage(Number(searchParams.get("page")) || 1);
    setLimit(Number(searchParams.get("limit")) || 10);
    setStatusFilter(
      (searchParams.get("status") as PortfolioItemStatus | "todos") || "todos"
    );
    setPhoneFilter(searchParams.get("phone") || "");
    setTab(
      (searchParams.get("tab") as "negociacoes" | "concluidas") || "negociacoes"
    );
    setOnlyInteraction(searchParams.get("onlyInteraction") === "true");
  }, [searchParams]);

  if (loadingPortfolio) {
    return <PortfolioDetailSkeleton />;
  }
  if (!portfolio) {
    return <div style={{ padding: 40 }}>Portfólio não encontrado.</div>;
  }

  const hasRightRail = !queueLoading && queueItems.length > 0;

  return (
    <>
      <Header
        title={portfolio.name}
        icon={<Wallet size={24} />}
        isSidebarOpen={true}
        portfolioStatus={portfolio.executionStatus}
        portfolioCreatedAt={formatDateDefault(portfolio.createdAt)}
        portfolioUpdatedAt={formatDateDefault(portfolio.updatedAt)}
      />
      <Container>
        <ContentGrid $hasRightRail={hasRightRail} style={{ gap: 48 }}>
          <DataContainer>
            <PortfolioHeaderSection
              crumbs={crumbs}
              portfolio={portfolio}
              onPortfolioChange={setPortfolio}
              dateFilter={dateFilter}
              setDateFilter={setDateFilter}
              setCustomRange={setCustomRange}
            />
            <PortfolioCardsSection
              dealValue={dealValue}
              loadingCards={loadingCards}
              portfolioAverageTicket={portfolioAverageTicket}
              portfolioFirstMessageMetrics={portfolioFirstMessageMetrics}
            />
            <PortfolioFilters
              tab={tab}
              setTab={setTab}
              totalNegociacoes={totalNegociacoes}
              totalConcluidas={totalConcluidas}
              phoneFilter={phoneFilter}
              setPhoneFilter={setPhoneFilter}
              statusFilter={statusFilter}
              setStatusFilter={setStatusFilter as (v: string) => void}
              onlyInteraction={onlyInteraction}
              setOnlyInteraction={setOnlyInteraction}
            />
            <PortfolioTable
              portfolioItems={portfolioItems}
              page={page}
              limit={limit}
              loading={loadingTable}
              setPage={setPage}
              setLimit={setLimit}
              onOpenChat={(item) =>
                goToNegotiationsList(
                  item.id,
                  item.phoneNumber && item.phoneNumber.replace(/\D/g, "")
                )
              }
            />
          </DataContainer>
          {hasRightRail && (
            <RightRail>
              <PortfolioServiceQueue />
            </RightRail>
          )}
        </ContentGrid>
      </Container>
    </>
  );
}
