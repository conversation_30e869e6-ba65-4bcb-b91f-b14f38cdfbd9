// src/app/ClientLayout.tsx
"use client";

import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { usePathname } from "next/navigation";
import { NewUIProviders } from "./providers";
import Sidebar from "@/components/Sidebar";
import { MenuItemData } from "@/components/Sidebar/types";
import { ChartPie, Download, Settings } from "lucide-react";
import { Wallet } from "lucide-react";
import { MdOutlineSpaceDashboard } from "react-icons/md";
import { FiMessageCircle } from "react-icons/fi";

const LayoutWrapper = styled.div`
  display: flex;
`;

const MainContent = styled.main.withConfig({
  shouldForwardProp: (prop) => prop !== "isSidebarOpen",
})<{ isSidebarOpen: boolean }>`
  margin-left: ${({ isSidebarOpen }) => (isSidebarOpen ? "280px" : "80px")};
  transition: margin-left 0.3s ease;
  width: 100%;
`;

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [hasMounted, setHasMounted] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) return null;

  const hideSidebar = pathname === "/login";

  if (hideSidebar) {
    return <NewUIProviders>{children}</NewUIProviders>;
  }

  const portfolioId = pathname.split("/").pop();

  const menuItems: MenuItemData[] = [
    {
      id: "dashboard",
      icon: <ChartPie size={16} />,
      label: "Dashboard",
      path: "/dashboard",
    },

    {
      id: "portfolio",
      icon: <Wallet size={16} />,
      label: "Portfólios",
      path: "/portfolio",
      children: [
        {
          id: "portfolio-detail",
          label: "Detalhe do portfólio",
          path: `/portfolio/${portfolioId}`,
          icon: <MdOutlineSpaceDashboard size={16} />,
        },
      ],
    },
    {
      id: "negociacoes",
      icon: <FiMessageCircle size={16} />,
      label: "Negociações",
      path: "/negotiations/list",
    },
    {
      id: "importacoes",
      icon: <Download size={16} />,
      label: "Importações",
      path: "/imports",
    },

    {
      id: "integracoes",
      icon: <Settings size={16} />,
      label: "Integrações",
      path: "/integrations",
    },
  ];

  return (
    <LayoutWrapper>
      <Sidebar
        isOpen={isSidebarOpen}
        toggleSidebar={() => setIsSidebarOpen((prev) => !prev)}
        menuItems={menuItems}
        languages={["Português"]}
      />

      <MainContent isSidebarOpen={!hideSidebar && isSidebarOpen}>
        {children}
      </MainContent>
    </LayoutWrapper>
  );
}
