import { ReactNode } from "react";
import { ToastClient } from "@/components/Toast/ToastClient";
import StyledComponentsRegistry from "@/lib/StyledComponentsRegistry";
import { NewUIProviders } from "./providers";
import "./globals.css";
export const metadata = {
  title: "DigAí Negocia",
  description: "Automatize suas negociações e recupere o que é seu",
};

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="pt-BR">
      <head>
        <link
          rel="preload"
          href="/fonts/Switzer-Regular.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
      </head>
      <body>
        <StyledComponentsRegistry>
          <NewUIProviders>
            <ToastClient>{children}</ToastClient>
          </NewUIProviders>
        </StyledComponentsRegistry>
      </body>
    </html>
  );
}
