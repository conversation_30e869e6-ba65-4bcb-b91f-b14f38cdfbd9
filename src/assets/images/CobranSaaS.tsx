export const Cobransaas = () => {
  return (
    <svg
      width="26"
      height="26"
      viewBox="0 0 26 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="26" height="26" fill="url(#pattern0_5225_49888)" />
      <defs>
        <pattern
          id="pattern0_5225_49888"
          patternContentUnits="objectBoundingBox"
          width="1"
          height="1"
        >
          <use xlinkHref="#image0_5225_49888" transform="scale(0.005)" />
        </pattern>
        <image
          id="image0_5225_49888"
          width="200"
          height="200"
          preserveAspectRatio="none"
          xlinkHref="data:image/jpeg;base64,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"
        />
      </defs>
    </svg>
  );
};
