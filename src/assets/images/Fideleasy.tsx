import React from "react";

export const Fideleasy = () => (
  <svg
    width="46"
    height="46"
    viewBox="0 0 46 46"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="46" height="46" rx="22" fill="url(#pattern0_5225_49949)" />
    <defs>
      <pattern
        id="pattern0_5225_49949"
        patternContentUnits="objectBoundingBox"
        width="1"
        height="1"
      >
        <use xlinkHref="#image0_5225_49949" transform="scale(0.00285714)" />
      </pattern>
      <image
        id="image0_5225_49949"
        width="350"
        height="350"
        preserveAspectRatio="none"
        xlinkHref="data:image/png;base64,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"
      />
    </defs>
  </svg>
);
