export const DigaiLogo = () => {
  return (
    <svg
      width="68"
      height="32"
      viewBox="0 0 68 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_5331_27621)">
        <path
          d="M14.8729 22.9022H11.1411C10.1677 23.4378 9.00484 23.7056 7.65277 23.7056C5.54349 23.7056 3.74072 22.9692 2.24447 21.4965C0.748127 20.0237 0 18.243 0 16.1544C0 14.0656 0.748214 12.3028 2.24447 10.8657C3.74081 9.42868 5.54357 8.71016 7.65277 8.71016C8.93284 8.71016 10.0956 8.99579 11.1411 9.56704V0H14.8729V22.9023V22.9022ZM7.65268 12.2983C6.57107 12.2983 5.65162 12.6732 4.89442 13.4229C4.13722 14.1727 3.7587 15.0831 3.7587 16.1543C3.7587 17.2075 4.13722 18.1135 4.89442 18.8723C5.65162 19.6309 6.57098 20.0102 7.65268 20.0102C8.77042 20.0102 9.64928 19.622 10.2894 18.8455C10.9293 18.0689 11.2492 17.1719 11.2492 16.1543C11.2492 15.1368 10.9293 14.2397 10.2894 13.4631C9.64928 12.6865 8.77042 12.2983 7.65268 12.2983Z"
          fill="#111C9D"
        />
        <path
          d="M21.6768 21.9007H17.918V8.51172H21.6768V21.9007Z"
          fill="#111C9D"
        />
        <path
          d="M35.2238 23.5646V23.0559C34.106 23.6629 32.9433 23.9664 31.7355 23.9664C29.6442 23.9664 27.8549 23.2389 26.3676 21.784C24.8803 20.329 24.1367 18.5662 24.1367 16.4953C24.1367 14.4066 24.8804 12.6305 26.3676 11.1665C27.8549 9.70271 29.6442 8.9707 31.7355 8.9707C33.0875 8.9707 34.2684 9.23853 35.2779 9.77409H38.9555V23.5646C38.9555 25.8674 38.1308 27.849 36.4813 29.5093C34.8317 31.1694 32.8441 31.9995 30.5185 31.9995C28.1749 31.9995 26.1828 31.1873 24.5422 29.5628L27.1653 26.9386C28.0487 27.867 29.1664 28.3311 30.5185 28.3311C31.7985 28.3311 32.9027 27.858 33.8311 26.9118C34.7596 25.9657 35.2238 24.8499 35.2238 23.5646V23.5646ZM31.7355 12.6662C30.6718 12.6662 29.7614 13.041 29.0042 13.7908C28.247 14.5406 27.8685 15.4421 27.8685 16.4953C27.8685 17.5664 28.247 18.4769 29.0042 19.2267C29.7614 19.9765 30.6717 20.3513 31.7355 20.3513C32.8531 20.3513 33.7321 19.9631 34.3721 19.1865C35.0121 18.4099 35.332 17.5129 35.332 16.4953C35.332 15.4778 35.0121 14.5852 34.3721 13.8176C33.7321 13.0499 32.8531 12.6662 31.7355 12.6662Z"
          fill="#111C9D"
        />
        <path
          d="M43.0842 23.3516C44.4106 24.9473 46.0675 26.2101 47.9162 27.0745C49.7571 27.9352 51.7944 28.3974 53.8913 28.3974C55.9879 28.3974 58.0252 27.9352 59.8663 27.0745C61.7149 26.2101 63.3718 24.9473 64.6982 23.3516L67.2003 25.3893C65.5609 27.3618 63.5211 28.9185 61.2506 29.98C58.9719 31.0455 56.4626 31.6174 53.8913 31.6174C51.3203 31.6174 48.8106 31.0455 46.5318 29.98C44.2613 28.9186 42.2215 27.3618 40.582 25.3893L43.0842 23.3516Z"
          fill="#111C9D"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M17.918 25.0879V28.9438H21.6768V25.0879H17.918Z"
          fill="#56C3BD"
        />
        <path
          d="M57.1709 20.6529H53.4391C52.4657 21.1885 51.3119 21.4562 49.9778 21.4562C47.8866 21.4562 46.1019 20.7243 44.6237 19.2605C43.1454 17.7966 42.4062 16.0294 42.4062 13.9586C42.4062 11.8878 43.1454 10.1205 44.6237 8.65669C46.1019 7.19286 47.8866 6.46094 49.9778 6.46094C51.3119 6.46094 52.4656 6.72876 53.4391 7.26424H57.1709V20.6528V20.6529ZM49.9778 10.1296C48.9142 10.1296 48.0083 10.5044 47.2602 11.2541C46.512 12.0039 46.138 12.9054 46.138 13.9587C46.138 16.0765 47.8313 17.7878 49.9778 17.7878C51.0775 17.7878 51.9473 17.3996 52.5873 16.623C53.2273 15.8465 53.5472 14.9584 53.5472 13.9587C53.5472 12.959 53.2273 12.0709 52.5873 11.2943C51.9473 10.5178 51.0775 10.1296 49.9778 10.1296Z"
          fill="#56C3BD"
        />
        <path
          d="M64.0596 20.6522H60.3008V7.26367H64.0596V20.6522Z"
          fill="#56C3BD"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M56.5625 0V3.85594H60.3213V0H56.5625Z"
          fill="#111C9D"
        />
      </g>
      <defs>
        <clipPath id="clip0_5331_27621">
          <rect width="67.2" height="32" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
