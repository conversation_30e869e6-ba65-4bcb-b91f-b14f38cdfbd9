import { BreadcrumbProps } from "./types";
import { Wrapper, CrumbLink, CrumbText, Separator } from "./styles";
import { ChevronRight } from "lucide-react";

const Breadcrumb = ({ items, padding, margin }: BreadcrumbProps) => {
  return (
    <Wrapper aria-label="Breadcrumb" padding={padding} margin={margin}>
      {items.map((crumb, idx) => {
        const isLast = idx === items.length - 1;

        return (
          <span key={crumb.label}>
            {isLast ? (
              <CrumbText>{crumb.label}</CrumbText>
            ) : crumb.href ? (
              <CrumbLink href={crumb.href}>{crumb.label}</CrumbLink>
            ) : (
              <CrumbLink as="button" onClick={crumb.onClick}>
                {crumb.label}
              </CrumbLink>
            )}

            {!isLast && (
              <Separator>
                <ChevronRight size={10} />
              </Separator>
            )}
          </span>
        );
      })}
    </Wrapper>
  );
};

export default Breadcrumb;
