import styled from "styled-components";

export const Wrapper = styled.nav<{ padding?: string; margin?: string }>`
  display: flex;
  gap: 4px;
  align-items: baseline;
  padding: ${({ padding = "10px" }) => padding};
  margin: ${({ margin = "20px" }) => margin};

  span {
    display: flex;
    gap: 4px;
  }
`;

export const CrumbLink = styled.a`
  font-size: 14px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.primary};
  text-decoration: none;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
`;

export const CrumbText = styled.span`
  font-size: 14px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.disabledText};
`;

export const Separator = styled.span`
  display: flex;
  align-items: center;
  color: ${({ theme }) => theme.colors.disabledText};
`;
