import { ButtonWrapper } from "./styles";
import { ButtonProps } from "./types";
import { LoadingOutlined } from "@ant-design/icons";
import { Spin } from "antd";

const loadingIcon = <LoadingOutlined style={{ fontSize: 16 }} spin />;

const Button = ({
  label,
  icon,
  iconPosition = "left",
  variant = "primary",
  size = "md",
  loading,
  className,
  ...rest
}: ButtonProps) => {
  return (
    <ButtonWrapper
      variant={variant}
      size={size}
      className={className}
      disabled={loading || variant === "disabled" || rest.disabled}
      {...rest}
    >
      {loading ? (
        <Spin indicator={loadingIcon} />
      ) : (
        <>
          {icon && iconPosition === "left" && icon}
          {label}
          {icon && iconPosition === "right" && icon}
        </>
      )}
    </ButtonWrapper>
  );
};

export default Button;
