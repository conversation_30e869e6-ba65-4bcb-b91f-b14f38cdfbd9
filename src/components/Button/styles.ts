import styled, { css } from "styled-components";
import { ButtonProps } from "./types";

const variantStyles = {
  primary: css`
    background-color: #111c9d;
    color: white;
    border: none;
    width: fit-content;

    &:hover {
      background-color: rgb(15, 24, 126);
    }
  `,
  secondary: css`
    background-color: white;
    color: ${({ theme }) => theme.colors.primary};
    border: 1px solid ${({ theme }) => theme.colors.primary};
    width: fit-content;

    &:hover {
      background-color: #f5f5ff;
    }
  `,
  outlined: css`
    background-color: transparent;
    color: ${({ theme }) => theme.colors.primary};
    border: 1px solid ${({ theme }) => theme.colors.primary};
    width: fit-content;

    &:hover {
      background-color: #f5f5ff;
    }
  `,
  ghost: css`
    background-color: transparent;
    color: ${({ theme }) => theme.colors.labelInput};
    border: none;
    width: fit-content;
  `,
  gray: css`
    background-color: ${({ theme }) => theme.colors.neutral500};
    color: ${({ theme }) => theme.colors.neutral900};
    border: none;
    border-radius: 8px;
    width: fit-content;
  `,
  disabled: css`
    background-color: ${({ theme }) => theme.colors.disabled};
    color: ${({ theme }) => theme.colors.disabledText};
    border: 0.5px solid ${({ theme }) => theme.colors.neutral400};
    cursor: not-allowed;
    width: fit-content;
  `,
  neutral: css`
    background-color: ${({ theme }) => theme.colors.background};
    color: ${({ theme }) => theme.colors.neutral900};
    border: 0.5px solid ${({ theme }) => theme.colors.neutral400};
    width: fit-content;
    box-shadow:
      0px 1px 2px 0px rgba(0, 0, 0, 0.03),
      0px 2px 3px 0px rgba(0, 0, 0, 0.04),
      0px 3px 6px 0px rgba(0, 0, 0, 0.03);
  `,
};

const sizeStyles = {
  xsm: css`
    height: 28px;
    font-size: 14px;
    padding: 5px 8px;
  `,
  sm: css`
    height: 32px;
    font-size: 14px;
    padding: 6px 12px;
  `,
  xs: css`
    height: 34px;
    font-size: 14px;
    padding: 8px 16px;
  `,
  md: css`
    height: 37px;
    font-size: 16px;
    padding: 8px 16px;
  `,
  lg: css`
    height: 58px;
    font-size: 16px;
    padding: 8px 16px;
    font-weight: 600;
  `,
};

export const ButtonWrapper = styled.button.withConfig({
  shouldForwardProp: (prop) => prop !== "minWidth",
})<{
  variant: ButtonProps["variant"];
  size: ButtonProps["size"];
  fullWidth?: boolean;
  minWidth?: number;
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
  border-radius: 4px;
  ${({ minWidth }) =>
    minWidth &&
    css`
      min-width: ${minWidth}px;
    `}
  transition: background-color 0.2s ease;

  ${({ fullWidth }) =>
    fullWidth &&
    css`
      width: 100%;
    `}
  ${({ variant = "primary" }) => variantStyles[variant]}
  ${({ size = "md" }) => sizeStyles[size]}
`;
