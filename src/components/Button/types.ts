import { ButtonHTMLAttributes, ReactNode } from "react";

export type ButtonVariant =
  | "primary"
  | "secondary"
  | "outlined"
  | "ghost"
  | "gray"
  | "disabled"
  | "neutral";
export type ButtonSize = "xsm" | "sm" | "xs" | "md" | "lg";

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  label: string;
  variant?: ButtonVariant;
  size?: ButtonSize;
  icon?: ReactNode;
  iconPosition?: "left" | "right";
  className?: string;
  minWidth?: number;
  loading?: boolean;
}
