import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Header,
  Title,
  Subtitle,
  CardIcon,
  CardBody,
  Actions,
} from "./styles";
import React from "react";
import { Tooltip } from "antd";
interface CardSearchProps {
  header: {
    title: React.ReactNode | string;
    subtitle?: string;
    icon?: React.ReactNode;
    image?: React.ReactNode;
    statusLabel?: string;
    actions?: React.ReactNode;
  };
  sections?: React.ReactNode[];
  footer?: React.ReactNode;
  maxHeight?: string;
  headerPadding?: string;
  alignItems?: string;
  maxWidth?: string;
}

function CardSearch({
  header,
  sections,
  footer,
  maxHeight,
  headerPadding,
  alignItems,
  maxWidth,
}: CardSearchProps) {
  return (
    <CardWrapper maxHeight={maxHeight} maxWidth={maxWidth}>
      {header.image ? (
        <CardIcon>{header.image}</CardIcon>
      ) : (
        header.icon && (
          <Tooltip title={header.statusLabel} placement="bottom">
            <CardIcon>{header.icon}</CardIcon>
          </Tooltip>
        )
      )}

      <CardHeader padding={headerPadding}>
        <div>
          <Title>{header.title}</Title>
          {header.subtitle && <Subtitle>{header.subtitle}</Subtitle>}
        </div>
        {header.actions && <Actions>{header.actions}</Actions>}
      </CardHeader>
      <CardBody alignItems={alignItems}>
        {sections?.map((section, index) => (
          <React.Fragment key={index}>{section}</React.Fragment>
        ))}
      </CardBody>
      {footer}
    </CardWrapper>
  );
}

export default CardSearch;
