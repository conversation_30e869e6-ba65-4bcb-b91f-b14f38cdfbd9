import styled, { keyframes } from "styled-components";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Subtitle,
  CardIcon,
  CardBody,
} from "./styles";

const shimmer = keyframes`
  0% { background-position: -200px 0 }
  100% { background-position: calc(200px + 100%) 0 }
`;

const Placeholder = styled.div<{
  width?: string;
  height?: string;
  radius?: string;
}>`
  background: linear-gradient(90deg, #f0f0f0 25%, #e4e4e4 37%, #f0f0f0 63%);
  background-size: 400% 100%;
  animation: ${shimmer} 1.4s ease infinite;
  width: ${({ width }) => width || "100%"};
  height: ${({ height }) => height || "16px"};
  border-radius: ${({ radius }) => radius || "4px"};
`;

const CardSkeletonWrapper = styled(CardWrapper)<{
  variant: "import" | "portfolio";
}>`
  min-height: ${({ variant }) => (variant === "portfolio" ? "208px" : "248px")};
`;

const CardSkeletonHeader = styled(<PERSON><PERSON><PERSON><PERSON>)<{
  variant: "import" | "portfolio";
}>`
  padding: ${({ variant }) =>
    variant === "portfolio" ? "16px" : "14px 24px 10px 75px"};
`;

const CardSkeletonBody = styled(CardBody)<{
  variant: "import" | "portfolio";
}>`
  align-items: ${({ variant }) =>
    variant === "portfolio" ? "flex-start" : "center"};
`;

type CardSearchSkeletonProps = {
  variant: "portfolio" | "import";
};

export default function CardSearchSkeleton({
  variant,
}: CardSearchSkeletonProps) {
  const withIcon = variant === "import";
  return (
    <CardSkeletonWrapper variant={variant}>
      {withIcon && (
        <CardIcon>
          <Placeholder width="48px" height="48px" radius="50%" />
        </CardIcon>
      )}

      <CardSkeletonHeader variant={variant}>
        <div>
          <Title>
            <Placeholder
              width={variant === "portfolio" ? "200px" : "120px"}
              height="22px"
            />
          </Title>
          <Subtitle>
            <Placeholder as="span" width="80px" height="18px" />
          </Subtitle>
        </div>

        {withIcon && (
          <div>
            <Placeholder width="32px" height="32px" radius="8px" />
          </div>
        )}
      </CardSkeletonHeader>

      <CardSkeletonBody variant={variant}>
        <Placeholder width="60%" height="16px" />
        <Placeholder width="40%" height="16px" />
        <Placeholder width="80%" height="16px" />
      </CardSkeletonBody>
    </CardSkeletonWrapper>
  );
}
