// src/components/Card/styles.ts
import styled from "styled-components";
import { theme } from "@/styles/theme";

interface CardHeaderProps {
  padding?: string;
}

interface CardWrapperProps {
  maxHeight?: string;
  maxWidth?: string;
}

interface CardBodyProps {
  alignItems?: string;
}

export const CardWrapper = styled.div.withConfig({
  shouldForwardProp: (prop) => !["maxHeight", "maxWidth"].includes(prop),
})<CardWrapperProps>`
  border-radius: 12px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(18, 32, 60, 0.07);
  padding: 0;
  display: flex;
  flex-direction: column;
  min-width: 359px;
  max-width: ${(props) => props.maxWidth || "547px"};
  max-height: ${(props) => props.maxHeight || "248px"};
  border: 0.5px solid ${theme.colors.neutral400};
`;

export const CardIcon = styled.div`
  position: absolute;
  min-width: 45px;
  min-height: 45px;
  border-radius: 50%;
  background: #fff;
  margin: auto 16px;
  transform: translateY(70%);
  border: 0.5px solid ${theme.colors.neutral400};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(18, 32, 60, 0.07);
`;

export const CardHeader = styled.div.withConfig({
  shouldForwardProp: (prop) => !["padding"].includes(prop),
})<CardHeaderProps>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e3e5eb;
  background-color: ${theme.colors.neutral200};
  padding: ${(props) => props.padding || "14px 24px 10px 75px"};
  border-radius: 12px 12px 0 0;
  max-height: 65px;
  min-height: 56px;
`;

export const Title = styled.h2`
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
`;

export const Subtitle = styled.p`
  font-size: 12px;
  color: ${theme.colors.neutral700};
  font-weight: 500;
  margin: 0;
`;

export const StatusSection = styled.div`
  max-width: 249px;
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

export const TextData = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

export const Label = styled.span`
  font-size: 14px;
  color: ${theme.colors.neutral700};
  font-weight: 400;
`;

export const Value = styled.span`
  font-size: 16px;
  color: ${theme.colors.neutral900};
  font-weight: 500;
`;

export const CardBody = styled.div.withConfig({
  shouldForwardProp: (prop) => !["alignItems"].includes(prop),
})<CardBodyProps>`
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: ${(props) => props.alignItems || "center"};
  margin: 16px 16px;
  min-height: 97px;
`;

export const CardFooter = styled.div`
  display: flex;
  max-width: 359px;
  margin-top: 16px;
`;

export const Actions = styled.div`
  :where(.css-dev-only-do-not-override-vrrzze).ant-switch.ant-switch-checked {
    background: #24a148;
  }

  :where(.css-vrrzze).ant-switch.ant-switch-checked .ant-switch-inner {
    background: #24a148;
  }

  :where(
      .css-dev-only-do-not-override-vrrzze
    ).ant-switch.ant-switch-checked:hover:not(.ant-switch-disabled) {
    background: #399d54;
  }
`;
