// src/components/Card/CardWorkflow.tsx
import React from "react";
import Button from "@/components/Button";
import {
  Card<PERSON>ontaine<PERSON>,
  Header,
  Title,
  Description,
  TemplateContainer,
  TemplateHeader,
  DownloadWrapper,
  TemplateColumnsGrid,
  Content,
} from "../styles";
import StatusTag from "../../StatusTag";
import { CircleArrowDown } from "lucide-react";

export interface CardWorkflowProps {
  id: string;
  title: string;
  workflowDescription: string;
  categoryLabel: string;
  categoryColor?: string;
  selected: boolean;
  onSelect: (id: string) => void;
  templateColumns?: string[];
  onDownloadTemplate?: (id: string) => void;
}

export default function CardWorkflow({
  id,
  title,
  workflowDescription,
  categoryLabel,
  categoryColor,
  selected,
  onSelect,
  templateColumns = [],
  onDownloadTemplate,
}: CardWorkflowProps) {
  return (
    <CardContainer selected={selected} onClick={() => onSelect(id)}>
      <Header>
        <Title>{title}</Title>
        <StatusTag
          label={categoryLabel}
          size="small"
          backgroundColor={categoryColor}
          variant="success"
        />
      </Header>

      <Content>
        <Description>{workflowDescription}</Description>

        {selected && (
          <TemplateContainer>
            <TemplateHeader>Modelo de template</TemplateHeader>

            <TemplateColumnsGrid columns={templateColumns.length}>
              {templateColumns.map((col) => (
                <div key={col}>
                  <p>{col}</p>
                </div>
              ))}
            </TemplateColumnsGrid>
            {onDownloadTemplate && (
              <DownloadWrapper>
                <Button
                  variant="primary"
                  size="sm"
                  icon={<CircleArrowDown size={16} />}
                  label="Download"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDownloadTemplate(id);
                  }}
                />
              </DownloadWrapper>
            )}
          </TemplateContainer>
        )}
      </Content>
    </CardContainer>
  );
}
