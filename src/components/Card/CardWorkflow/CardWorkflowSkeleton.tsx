import styled, { keyframes } from "styled-components";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Content } from "../styles";

const shimmer = keyframes`
  0% { background-position: -200px 0 }
  100% { background-position: calc(200px + 100%) 0 }
`;

const Placeholder = styled.div<{
  width?: string;
  height?: string;
  radius?: string;
}>`
  background: linear-gradient(90deg, #f0f0f0 25%, #e4e4e4 37%, #f0f0f0 63%);
  background-size: 400% 100%;
  animation: ${shimmer} 1.4s ease infinite;
  width: ${({ width }) => width || "100%"};
  height: ${({ height }) => height || "16px"};
  border-radius: ${({ radius }) => radius || "4px"};
  margin-bottom: 8px;
`;

export default function CardWorkflowSkeleton() {
  return (
    <CardContainer selected={false}>
      <Header>
        <Placeholder width="60%" height="20px" />
        <Placeholder width="80px" height="20px" radius="999px" />
      </Header>

      <Content>
        <Placeholder width="100%" height="16px" />
        <Placeholder width="90%" height="16px" />
      </Content>
    </CardContainer>
  );
}
