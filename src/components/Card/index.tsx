import { Divider } from "antd";
import { Wrapper, CardData } from "./styles";

interface CardProps {
  title: string;
  icon: React.ReactNode;
  value: string;
  subtitle?: string;
  infoText?: string;
  secondaryValue?: string;
}

export function Card({
  title,
  icon,
  value,
  subtitle,
  infoText,
  secondaryValue,
}: CardProps) {
  return (
    <Wrapper>
      <CardData>
        <div
          className="icon"
          style={{
            width: 26,
            height: 26,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            position: "relative",
          }}
        >
          <div
            style={{
              position: "absolute",
              width: 20,
              height: 20,
              borderRadius: "50%",
              background: "#ECECEC",
              left: 0,
              top: 0,
            }}
          />
          {icon && (
            <span style={{ position: "relative", zIndex: 1 }}>{icon}</span>
          )}
        </div>
        <div className="content">
          <h3 className="full-width">{title}</h3>
          <p>{subtitle}</p>
          <Divider style={{ margin: "6px 0" }} />

          <div className="data">
            <span>
              <b>{value}</b>
            </span>
          </div>
          <div className="info-text">
            <span className="info-text-label"> {infoText}</span>
            <span className="secondary-info">{secondaryValue}</span>
          </div>
        </div>
      </CardData>
    </Wrapper>
  );
}
