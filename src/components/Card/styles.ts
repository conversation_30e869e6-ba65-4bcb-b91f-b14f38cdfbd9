import styled from "styled-components";

export const NoData = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 16px;
  background: #ececec;
  border-radius: 30px;
  max-width: max-content;

  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #bfbfbf;
`;

export const Wrapper = styled.div`
  display: grid;
  align-items: center;
  gap: 24px;
  padding-bottom: 32px;
`;

export const CardData = styled.div`
  display: flex;
  align-items: center;
  max-height: 159px;
  gap: 16px;
  background: #fefefe;
  border: 0.5px solid #cacaca;
  box-shadow:
    0px 4px 8px rgba(0, 0, 0, 0.05),
    0px 2px 4px rgba(0, 0, 0, 0.05),
    0px 1px 2px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  padding: 16px;
  align-self: stretch;
  .ant-progress-inner {
    box-shadow: inset 0px 4px 5px rgba(0, 0, 0, 0.05);
  }
  .content {
    width: 100%;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .full-width {
    display: flex;
    justify-content: space-between;
    width: 100%;
    min-width: 100%;
    align-items: center;
  }
  h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
    max-width: max-content;
  }

  p {
    font-size: 14px;
    font-family: var(--font-inter);
    color: #9e9e9e;
  }

  .progress-data {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    font-weight: 500;
    font-family: var(--font-inter);
    span:first-child {
      display: flex;
      justify-content: center;
      background: #ececec;
      border-radius: 8px;
      color: var(--primary-color);
      padding: 4px;
      min-width: 26px;
    }

    span:nth-child(3) {
      margin-left: auto;
      font-family: var(--font-inter);
      color: #9e9e9e;
    }
  }

  .data {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    font-family: var(--font-inter);
    span:first-child {
      font-size: 14px;
      font-weight: 500;
      color: #9e9e9e;
      display: flex;
      align-items: baseline;
      gap: 4px;

      b {
        color: ${({ theme }) => theme.colors.primary};
        font-size: 32px;
        font-weight: 600;
      }
    }
    /* .secondary-info {
      font-weight: 500;
      font-size: 14px;
      line-height: 18px;
      color: #399d54;
      background-color: ${({ theme }) => theme.colors.neutral200};
      padding: 2px 4px;
      border-radius: 4px;
    } */
  }
  .info-text {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 4px;
    margin-top: 8px;
    .info-text-label {
      font-size: 13px;
      font-weight: 400;
      color: ${({ theme }) => theme.colors.neutral800};
    }
    .secondary-info {
      font-size: 12px;
      font-weight: 500;
      color: #399d54;
      background-color: ${({ theme }) => theme.colors.neutral200};
      padding: 2px 4px;
      border-radius: 4px;
    }
  }

  .review {
    color: #454545;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
`;

export const Info = styled.div`
  max-width: 400px;

  .header {
    display: flex;
    align-items: center;
    column-gap: 24px;
    row-gap: 16px;
    padding-left: 20px;
    padding-right: 20px;
    p {
      margin-bottom: unset;
    }
  }
  h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
  }
  p {
    font-size: 14px;
    font-family: var(--font-inter);
    margin-bottom: 8px;
    color: #9e9e9e;
  }
  .content {
    display: flex;
    flex-direction: column;
    padding-left: 20px;
    padding-right: 20px;
    gap: 16px;

    h4 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
      font-family: var(--font-roboto-flex);

      &::before {
        content: "";
        display: inline-block;
        width: 8px;
        height: 8px;
        background: currentColor;
        border-radius: 50%;
        margin-right: 4px;
      }
    }
    span {
      font-size: 14px;
      line-height: 17px;
      color: #9e9e9e;
    }
    p {
      font-size: 14px;
      color: #454545;
    }
  }
`;

export const CardContainer = styled.div<{ selected: boolean }>`
  max-width: 951px;
  border: 0.5px solid
    ${({ selected, theme }) =>
      selected ? theme.colors.success : theme.colors.neutral400};
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors.background};
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition:
    border-color 0.3s ease,
    box-shadow 0.3s ease,
    transform 0.12s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
`;

export const Header = styled.div`
  display: flex;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.neutral400};
  border-radius: 8px 8px 0 0;
  align-items: center;
  margin-bottom: 8px;
  background-color: ${({ theme }) => theme.colors.neutral200};
`;

export const Title = styled.h4`
  font-size: 16px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.neutral900};
  margin: 0;
`;

export const Content = styled.div`
  padding: 16px;
`;

export const Description = styled.p`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.neutral900};
  margin: 0 0 12px;
`;

export const TemplateContainer = styled.div`
  border: 0.5px solid ${({ theme }) => theme.colors.neutral400};
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors.neutral200};
  padding: 16px;
  position: relative;
  margin-top: 12px;
  max-height: 244px;
`;

export const TemplateHeader = styled.div`
  font-size: 12px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.neutral700};
  margin-bottom: 8px;
`;

export const TemplateColumnsGrid = styled.div<{ columns: number }>`
  display: grid;
  grid-template-columns: repeat(${(p) => p.columns}, 1fr);
  width: 100%;
  height: 80px;
  margin: 8px 0px 24px 0px;
  max-width: 1000px;
  overflow-y: auto;
  padding-bottom: 5px;

  &::-webkit-scrollbar {
    height: 14px;
    background-color: ${({ theme }) => theme.colors.neutral600};
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: ${({ theme }) => theme.colors.primary};
    border-radius: 4px;
  }

  > div {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 500;
    color: ${({ theme }) => theme.colors.neutral900};
    text-align: center;
    // max-width: 230px;

    p {
      width: 100%;
      border-bottom: 1px solid ${({ theme }) => theme.colors.neutral400};
      padding: 8px 16px;
    }
  }

  > div:not(:last-child) {
    border-right: 1px solid ${({ theme }) => theme.colors.neutral400};
    height: 100%;
  }
`;

export const DownloadWrapper = styled.div`
  margin-top: 8px;
`;
