"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, XAxis } from "recharts";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { ChartLegend } from "./ChartLegend";
import { useEffect, useState } from "react";
import { fetchFirstMessagesSentMetrics } from "@/services/metricsService";
import {
  fetchPortfolioItemsWithInteractionMetrics,
  fetchPortfolioItemsGroupedByDateMetrics,
} from "@/services/metricsService";
import dayjs from "dayjs";
import { theme } from "@/styles/theme";
export const description = "A multiple bar chart";

const chartConfig = {
  cobrancas_iniciadas: {
    label: "Acionamentos",
    color: "#0F61D0",
  },
  interaction: {
    label: "Negociações com interação",
    color: "#59595F",
  },
  closed: {
    label: "Negociações com sucesso",
    color: "#33BF66",
  },
} satisfies ChartConfig;

const chartLegendItems = [
  {
    label: "Acionamentos",
    color: "#0F61D0",
  },
  {
    label: "Negociações com interação",
    color: "#59595F",
  },
  {
    label: "Negociações com sucesso",
    color: "#33BF66",
  },
];

export interface ChartBarMultipleProps {
  startDate?: string;
  endDate?: string;
}

export function ChartBarMultiple({
  startDate,
  endDate,
}: ChartBarMultipleProps) {
  const [isLoading, setIsLoading] = useState(false);

  const [data, setData] = useState<
    {
      date: string;
      cobrancas_iniciadas: number;
      interaction: number;
      closed: number;
    }[]
  >([]);

  useEffect(() => {
    const load = async () => {
      setIsLoading(true);
      const isTotal = !startDate && !endDate;
      const ds = isTotal
        ? dayjs()
            .subtract(1, "year")
            .startOf("day")
            .format("YYYY-MM-DDTHH:mm:ss")
        : dayjs(startDate!).startOf("day").format("YYYY-MM-DDTHH:mm:ss");
      const de = isTotal
        ? dayjs().endOf("day").format("YYYY-MM-DDTHH:mm:ss")
        : dayjs(endDate!).endOf("day").format("YYYY-MM-DDTHH:mm:ss");

      try {
        const [firstResp, interactionResp, closedResp] = await Promise.all([
          fetchFirstMessagesSentMetrics(ds, de) as Promise<{
            totalFirstMessagesSent: {
              dailyTotals: Record<string, number>;
            };
          }>,
          fetchPortfolioItemsWithInteractionMetrics(ds, de) as Promise<
            Record<string, number>
          >,
          fetchPortfolioItemsGroupedByDateMetrics(ds, de) as Promise<
            Record<string, number>
          >,
        ]);
        const firstMap = firstResp.totalFirstMessagesSent.dailyTotals;
        const interactionMap = interactionResp;
        const closedMap = closedResp;

        if (isTotal) {
          const totalFirst = Object.values(firstMap).reduce((a, b) => a + b, 0);
          const totalInteraction = Object.values(interactionMap).reduce(
            (a, b) => a + b,
            0
          );
          const totalClosed = Object.values(closedMap).reduce(
            (a, b) => a + b,
            0
          );

          setData([
            {
              date: "Total",
              cobrancas_iniciadas: totalFirst,
              interaction: totalInteraction,
              closed: totalClosed,
            },
          ]);
        } else {
          const start = dayjs(ds);
          const end = dayjs(de);
          const daysCount = end.diff(start, "day");
          const points = Array.from({ length: daysCount + 1 }).map((_, idx) => {
            const date = start.add(idx, "day").format("YYYY-MM-DD");
            return {
              date,
              cobrancas_iniciadas: firstMap[date] || 0,
              interaction: interactionMap[date] || 0,
              closed: closedMap[date] || 0,
            };
          });

          setData(points);
        }
      } catch (err) {
        console.error("Erro ao carregar dados do gráfico:", err);
      } finally {
        setIsLoading(false);
      }
    };

    load();
  }, [startDate, endDate]);

  return (
    <Card
      style={{
        padding: "30px",
        height: 470,
        border: `0.5px solid ${theme.colors.neutral400}`,
      }}
    >
      <CardHeader style={{ gap: "10px" }}>
        <CardTitle>Performance da operação</CardTitle>
        <CardDescription>
          Dados de acionamentos, negociações com interação e negociações com
          sucesso baseada no período selecionado.
        </CardDescription>
        <ChartLegend items={chartLegendItems} />
      </CardHeader>
      <CardContent style={{ height: 500, gap: 8 }}>
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <h1>Carregando...</h1>
          </div>
        ) : (
          <ChartContainer
            config={chartConfig}
            style={{ maxHeight: 296, width: "100%" }}
            className="gap-8"
          >
            <BarChart
              accessibilityLayer
              data={data}
              barCategoryGap="40%"
              barGap={8}
            >
              <CartesianGrid vertical={false} strokeDasharray="3 3" />
              <XAxis
                dataKey="date"
                tickLine={false}
                tickMargin={10}
                axisLine={false}
                tickFormatter={(v) =>
                  v === "Total" ? "Total" : dayjs(v).format("DD/MM")
                }
              />
              <ChartTooltip
                cursor={true}
                labelFormatter={(v) =>
                  v === "Total" ? "Total" : dayjs(v).format("DD/MM")
                }
                labelClassName="font-switzer font-medium text-[14px]"
                content={
                  <ChartTooltipContent
                    className="min-w-[16rem] font-switzer text-black font-medium"
                    style={{ padding: "10px" }}
                    indicator="line"
                  />
                }
              />
              <Bar dataKey="cobrancas_iniciadas" fill="#0F61D0" barSize={12} />
              <Bar dataKey="interaction" fill="#59595F" barSize={12} />
              <Bar dataKey="closed" fill="#33BF66" barSize={12} />
            </BarChart>
          </ChartContainer>
        )}
      </CardContent>
    </Card>
  );
}
