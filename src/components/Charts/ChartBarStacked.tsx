/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { <PERSON>, BarChart, CartesianGrid, XAxis } from "recharts";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ChartLegend } from "./ChartLegend";
import {
  // ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { useEffect, useState } from "react";
import { fetchPortfolioItemsWithInteractionMetrics } from "@/services/metricsService";
import { fetchPortfolioItemsGroupedByDateMetrics } from "@/services/metricsService";
import dayjs from "dayjs";
export const description = "A stacked bar chart with a legend";

// const chartData = [
//   { month: "January", desktop: 186, mobile: 80 },
//   { month: "February", desktop: 305, mobile: 200 },
//   { month: "March", desktop: 237, mobile: 120 },
//   { month: "April", desktop: 73, mobile: 190 },
//   { month: "May", desktop: 209, mobile: 130 },
//   { month: "June", desktop: 214, mobile: 140 },
// ];

export interface ChartBarStackedProps {
  startDate?: string;
  endDate?: string;
}

const chartConfig = {
  interaction: {
    label: "Cobranças com interação",
    color: "#59595F",
  },
  closed: {
    label: "Acordos fechados",
    color: "#33BF66",
  },
} as const;

const chartLegendItems = [
  {
    label: "Cobranças com interação",
    color: "#59595F",
  },
  {
    label: "Acordos fechados",
    color: "#33BF66",
  },
];

export function ChartBarStacked({ startDate, endDate }: ChartBarStackedProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState<
    {
      date: string;
      interaction: number;
      closed: number;
    }[]
  >([]);

  useEffect(() => {
    const load = async () => {
      setIsLoading(true);
      const isTotal = !startDate && !endDate;
      const ds = isTotal
        ? dayjs().subtract(1, "year").format("YYYY-MM-DD")
        : dayjs(startDate!).format("YYYY-MM-DD");
      const de = isTotal
        ? dayjs().format("YYYY-MM-DD")
        : dayjs(endDate!).format("YYYY-MM-DD");

      try {
        const [intResp, grpResp] = await Promise.all([
          fetchPortfolioItemsWithInteractionMetrics(ds, de),
          fetchPortfolioItemsGroupedByDateMetrics(ds, de),
        ]);
        const interactionMap = intResp as Record<string, number>;
        const closedMap = grpResp as Record<string, number>;
        if (isTotal) {
          const totalInteraction = Object.values(interactionMap).reduce(
            (a, b) => a + b,
            0
          );
          const totalClosed = Object.values(closedMap).reduce(
            (a, b) => a + b,
            0
          );
          setData([
            {
              date: "Total",
              interaction: totalInteraction,
              closed: totalClosed,
            },
          ]);
        } else {
          const start = dayjs(ds);
          const end = dayjs(de);
          const daysCount = end.diff(start, "day");

          const points = Array.from({ length: daysCount + 1 }).map((_, idx) => {
            const key = start.add(idx, "day").format("YYYY-MM-DD");
            return {
              date: key,
              interaction: interactionMap[key] ?? 0,
              closed: closedMap[key] ?? 0,
            };
          });

          setData(points);
        }
      } catch (err) {
        console.error("Erro ao carregar gráfico empilhado:", err);
      } finally {
        setIsLoading(false);
      }
    };

    load();
  }, [startDate, endDate]);

  return (
    <Card
      style={{
        padding: "30px",
        height: 470,
        border: "0.5px solid #cacaca",
      }}
    >
      <CardHeader style={{ gap: "10px" }}>
        <CardTitle>
          Performance de cobranças com interação e acordos fechados
        </CardTitle>
        <CardDescription>
          Taxa de conversão entre cobranças com interação e acordos fechados,
          com base no período selecionado.
        </CardDescription>
        <ChartLegend items={chartLegendItems} />
      </CardHeader>
      <CardContent style={{ height: 500, gap: 8 }}>
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <h1>Carregando...</h1>
          </div>
        ) : (
          <ChartContainer
            config={chartConfig}
            style={{ maxHeight: 296, width: "100%" }}
          >
            <BarChart
              accessibilityLayer
              data={data}
              barCategoryGap="40%"
              barGap={8}
            >
              <CartesianGrid vertical={false} strokeDasharray="3 3" />
              <XAxis
                dataKey="date"
                tickLine={false}
                tickMargin={10}
                axisLine={false}
                tickFormatter={(v) =>
                  v === "Total" ? "Total" : dayjs(String(v)).format("DD/MM")
                }
              />
              <ChartTooltip
                cursor={true}
                labelFormatter={(v) =>
                  v === "Total" ? "Total" : dayjs(String(v)).format("DD/MM")
                }
                labelClassName="font-switzer font-medium text-[14px]"
                content={
                  <ChartTooltipContent
                    className="min-w-[15rem] font-switzer text-black font-medium"
                    style={{ padding: "10px" }}
                    indicator="line"
                  />
                }
              />
              <Bar dataKey="interaction" stackId="a" fill="#59595F" />
              <Bar dataKey="closed" stackId="a" fill="#33BF66" />
            </BarChart>
          </ChartContainer>
        )}
      </CardContent>
    </Card>
  );
}
