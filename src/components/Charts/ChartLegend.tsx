import { ChartLegendContainer, ColorLine, LegendItem } from "./styles";

export const ChartLegend = ({
  items,
}: {
  items: { label: string; color: string }[];
}) => {
  return (
    <ChartLegendContainer>
      {items.map((item) => (
        <LegendItem key={item.label}>
          <ColorLine color={item.color} />
          <h1>{item.label}</h1>
        </LegendItem>
      ))}
    </ChartLegendContainer>
  );
};
