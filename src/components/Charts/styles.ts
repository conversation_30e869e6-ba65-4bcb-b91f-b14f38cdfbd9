import styled from "styled-components";

export const ColorLine = styled.div<{ color: string }>`
  width: 24px;
  height: 4px;
  background-color: ${(props) => props.color};
  border-radius: 2px;
  position: relative;
`;

export const ChartLegendContainer = styled.div`
  display: flex;
  gap: 40px;
  flex-direction: row;
  margin-top: 14px;
  align-items: center;
`;

export const LegendItem = styled.div`
  display: flex;
  gap: 8px;
  flex-direction: row;
  align-items: center;

  h1 {
    margin: 0;
  }
`;
