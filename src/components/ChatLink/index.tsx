import { FiExternalLink } from "react-icons/fi";
import styled from "styled-components";

const LinkIcon = styled.div`
  color: ${({ theme }) => theme.colors.link};
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
`;

interface ChatLinkProps {
  chatId: string;
}

const ChatLink = ({ chatId }: ChatLinkProps) => {
  return (
    <LinkIcon onClick={() => console.log(`Opening chat: ${chatId}`)}>
      <FiExternalLink size={20} />
    </LinkIcon>
  );
};

export default ChatLink;
