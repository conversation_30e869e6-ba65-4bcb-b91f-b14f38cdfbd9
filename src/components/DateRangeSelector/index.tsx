/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState } from "react";
import {
  CalendarBlock,
  Container,
  Option,
  RangePickerContainer,
} from "./styles";
import { FiCalendar } from "react-icons/fi";
import { theme } from "@/styles/theme";
import { DatePicker, ConfigProvider } from "antd";
import { Dayjs } from "dayjs";
import "dayjs/locale/pt-br";
import locale from "antd/locale/pt_BR";
import { DateRangeOptionValue } from "@/hooks/useDateRange";

export interface DateRangeOption {
  label: string;
  value: string;
}

export interface DateRangeSelectorProps {
  options?: DateRangeOption[];
  initialValue?: DateRangeOptionValue | string;
  onChange?: (value: DateRangeOptionValue, dates?: [string, string]) => void;
}

export default function DateRangeSelector({
  initialValue,
  onChange,
}: DateRangeSelectorProps) {
  const defaultOptions: DateRangeOption[] = [
    { label: "30 dias", value: "30" },
    { label: "15 dias", value: "15" },
    { label: "7 dias", value: "7" },
    { label: "Hoje", value: "today" },
    { label: "Total", value: "all" },
    { label: "Customizado", value: "custom" },
  ];

  const [selected, setSelected] = useState(initialValue ?? "today");
  const [customDates, setCustomDates] = useState<[Dayjs, Dayjs] | null>(null);

  const handleClick = (value: string) => {
    setSelected(value);
    if (value !== "custom") {
      onChange?.(value as DateRangeOptionValue);
      setCustomDates(null);
    }
  };

  const handleCustomChange = (dates: any) => {
    setCustomDates(dates);
    if (dates && dates[0] && dates[1]) {
      onChange?.("custom", [
        dates[0].startOf("day").utc().toISOString(),
        dates[1].endOf("day").utc().toISOString(),
      ]);
    }
  };

  return (
    <ConfigProvider locale={locale}>
      <Container>
        <CalendarBlock>
          <FiCalendar size={16} color={theme.colors.neutral800} />
        </CalendarBlock>
        {defaultOptions.map((opt) => {
          if (opt.value === "custom") {
            return (
              <Option
                key="custom"
                isSelected={selected === "custom"}
                onClick={() => handleClick("custom")}
              >
                {selected !== "custom" && "Customizado"}
                {selected === "custom" && (
                  <RangePickerContainer>
                    <DatePicker.RangePicker
                      value={customDates as any}
                      format="DD/MM/YYYY"
                      onChange={handleCustomChange}
                      allowClear={false}
                      style={{
                        width: 205,
                        height: 20,
                        borderRadius: 10,
                        color: "#fff",
                      }}
                      separator={<div style={{ color: "#fff" }}>→</div>}
                      placement="bottomLeft"
                      getPopupContainer={(trigger) =>
                        trigger.parentNode as HTMLElement
                      }
                      placeholder={["Data inicial", "Data final"]}
                      variant="borderless"
                      suffixIcon={false}
                    />
                  </RangePickerContainer>
                )}
              </Option>
            );
          }
          return (
            <Option
              key={opt.value}
              isSelected={selected === opt.value}
              onClick={() => handleClick(opt.value)}
            >
              {opt.label}
            </Option>
          );
        })}
      </Container>
      {/* {selected === "custom" && (
        <div style={{ marginTop: 12, marginBottom: 4 }}>
          <DatePicker.RangePicker
            value={customDates as any}
            format="DD/MM/YYYY"
            onChange={handleCustomChange}
            allowClear={false}
            style={{ width: 260 }}
            getPopupContainer={(trigger) =>
              (trigger.parentNode as HTMLElement) || trigger
            }
            placeholder={["Data inicial", "Data final"]}
          />
        </div>
      )} */}
    </ConfigProvider>
  );
}
