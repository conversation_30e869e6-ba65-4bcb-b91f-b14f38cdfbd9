import styled from "styled-components";
import { theme } from "@/styles/theme";

export const Container = styled.div`
  display: flex;
  border: 0.5px solid ${theme.colors.neutral400};
  border-radius: 50px;
  overflow: visible;
`;

export const Option = styled.button.withConfig({
  shouldForwardProp: (prop) => prop !== "isSelected",
})<{ isSelected: boolean }>`
  position: relative;
  flex: none;
  padding: 6px 12px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  background: ${({ isSelected }) =>
    isSelected ? theme.colors.primary : theme.colors.neutral200};
  color: ${({ isSelected }) => (isSelected ? "#fff" : theme.colors.neutral900)};
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: ${({ isSelected }) =>
      isSelected ? theme.colors.primary : "#f2f4f7"};
  }

  &:not(:last-child) {
    border-right: 1px solid #d0d5dd;
  }

  &:last-child {
    border-start-end-radius: 20px;
    border-end-end-radius: 20px;
  }
`;

export const CalendarBlock = styled.div`
  background: ${theme.colors.neutral600};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  height: 34px;
  border-right: 1px solid ${theme.colors.neutral400};
  border-start-start-radius: 20px;
  border-end-start-radius: 20px;

  svg {
    display: block;
  }
`;

export const RangePickerContainer = styled.div`
  top: 100%;
  z-index: 1000;

  :where(.css-dev-only-do-not-override-vrrzze).ant-picker
    .ant-picker-input
    > input::placeholder {
    color: rgb(255, 255, 255) !important;
  }

  .ant-picker-input > input::placeholder {
    color: white !important;
  }

  .ant-picker-input > input:focus {
    color: white !important;
  }

  input::placeholder {
    color: white !important;
  }

  input:focus {
    color: white !important;
  }

  :where(.css-dev-only-do-not-override-vrrzze).ant-picker
    .ant-picker-input
    .ant-picker-input-active
    .ant-picker-input-placeholder {
    color: #fff;
  }
  .ant-picker-focused .ant-picker-input > input::placeholder {
    color: #fff;
  }
  .ant-picker-input .ant-picker-input-placeholder {
    color: #fff;
  }
  .ant-picker-input .ant-picker-input-placeholder > input {
    color: white !important;
  }

  :where(.css-dev-only-do-not-override-vrrzze).ant-picker
    .ant-picker-input-placeholder
    > input {
    color: #fff;
  }
`;
