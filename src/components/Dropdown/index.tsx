"use client";
import React, { useState, useRef, useEffect } from "react";
import { DropdownContainer, DropdownContent, DropdownItem } from "./styles";
import { DropdownProps } from "./types";

export function Dropdown({
  trigger,
  items,
  children,
  minWidth = 200,
  placement = "bottomRight",
  arrowPlacement = "right",
  style,
  className,
}: DropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => setIsOpen((open) => !open);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        triggerRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const getPositionStyle = () => {
    switch (placement) {
      case "top":
        return { bottom: "calc(100% + 8px)", left: 0 };
      case "topRight":
        return { bottom: "calc(100% + 8px)", right: "-25px" };
      case "bottom":
        return { top: "calc(100% + 8px)", left: 0 };
      case "bottomRight":
      default:
        return { top: "calc(100% + 8px)", right: "-32px" };
    }
  };

  return (
    <div
      style={{ position: "relative", display: "inline-block" }}
      className={className}
    >
      <div
        ref={triggerRef}
        onClick={toggleDropdown}
        style={{ cursor: "pointer", display: "inline-flex" }}
      >
        {trigger}
      </div>
      <DropdownContainer
        ref={dropdownRef}
        isOpen={isOpen}
        arrowPlacement={arrowPlacement}
        style={{
          minWidth,
          ...getPositionStyle(),
          ...style,
        }}
      >
        <DropdownContent>
          {items
            ? items.map((item) => (
                <DropdownItem
                  key={item.key}
                  onClick={() => {
                    if (!item.disabled && item.onClick) {
                      item.onClick();
                      setIsOpen(false);
                    }
                  }}
                  style={item.style}
                  $disabled={item.disabled}
                >
                  {item.icon}
                  {item.label}
                </DropdownItem>
              ))
            : children}
        </DropdownContent>
      </DropdownContainer>
    </div>
  );
}
