import styled from "styled-components";

export const DropdownContainer = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== "isOpen" && prop !== "arrowPlacement",
})<{ isOpen: boolean; arrowPlacement?: "left" | "right" | "center" }>`
  position: absolute;
  background-color: ${({ theme }) => theme.colors.neutral100};
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid ${({ theme }) => theme.colors.neutral400};
  z-index: 20;
  opacity: ${({ isOpen }) => (isOpen ? 1 : 0)};
  transform: ${({ isOpen }) =>
    isOpen ? "translateY(0px)" : "translateY(-10px)"};
  visibility: ${({ isOpen }) => (isOpen ? "visible" : "hidden")};
  pointer-events: ${({ isOpen }) => (isOpen ? "auto" : "none")};
  transition:
    opacity 0.2s ease,
    transform 0.2s ease,
    visibility 0.2s ease;

  &::before {
    content: "";
    position: absolute;

    border: 1px solid ${({ theme }) => theme.colors.neutral400};
    top: -5px;
    ${({ arrowPlacement }) => {
      switch (arrowPlacement) {
        case "center":
          return "left: 50%;";
        case "left":
          return "left: 24px;";
        case "right":
          return "right: 32px;";
      }
    }}
    width: 11px;
    height: 11px;
    background-color: ${({ theme }) => theme.colors.neutral100};
    transform: rotate(45deg);
    border-top-left-radius: 2px;
    box-shadow: 0 4px 4px rgba(0, 0, 0, 0.05);
    z-index: -1;
  }
`;

export const DropdownContent = styled.div`
  position: relative;
  width: 100%;
  background-color: ${({ theme }) => theme.colors.neutral100};
  border-radius: 8px;
  overflow: hidden;
  z-index: 1;
`;

export const DropdownItem = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== "isActive",
})<{ isActive?: boolean; $disabled?: boolean }>`
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: ${({ $disabled }) => ($disabled ? "not-allowed" : "pointer")};
  color: ${({ theme, $disabled }) =>
    $disabled ? theme.colors.neutral400 : theme.colors.neutral900};
  font-size: 15px;
  opacity: ${({ $disabled }) => ($disabled ? 0.5 : 1)};
  pointer-events: ${({ $disabled }) => ($disabled ? "none" : "auto")};
  background: none;
  transition: background 0.15s;

  &:hover {
    background-color: ${({ theme, $disabled }) =>
      !$disabled && theme.colors.neutral200};
  }

  svg {
    color: ${({ theme }) => theme.colors.neutral700};
  }
  &:not(:last-child) {
    border-bottom: 1px solid ${({ theme }) => theme.colors.neutral400};
  }
`;
