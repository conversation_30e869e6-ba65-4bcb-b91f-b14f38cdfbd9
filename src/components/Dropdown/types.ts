import { ReactNode, CSSProperties } from "react";

export type DropdownItemType = {
  key: string;
  icon?: ReactNode;
  label: ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  style?: CSSProperties;
};

export interface DropdownProps {
  trigger: ReactNode;
  items?: DropdownItemType[];
  children?: ReactNode;
  minWidth?: number | string;
  placement?: "top" | "topRight" | "bottom" | "bottomRight";
  arrowPlacement?: "left" | "right" | "center";
  style?: CSSProperties;
  className?: string;
}
