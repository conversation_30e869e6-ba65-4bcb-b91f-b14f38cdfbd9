import Image from "next/image";
import styled from "styled-components";
import noFound from "@/assets/images/digai-not-found.png";

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
  padding: 110px 130px 100px 100px;
  width: 100%;
  max-height: 700px;
  height: 100%;
  text-align: center;
  gap: 53px;

  h1 {
    font-size: 24px;
    font-weight: 600;
    color: ${({ theme }) => theme.colors.neutral900};
  }

  p {
    font-size: 16px;
    font-weight: 500;
    color: ${({ theme }) => theme.colors.neutral900};
  }
`;

const Content = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

type EmptyStateProps = {
  title: string;
  subtitle?: string;
};

export default function EmptyState({ title, subtitle }: EmptyStateProps) {
  return (
    <Wrapper>
      <Image
        src={noFound}
        alt={title}
        width={396}
        height={186}
        quality={100}
        unoptimized
      />
      <Content>
        <h1>{title}</h1>
        {subtitle && <p>{subtitle}</p>}
      </Content>
    </Wrapper>
  );
}
