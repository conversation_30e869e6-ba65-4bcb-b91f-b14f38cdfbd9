import React from "react";
import styled from "styled-components";

export const FilledInput = ({
  label,
  value,
}: {
  label: string;
  value: string;
}) => {
  return (
    <Container>
      <Label>{label}</Label>
      <Value>{value}</Value>
    </Container>
  );
};

export default FilledInput;

const Container = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 2px;
`;

const Label = styled.p`
  font-size: 14px;
  font-weight: 400;
  color: ${({ theme }) => theme.colors.labelInput};
`;

const Value = styled.p`
  font-size: 16px;
  font-weight: 400;
  color: ${({ theme }) => theme.colors.neutral900};
`;
