import {
  InputWrapper,
  InputContainer,
  StyledInput,
  Label,
  ErrorText,
} from "./styles";
import { TextInputProps } from "./types";

const TextInput = ({
  label,
  iconLeft,
  selectLeft,
  error,
  disabled,
  value,
  width,
  ...rest
}: TextInputProps) => {
  const isFilled = Boolean(value && value.toString().length > 0);

  return (
    <InputWrapper width={width}>
      {label && <Label isFilled={isFilled}>{label}</Label>}

      <InputContainer disabled={disabled} hasError={!!error}>
        {iconLeft}
        {selectLeft}

        <StyledInput
          value={value}
          disabled={disabled}
          isFilled={isFilled}
          hasSelectLeft={!!selectLeft}
          {...rest}
        />
      </InputContainer>

      {error && <ErrorText>{error}</ErrorText>}
    </InputWrapper>
  );
};

export default TextInput;
