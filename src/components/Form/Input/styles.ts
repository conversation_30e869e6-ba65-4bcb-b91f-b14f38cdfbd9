import { theme } from "@/styles/theme";
import styled, { css } from "styled-components";
import { StyledInputProps } from "./types";

interface ContainerProps {
  isFilled: boolean;
  hasError?: boolean;
}

export const InputWrapper = styled.div<{ width?: string }>`
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: ${({ width }) => width || "100%"};
  position: relative;
`;

export const Label = styled.label.withConfig({
  shouldForwardProp: (prop) => prop !== "isFilled",
})<ContainerProps>`
  position: absolute;
  left: 12.6px;
  color: ${({ isFilled }) =>
    isFilled ? theme.colors.labelInput : "transparent"};
  font-weight: 500;

  top: ${({ isFilled }) => (isFilled ? "4px" : "50%")};
  font-size: ${({ isFilled }) => (isFilled ? "12px" : "16px")};
  transform: translateY(${({ isFilled }) => (isFilled ? "0" : "-50%")});

  transition: all 0.2s ease;
  pointer-events: none;
  padding: 4px 4px;
  z-index: 2;
`;

export const InputContainer = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== "disabled" && prop !== "hasError",
})<{
  disabled?: boolean;
  hasError?: boolean;
}>`
  display: flex;
  align-items: center;
  height: 58px;
  border-radius: 4px;
  border: 0.5px solid ${({ theme }) => theme.colors.neutral400};
  background-color: ${({ theme }) => theme.colors.neutral200};
  padding: 0 16px;
  gap: 12px;

  ${({ disabled }) =>
    disabled &&
    css`
      background-color: #eaeaea;
      color: ${({ theme }) => theme.colors.disabledText};
      cursor: not-allowed;
      input {
        color: ${({ theme }) => theme.colors.disabledText};
      }
    `}

  ${({ hasError }) =>
    hasError &&
    css`
      border-color: ${({ theme }) => theme.colors.error};
    `} /* box-shadow:
    0px 1px 2px rgba(0, 0, 0, 0.03),
    0px 2px 3px rgba(0, 0, 0, 0.04),
    0px 3px 5px rgba(0, 0, 0, 0.03); */
`;

export const StyledInput = styled.input.withConfig({
  shouldForwardProp: (prop) => prop !== "isFilled" && prop !== "hasSelectLeft",
})<StyledInputProps>`
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-family: "Switzer";
  font-size: 16px;
  margin-top: ${({ isFilled, hasSelectLeft }) =>
    hasSelectLeft ? "0" : isFilled ? "10px" : "0"};
  color: ${({ theme }) => theme.colors.neutral900};

  &::placeholder {
    color: ${({ theme }) => theme.colors.neutral900};
  }
`;

export const ErrorText = styled.span`
  color: ${({ theme }) => theme.colors.error};
  font-size: 12px;
  margin-top: 2px;
`;
