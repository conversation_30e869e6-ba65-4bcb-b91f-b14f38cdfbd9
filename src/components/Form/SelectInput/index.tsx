import { useState, useRef, useEffect } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";
import { SelectInputProps } from "./types";
import {
  Wrapper,
  SelectBox,
  Label,
  Icon,
  Dropdown,
  OptionItem,
  PrefixLabel,
} from "./styles";

const SelectInput = ({
  value,
  onChange,
  options,
  placeholder = "Selecione...",
  disabled = false,
  variant = "standalone",
  labelInside,
}: SelectInputProps) => {
  const [open, setOpen] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState<number | null>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<HTMLUListElement>(null);

  const selected = options.find((opt) => opt.value === value);

  const handleSelect = (val: string) => {
    onChange?.(val);
    setOpen(false);
    setFocusedIndex(null);
  };

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        wrapperRef.current &&
        !wrapperRef.current.contains(e.target as Node)
      ) {
        setOpen(false);
        setFocusedIndex(null);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (!open) return;

    if (e.key === "ArrowDown") {
      e.preventDefault();
      setFocusedIndex((prev) =>
        prev === null || prev === options.length - 1 ? 0 : prev + 1
      );
    }

    if (e.key === "ArrowUp") {
      e.preventDefault();
      setFocusedIndex((prev) =>
        prev === null || prev === 0 ? options.length - 1 : prev - 1
      );
    }

    if (e.key === "Enter" && focusedIndex !== null) {
      e.preventDefault();
      handleSelect(options[focusedIndex].value);
    }

    if (e.key === "Escape") {
      setOpen(false);
      setFocusedIndex(null);
    }
  };

  useEffect(() => {
    if (
      listRef.current &&
      focusedIndex !== null &&
      listRef.current.children[focusedIndex]
    ) {
      const item = listRef.current.children[focusedIndex] as HTMLElement;
      item.scrollIntoView({ block: "nearest" });
    }
  }, [focusedIndex]);

  return (
    <Wrapper
      variant={variant}
      style={{ position: "relative" }}
      ref={wrapperRef}
    >
      <SelectBox
        disabled={disabled}
        variant={variant}
        onClick={() => !disabled && setOpen((prev) => !prev)}
        tabIndex={0}
        onKeyDown={handleKeyDown}
        role="combobox"
        aria-expanded={open}
        aria-haspopup="listbox"
        aria-controls="dropdown-list"
      >
        <Label>
          {labelInside && <PrefixLabel>{labelInside}</PrefixLabel>}
          {selected ? selected.label : placeholder}
        </Label>

        <Icon>
          {open ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
        </Icon>
      </SelectBox>

      {open && (
        <Dropdown id="dropdown-list" ref={listRef} role="listbox">
          {options.map((opt, index) => (
            <OptionItem
              key={opt.value}
              onClick={() => handleSelect(opt.value)}
              style={{
                backgroundColor:
                  index === focusedIndex
                    ? "rgba(0, 0, 0, 0.05)"
                    : "transparent",
              }}
            >
              {opt.label}
            </OptionItem>
          ))}
        </Dropdown>
      )}
    </Wrapper>
  );
};

export default SelectInput;
