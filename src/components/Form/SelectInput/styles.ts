import styled, { css } from "styled-components";
import { SelectInputProps } from "./types";

export const Wrapper = styled.div<{ variant: SelectInputProps["variant"] }>`
  ${({ variant }) =>
    variant === "standalone" &&
    css`
      width: 100%;
      background-color: ${({ theme }) => theme.colors.neutral200};
    `}

  ${({ variant }) =>
    variant === "embedded" &&
    css`
      width: fit-content;
    `}
`;

export const SelectBox = styled.div<{
  disabled?: boolean;
  variant: SelectInputProps["variant"];
}>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: ${({ variant }) => (variant === "embedded" ? "37px" : "58px")};
  padding: 0 16px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 400;
  cursor: pointer;
  border: 0.5px solid ${({ theme }) => theme.colors.neutral400};

  ${({ disabled }) =>
    disabled &&
    css`
      background-color: ${({ theme }) => theme.colors.disabled};
      color: #c4c4c4;
      cursor: not-allowed;
    `}

  ${({ variant }) =>
    variant === "embedded" &&
    css`
      background-color: ${({ theme }) => theme.colors.disabled};
      border: none;
      border-radius: 8px;
    `}
`;

export const Label = styled.span`
  color: ${({ theme }) => theme.colors.neutral900};
  display: flex;
  flex-direction: column;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 16px;
`;

export const Icon = styled.span`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  color: ${({ theme }) => theme.colors.neutral900};
`;

export const Dropdown = styled.ul`
  list-style: none;
  margin: 4px 0 0 0;
  padding: 4px 0;
  border: 0.5px solid ${({ theme }) => theme.colors.neutral400};
  background-color: white;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  z-index: 1000;
  width: 100%;
`;

export const OptionItem = styled.li`
  padding: 10px 16px;
  font-size: 14px;
  color: ${({ theme }) => theme.colors.neutral900};
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: ${({ theme }) => theme.colors.neutral100};
  }
`;
export const PrefixLabel = styled.span`
  font-size: 14px;
  font-weight: 400;
  color: ${({ theme }) => theme.colors.labelInput};
  margin-right: 8px;
`;
