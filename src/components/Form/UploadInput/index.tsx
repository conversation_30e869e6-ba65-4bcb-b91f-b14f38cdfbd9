// src/components/Form/UploadInput.tsx
import { useRef } from "react";
import {
  UploadWrapper,
  UploadContainer,
  HiddenInput,
  UploadText,
  UploadSubtext,
  IconArea,
  FileName,
  RemoveIcon,
  FileInfo,
  RightSide,
  UploadBox,
} from "./styles";
import { UploadInputProps } from "./types";
import { FiTrash2 } from "react-icons/fi";
import { FileText } from "lucide-react";
import Button from "@/components/Button";

interface Props extends UploadInputProps {
  onReviewClick?: () => void;
}

const UploadInput = ({
  file,
  onChange,
  accept,
  disabled,
  onReviewClick,
}: Props) => {
  const inputRef = useRef<HTMLInputElement>(null);

  const handleClick = () => {
    if (!disabled) inputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.[0]) {
      onChange(e.target.files[0]);
    }
  };

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange(null);
    if (inputRef.current) inputRef.current.value = "";
  };

  return (
    <UploadWrapper>
      <UploadBox>
        <UploadContainer hasFile={!!file} onClick={handleClick}>
          <HiddenInput
            type="file"
            ref={inputRef}
            onChange={handleFileChange}
            accept={accept}
          />

          <IconArea>
            <FileText size={40} color="#B5B6C2" strokeWidth={1.5} />
            {file ? (
              <FileName>{file.name}</FileName>
            ) : (
              <FileInfo>
                <UploadText>Selecione um arquivo</UploadText>
                <UploadSubtext>
                  Formatos aceitos (.csv). O arquivo deve seguir o modelo de
                  template do workflow selecionado.
                </UploadSubtext>
              </FileInfo>
            )}
          </IconArea>
        </UploadContainer>
        {file && (
          <RightSide>
            <RemoveIcon onClick={handleRemove}>
              <FiTrash2 size={18} />
            </RemoveIcon>
          </RightSide>
        )}
      </UploadBox>

      {file && (
        <Button
          variant="primary"
          size="md"
          label="Revisar upload"
          icon={<FileText size={16} />}
          onClick={onReviewClick}
        />
      )}
    </UploadWrapper>
  );
};

export default UploadInput;
