import styled from "styled-components";

export const UploadWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

export const UploadContainer = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== "hasFile",
})<{ hasFile: boolean }>`
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 850px;
  background-color: ${({ theme }) => theme.colors.neutral200};
  border: ${({ hasFile, theme }) =>
    hasFile
      ? `1px solid ${theme.colors.neutral400}`
      : `1px dashed ${theme.colors.neutral400}`};
  transition: 0.2s;
`;

export const UploadBox = styled.div`
  display: flex;
  flex-direction: row;
  gap: 12px;
`;

export const HiddenInput = styled.input`
  display: none;
`;

export const IconArea = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

export const FileInfo = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
`;

export const FileName = styled.span`
  font-size: 16px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.neutral900};
`;

export const UploadText = styled.span`
  font-size: 16px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.neutral900};
`;

export const UploadSubtext = styled.span`
  font-size: 14px;
  font-weight: 400;
  color: ${({ theme }) => theme.colors.neutral900};
`;

export const RemoveIcon = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${({ theme }) => theme.colors.labelInput};
  transition: 0.2s;

  &:hover {
    color: ${({ theme }) => theme.colors.error};
  }
`;

export const RightSide = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;
