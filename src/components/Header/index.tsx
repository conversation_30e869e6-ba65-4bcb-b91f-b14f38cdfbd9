"use client";
import React, { useState, useRef, useEffect } from "react";
import { ChevronDown, User, LogOut, CalendarIcon } from "lucide-react";
import {
  HeaderContainer,
  LogoContainer,
  LogoText,
  IconsContainer,
  ProfileCircle,
  DropdownContainer,
  DropdownContent,
  DropdownItem,
  HeaderWrapper,
  MetaContainer,
  InfoLabel,
  InfoContent,
  InfoBlock,
  DateInfoContainer,
} from "./styles";
import { HeaderProps } from "./types";
import { IoIosArrowForward } from "react-icons/io";
import { useRouter } from "next/navigation";
import { ChevronIcon } from "./styles";
import StatusTag from "../StatusTag";
import { portfolioStatusMap } from "@/constants/portfolioStatusMap";
import { Fetch } from "@/interceptors/Fetch";

const Header = ({
  title,
  icon,
  isSidebarOpen,
  portfolioStatus,
  portfolioCreatedAt,
  portfolioUpdatedAt,
}: HeaderProps) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const profileRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };
  const handleLogout = async () => {
    try {
      const api = Fetch();

      const response = await api.post(
        `${process.env.NEXT_PUBLIC_TRANSCENDENCE_SERVICE_URL}/v1/auth/session/logout`
      );
      if (
        typeof response === "string"
          ? response.includes("User successfully logged out")
          : (response as { message: string }).message ===
            "User successfully logged out"
      ) {
        localStorage.clear();
        sessionStorage.clear();
        router.replace("/login");
      } else {
        console.error(
          "Falha ao fazer logout:",
          (response as { data: string }).data || response
        );
      }
    } catch (err) {
      console.error("Erro ao fazer logout:", err);
    }
  };

  const [user, setUser] = useState<{
    firstname: string;
    lastname: string;
    email: string;
  } | null>(null);

  useEffect(() => {
    const customer = localStorage.getItem("customer");
    if (customer) {
      try {
        const parsed = JSON.parse(customer);
        setUser({
          firstname: parsed.firstname,
          lastname: parsed.lastname,
          email: parsed.email,
        });
      } catch {
        setUser(null);
      }
    }
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        profileRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !profileRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const statusConfig = portfolioStatus
    ? portfolioStatusMap[portfolioStatus]
    : undefined;

  return (
    <HeaderWrapper>
      <HeaderContainer isSidebarOpen={isSidebarOpen}>
        <LogoContainer>
          {icon}
          <LogoText>{title}</LogoText>
          <MetaContainer>
            {statusConfig && (
              <StatusTag
                label={statusConfig.label}
                variant={statusConfig.variant}
                icon={statusConfig.icon}
                tooltip={statusConfig.tooltip}
                size="medium"
              />
            )}
          </MetaContainer>
        </LogoContainer>
        <DateInfoContainer>
          {portfolioCreatedAt && (
            <InfoBlock>
              <InfoLabel>Data de criação</InfoLabel>
              <InfoContent>
                <CalendarIcon size={16} />
                <span>{portfolioCreatedAt}</span>
              </InfoContent>
            </InfoBlock>
          )}

          {portfolioCreatedAt && portfolioUpdatedAt && (
            <IoIosArrowForward color="#898989" size={24} />
          )}

          {portfolioUpdatedAt && (
            <InfoBlock>
              <InfoLabel>Última atualização</InfoLabel>
              <InfoContent>
                <CalendarIcon size={16} />
                <span>{portfolioUpdatedAt}</span>
              </InfoContent>
            </InfoBlock>
          )}
        </DateInfoContainer>
        <IconsContainer>
          {/* <IconWrapper>
                <TbBell size={24} />
                <NotificationDot />
              </IconWrapper> */}

          <div
            style={{
              position: "relative",
              display: "flex",
              alignItems: "center",
              gap: 10,
            }}
            onClick={toggleDropdown}
            ref={profileRef}
          >
            <ProfileCircle>
              <User size={24} />
            </ProfileCircle>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: 4,
                alignItems: "flex-start",
                margin: 0,
              }}
            >
              <h1
                style={{
                  fontSize: 16,
                  fontWeight: 600,
                  color: "#212121",
                  margin: 0,
                }}
              >
                {user ? `${user.firstname} ${user.lastname}` : ""}
              </h1>
              <p
                style={{
                  fontSize: 12,
                  fontWeight: 500,
                  color: "#898989",
                  margin: 0,
                }}
              >
                {user ? user.email : ""}
              </p>
            </div>
            <ChevronIcon
              as={ChevronDown}
              isOpen={isDropdownOpen}
              size={16}
              cursor="pointer"
            />

            <DropdownContainer ref={dropdownRef} isOpen={isDropdownOpen}>
              <DropdownContent>
                <DropdownItem onClick={handleLogout}>
                  <LogOut size={16} />
                  Sair
                </DropdownItem>
              </DropdownContent>
            </DropdownContainer>
          </div>
        </IconsContainer>
      </HeaderContainer>
    </HeaderWrapper>
  );
};

export default Header;
