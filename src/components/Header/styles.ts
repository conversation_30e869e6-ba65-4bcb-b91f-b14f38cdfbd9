import styled from "styled-components";

export const HeaderWrapper = styled.div`
  position: sticky;
  top: 0;
  z-index: 100;
`;

export const HeaderContainer = styled.header.withConfig({
  shouldForwardProp: (prop) => prop !== "isSidebarOpen",
})<{ isSidebarOpen: boolean }>`
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  height: 81px;
  padding: 0 28px;
  background-color: ${({ theme }) => theme.colors.background};
  border-bottom: 0.5px solid ${({ theme }) => theme.colors.neutral400};
`;

export const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const LogoText = styled.h1`
  font-size: 24px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.neutral900};
  margin: 0;
`;

export const IconsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

export const IconWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  color: #212121;
`;

export const NotificationDot = styled.div`
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: ${({ theme }) => theme.colors.error};
  border-radius: 50%;
  top: 1px;
  right: 3px;
`;

export const ProfileCircle = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 41px;
  height: 41px;
  border-radius: 50%;
  background-color: #f5f5f5;
  cursor: pointer;
  position: relative;
`;

export const MetaContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-left: 18px;
`;

export const DateInfoContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 24px;
`;

export const InfoBlock = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

export const InfoLabel = styled.span`
  font-size: 14px;
  font-weight: 400;
  color: ${({ theme }) => theme.colors.neutral800};
`;

export const InfoContent = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: ${({ theme }) => theme.colors.neutral900};
  font-weight: 500;
  background-color: ${({ theme }) => theme.colors.neutral600};
  border-radius: 50px;
  padding: 4px 8px;
`;

export const DropdownContainer = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== "isOpen",
})<{ isOpen: boolean }>`
  position: absolute;
  top: calc(100% + 16px);
  right: 0;
  background-color: ${({ theme }) => theme.colors.neutral100};
  border-radius: 8px;
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.05);
  width: 200px;
  z-index: 10;
  border: 1px solid ${({ theme }) => theme.colors.neutral400};
  opacity: ${({ isOpen }) => (isOpen ? 1 : 0)};
  transform: ${({ isOpen }) =>
    isOpen ? "translateY(0px)" : "translateY(-10px)"};
  visibility: ${({ isOpen }) => (isOpen ? "visible" : "hidden")};
  pointer-events: ${({ isOpen }) => (isOpen ? "auto" : "none")};
  transition:
    opacity 0.2s ease,
    transform 0.2s ease,
    visibility 0.2s ease;

  &::before {
    content: "";
    position: absolute;
    top: -8px;
    right: 24px;
    border: 1px solid ${({ theme }) => theme.colors.neutral400};
    width: 16px;
    height: 16px;
    background-color: ${({ theme }) => theme.colors.neutral100};
    transform: rotate(45deg);
    border-top-left-radius: 2px;
    box-shadow: 0 4px 4px rgba(0, 0, 0, 0.05);
    z-index: -1;
  }
`;

export const DropdownContent = styled.div`
  position: relative;
  width: 100%;
  background-color: ${({ theme }) => theme.colors.neutral100};
  border-radius: 8px;
  overflow: hidden;
  z-index: 1;
`;

export const DropdownArrow = styled.div`
  position: absolute;
  top: -8px;
  right: 12px;
  width: 16px;
  height: 16px;
  background-color: ${({ theme }) => theme.colors.neutral100};
  transform: rotate(45deg);
  box-shadow: -2px -2px 3px rgba(0, 0, 0, 0.05);
`;

export const DropdownItem = styled.div`
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
  color: ${({ theme }) => theme.colors.neutral900};
  font-size: 15px;

  &:hover {
    background-color: ${({ theme }) => theme.colors.neutral100};
  }

  svg {
    margin-right: 12px;
    color: ${({ theme }) => theme.colors.neutral700};
  }
`;

export const ChevronIcon = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== "isOpen",
})<{ isOpen: boolean }>`
  transition: transform 0.3s ease;
  color: ${({ theme }) => theme.colors.neutral700};
  transform: ${({ isOpen }) => (isOpen ? "rotate(180deg)" : "rotate(0deg)")};
`;
