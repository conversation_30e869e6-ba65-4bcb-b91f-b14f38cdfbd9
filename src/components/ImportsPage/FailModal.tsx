/* eslint-disable @typescript-eslint/no-explicit-any */
import Modal from "@/components/Modal";
import Button from "@/components/Button";
import { CircleArrowDown } from "lucide-react";
import { Table } from "@/components/Table";

type FailModalProps = {
  isOpen: boolean;
  onClose: () => void;
  failRows: Array<{ line: number; reason: string }>;
  failRowsLoading: boolean;
  failRowsPaginated: Array<{ line: number; reason: string }>;
  failPageIndex: number;
  failPageSize: number;
  setFailPageIndex: (v: number) => void;
  setFailPageSize: (v: number) => void;
  onDownload: () => void;
};

export default function FailModal({
  isOpen,
  onClose,
  failRows,
  failRowsLoading,
  failRowsPaginated,
  failPageIndex,
  failPageSize,
  setFailPageIndex,
  setFailPageSize,
  onDownload,
}: FailModalProps) {
  return (
    <Modal
      isOpen={isOpen}
      maxWidth="774px"
      onClose={onClose}
      title="Falharam"
      headerSubtitle={
        failRows.length > 0 ? `(${failRows.length} itens)` : undefined
      }
      headerRightContent={
        failRows.length > 0 && (
          <Button
            label="Download"
            icon={<CircleArrowDown size={20} />}
            variant="primary"
            size="md"
            onClick={onDownload}
          />
        )
      }
    >
      <Table
        columns={[
          {
            accessorKey: "line",
            header: "Linha",
            cell: (info: any) => info.getValue(),
          },
          {
            accessorKey: "reason",
            header: "Motivo",
            cell: (info: any) => info.getValue(),
          },
        ]}
        data={failRowsPaginated}
        totalCount={failRows.length}
        pageIndex={failPageIndex}
        pageSize={failPageSize}
        isLoading={failRowsLoading}
        onPageChange={(page, size) => {
          setFailPageIndex(page);
          setFailPageSize(size);
        }}
      />
    </Modal>
  );
}
