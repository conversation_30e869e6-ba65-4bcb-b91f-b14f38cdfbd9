/* eslint-disable @typescript-eslint/no-explicit-any */
import EmptyState from "@/components/EmptyState";
import styled from "styled-components";
import CardSearch from "../Card/CardSearch/CardSearch";
import CardPortfolioSkeleton from "../Card/CardSearch/CardSearchSkeleton";

const CardList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(310px, 359px));
  gap: 16px;
  margin-top: 40px;

  @media (max-width: 1450px) {
    grid-template-columns: repeat(auto-fit, minmax(310px, 342px));
  }
  & > * {
    flex: 1 1 300px;
    min-width: 340px;
    max-width: 359px;
  }
`;

type ImportsCardListProps = {
  data: any[];
  allData: any[];
  isLoading: boolean;
};

function ImportsCardList({
  data,
  allData,
  isLoading = true,
}: ImportsCardListProps) {
  if (isLoading) {
    return (
      <CardList>
        {Array.from({ length: 6 }).map((_, i) => (
          <CardPortfolioSkeleton key={i} variant="import" />
        ))}
      </CardList>
    );
  }
  if (!allData || allData.length === 0) {
    return (
      <EmptyState
        title="Você não possui importações"
        subtitle='Importe seu primeiro portfólio no botão "Importar" logo acima.'
      />
    );
  }

  if (!data || data.length === 0) {
    return (
      <EmptyState
        title="Nenhum portfólio encontrado"
        subtitle="Tente alterar os filtros ou procurar por outro portfólio."
      />
    );
  }

  return (
    <CardList>
      {data.map((item) => (
        <CardSearch
          key={item.id}
          header={item.header}
          sections={item.sections}
          footer={item.footer}
        />
      ))}
    </CardList>
  );
}

export default ImportsCardList;
