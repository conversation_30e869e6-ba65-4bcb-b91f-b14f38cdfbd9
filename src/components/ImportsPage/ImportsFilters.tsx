import styled from "styled-components";
import TextInput from "@/components/Form/Input";
import SelectInput from "@/components/Form/SelectInput";
import Button from "@/components/Button";
import { FiSearch } from "react-icons/fi";
import { Download } from "lucide-react";
import { theme } from "@/styles/theme";
import { useRouter } from "next/navigation";
import { portfolioStatusOptions } from "@/constants/portfolioStatusMap";

const SectionFilters = styled.div`
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
  gap: 24px;
`;

const InputArea = styled.div`
  flex: 1 1 0;
  min-width: 550px;
`;

const InputWrapper = styled.div`
  display: flex;
  flex-direction: row;
  gap: 16px;
`;

type ImportsFiltersProps = {
  searchTerm: string;
  setSearchTerm: (v: string) => void;
  selectedEmbedded: string;
  setSelectedEmbedded: (v: string) => void;
  selectedExecutionStatus: string;
  setSelectedExecutionStatus: (v: string) => void;
  currentTab: string;
};

const options = [{ label: "Portfólio", value: "all" }];

export default function ImportsFilters({
  searchTerm,
  setSearchTerm,
  selectedEmbedded,
  setSelectedEmbedded,
  selectedExecutionStatus,
  setSelectedExecutionStatus,
  currentTab,
}: ImportsFiltersProps) {
  const router = useRouter();

  return (
    <SectionFilters>
      <InputWrapper>
        <InputArea>
          <TextInput
            placeholder="Pesquisar por nome"
            iconLeft={<FiSearch size={24} color={theme.colors.labelInput} />}
            selectLeft={
              <SelectInput
                variant="embedded"
                value={selectedEmbedded}
                placeholder="Portfólio"
                options={options}
                onChange={setSelectedEmbedded}
              />
            }
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </InputArea>
        {currentTab === "todas" && (
          <div style={{ minWidth: 203, width: "100%" }}>
            <SelectInput
              variant="standalone"
              labelInside="Status"
              options={portfolioStatusOptions}
              value={selectedExecutionStatus}
              onChange={setSelectedExecutionStatus}
            />
          </div>
        )}
      </InputWrapper>
      <Button
        variant="primary"
        size="lg"
        label="Importar"
        iconPosition="left"
        icon={<Download size={24} />}
        minWidth={152}
        onClick={() => router.push("/imports/create")}
      />
    </SectionFilters>
  );
}
