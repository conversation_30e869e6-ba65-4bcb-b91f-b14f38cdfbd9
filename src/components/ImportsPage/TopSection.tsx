import styled from "styled-components";
import Tabs from "@/components/Tabs";

const TopSectionWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 40px;
`;

type TopSectionProps = {
  description?: React.ReactNode;
  tabs?: {
    items: Array<{ label: string; value: string; count?: number }>;
    value: string;
    onChange: (value: string) => void;
  };
  children?: React.ReactNode;
};

export default function TopSection({
  description,
  tabs,
  children,
}: TopSectionProps) {
  return (
    <TopSectionWrapper>
      {description && <div>{description}</div>}
      {tabs && (
        <Tabs tabs={tabs.items} value={tabs.value} onChange={tabs.onChange} />
      )}
      {children}
    </TopSectionWrapper>
  );
}
