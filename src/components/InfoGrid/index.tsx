import React from "react";
import {
  InfoGridWrapper,
  InfoGridRow1,
  InfoGridRow2,
  InfoGridCell,
  InfoLabel,
  InfoValue,
} from "./styles";
import { InfoGridProps } from "./types";

export function InfoGrid({ rows }: InfoGridProps) {
  return (
    <InfoGridWrapper>
      <InfoGridRow1>
        {rows[0].map((cell, i) => (
          <InfoGridCell key={i}>
            <InfoLabel>{cell.label}</InfoLabel>
            <InfoValue $color={cell.valueColor}>
              {cell.value}
              {cell.icon && <span style={{ marginLeft: 4 }}>{cell.icon}</span>}
            </InfoValue>
          </InfoGridCell>
        ))}
      </InfoGridRow1>
      <InfoGridRow2>
        {rows[1].map((cell, i) => (
          <InfoGridCell key={i}>
            <InfoLabel>{cell.label}</InfoLabel>
            <InfoValue
              $color={cell.valueColor}
              style={cell.onClick ? { cursor: "pointer" } : {}}
              onClick={cell.onClick}
            >
              {cell.value}
              {cell.icon && <span style={{ marginLeft: 4 }}>{cell.icon}</span>}
            </InfoValue>
          </InfoGridCell>
        ))}
      </InfoGridRow2>
    </InfoGridWrapper>
  );
}
