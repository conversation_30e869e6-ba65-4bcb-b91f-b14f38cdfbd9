import styled from "styled-components";
import { theme } from "@/styles/theme";
export const InfoGridWrapper = styled.div`
  display: flex;
  flex-direction: column;
  max-width: 249px;
  gap: 16px;
  margin-left: 30px;
`;

export const InfoGridRow1 = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
`;

export const InfoGridRow2 = styled.div`
  display: flex;
  flex-direction: row;
  gap: 86px;
`;

export const InfoGridCell = styled.div`
  min-width: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
`;

export const InfoLabel = styled.span`
  color: ${theme.colors.neutral700};
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 2px;
  min-width: 84px;
  line-height: 1.2;
`;

export const InfoValue = styled.span<{ $color?: string }>`
  color: ${({ $color }) => $color || "#222"};
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  line-height: 1.1;
`;
