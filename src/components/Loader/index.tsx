// components/Loader/index.tsx
import styled, { keyframes } from "styled-components";

const spin = keyframes`
  to { transform: rotate(360deg); }
`;

// const Overlay = styled.div`
//   position: absolute;
//   inset: 0;
//   background: rgba(255, 255, 255, 0.6);
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   z-index: 10;
// `;

const Spinner = styled.div`
  width: 32px;
  height: 32px;
  border: 4px solid ${({ theme }) => theme.colors.primary}33;
  border-top-color: ${({ theme }) => theme.colors.primary};
  border-radius: 50%;
  animation: ${spin} 0.8s linear infinite;
`;

export const Loader = () => <Spinner />;
