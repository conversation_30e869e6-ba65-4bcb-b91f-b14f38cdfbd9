"use client";
import { useState, ChangeEventHand<PERSON> } from "react";
import {
  EyeTwoTone,
  EyeInvisibleOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import { Form, Popover } from "antd";
import { InputWrapper, CustomInput, CustomNumberInput } from "./styles";

type Props = {
  name?: string;
  label?: string;
  type?: string;
  placeholder?: string;
  rules?: object[];
  passwordInput?: boolean;
  onChange?: ChangeEventHandler<HTMLInputElement>;
  disabled?: boolean;
  readonly?: boolean;
  initialValue?: string | number;
  defaultValue?: string | number;
  infoText?: string;
  min?: number;
  max?: number;
  invisibleInput?: boolean;
  value?: string;
  onPressEnter?: () => void;
  isTalkia?: boolean;
  bgColor?: string;
  errorMessage?: string;
};

export default function Input({
  name,
  label,
  type,
  placeholder,
  rules,
  passwordInput,
  onChange,
  value,
  disabled,
  readonly,
  initialValue,
  defaultValue,
  infoText,
  min,
  max,
  invisibleInput,
  onPressEnter,
  bgColor,
  errorMessage,
}: Props) {
  const [visibility, setVisibility] = useState(false);

  function InfoPopOver() {
    return (
      <Popover placement="top" content={infoText} title="Obs:" trigger="hover">
        <InfoCircleOutlined />
      </Popover>
    );
  }

  return (
    <InputWrapper invisibleInput={invisibleInput} $bgColor={bgColor}>
      <Form.Item
        name={name}
        rules={rules}
        style={{ width: "100%" }}
        initialValue={initialValue}
        help={errorMessage}
      >
        {!passwordInput && type !== "number" && (
          <CustomInput
            type={type}
            placeholder={placeholder}
            onChange={onChange}
            disabled={disabled}
            $readonly={readonly}
            defaultValue={defaultValue}
            value={value}
            onPressEnter={onPressEnter}
          />
        )}
        {passwordInput && (
          <CustomInput
            type={visibility ? "text" : "password"}
            name={name}
            placeholder={placeholder}
            onChange={onChange}
            disabled={disabled}
            $bgColor={bgColor}
          />
        )}
        {type === "number" && (
          <CustomNumberInput
            type={type}
            placeholder={placeholder}
            onChange={onChange}
            disabled={disabled}
            $readonly={readonly}
            defaultValue={defaultValue}
            min={min}
            max={max}
            className="input"
            $bgColor={bgColor}
          />
        )}
      </Form.Item>
      {label && (
        <label htmlFor={name}>
          {label} {infoText ? <InfoPopOver /> : null}
        </label>
      )}
      {passwordInput && (
        <button
          type="button"
          onClick={() => setVisibility((prevState) => !prevState)}
        >
          {visibility ? <EyeTwoTone /> : <EyeInvisibleOutlined />}
        </button>
      )}
    </InputWrapper>
  );
}

export { Input };
