"use client";
import { Change<PERSON>ventHand<PERSON> } from "react";
import styled, { css } from "styled-components";
import { Input, InputNumber } from "antd";

type Props = {
  $readonly?: boolean;
  onChange?: ChangeEventHandler<HTMLInputElement>;
  $isTalkia?: boolean;
  $bgColor?: string;
};

export const CustomInput = styled(Input)<Props>`
  position: relative;
  width: 100%;
  padding: 8px;
  border-radius: 5px;
  border: 1px solid currentColor;
  font-family: Switzer;

  &.ant-input {
    font-size: 16px;
  }
  & ~ button:focus-visible {
    outline: unset;
  }

  &:focus,
  .ant-input:hover,
  .ant-input:focus {
    outline: 1px solid currentColor;
    border-color: currentColor;
  }
`;
export const CustomNumberInput = styled(InputNumber)<Props>`
  position: relative;
  width: 100%;
  padding: 8px;
  border-radius: 5px;
  background-color: ${({ $bgColor }) => ($bgColor ? $bgColor : "transparent")};
  border: 1px solid
    ${({ $readonly, disabled }) =>
      $readonly || disabled ? "lightgray" : "var(--primary-color)"};
  &.ant-input {
    font-size: 16px;
  }
  & ~ button:focus-visible {
    outline: unset;
  }

  &:focus,
  .ant-input:hover,
  .ant-input:focus {
    outline: 1px solid
      ${({ $isTalkia }) =>
        $isTalkia ? "var(--talkia-black)" : "var(--primary-color)"};
  }
`;

export const InputWrapper = styled.div<{
  invisibleInput?: boolean;
  $isTalkia?: boolean;
  $bgColor?: string;
}>`
  ${({ invisibleInput }) =>
    invisibleInput &&
    css`
      display: none;
    `}
  position: relative;
  font-family: Switzer;
  color: #111c9d;
  width: 100%;
  font-size: 16px;
  line-height: 17px;
  font-weight: 400;
  ${({ $isTalkia }) =>
    $isTalkia &&
    css`
      font-family: var(--font-roboto-talkia);
    `};

  .ant-form-item {
    margin-bottom: unset;
  }
  .ant-form-item-with-help .ant-form-item-has-error ~ label {
    color: #ff4d4f;
  }
  .ant-form-item .ant-form-item-explain-error {
    text-align: left;
  }

  .ant-form-item ~ button,
  .input ~ button {
    position: absolute;
    top: 20px;
    transform: translateY(-50%);
    right: 16px;
    background-color: ${({ $bgColor }) =>
      $bgColor ? $bgColor : "transparent"};
    border: none;
  }
  .ant-form-item ~ label,
  .input ~ label {
    position: absolute;
    top: 0;
    left: 12px;
    transform: translateY(-50%);
    color: ${({ $isTalkia }) =>
      $isTalkia ? "var(--talkia-black)" : "var(--primary-color)"};
    font-size: 14px;
    line-height: 17px;
    font-weight: 400;
    padding: 4px;

    &:before {
      content: "";
      background-color: #fff;
      height: 3px;
      width: 100%;
      display: flex;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 0;
      z-index: -1;
    }
  }

  @media only screen and (max-width: 576px) {
    .ant-form-item ~ label,
    .input ~ label {
      font-size: 16px;
    }
  }
`;
