import { useEffect } from "react";
import ReactDOM from "react-dom";
import { Overlay, Drawer, Header, Content, Footer, Box } from "./styles";
import { ModalProps } from "./types";
import Button from "@/components/Button";

const Modal = ({
  isOpen,
  onClose,
  title,
  headerSubtitle,
  headerRightContent,
  maxWidth,
  children,
  footer,
  padding,
  display,
  overflowY,
}: ModalProps) => {
  useEffect(() => {
    const handleKey = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };
    if (isOpen) document.addEventListener("keydown", handleKey);
    return () => document.removeEventListener("keydown", handleKey);
  }, [isOpen, onClose]);

  return ReactDOM.createPortal(
    <>
      <Overlay isOpen={isOpen} onClick={onClose} />
      <Drawer
        maxWidth={maxWidth}
        isOpen={isOpen}
        role="dialog"
        aria-modal="true"
      >
        {title && (
          <Header>
            <Box>
              <h2>{title}</h2>
              {headerSubtitle && <span>{headerSubtitle}</span>}
            </Box>
            {headerRightContent && <div>{headerRightContent}</div>}
          </Header>
        )}
        <Content display={display} padding={padding} overflowY={overflowY}>
          {children}
        </Content>
        {footer !== false && (
          <Footer>
            {footer ?? (
              <Button
                label="Voltar"
                variant="outlined"
                size="md"
                onClick={onClose}
              />
            )}
          </Footer>
        )}
      </Drawer>
    </>,
    document.body
  );
};

export default Modal;
