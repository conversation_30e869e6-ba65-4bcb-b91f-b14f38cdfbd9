import styled, { css } from "styled-components";

export const Overlay = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== "isOpen",
})<{ isOpen: boolean }>`
  ${({ isOpen }) =>
    isOpen
      ? css`
          display: block;
        `
      : css`
          display: none;
        `}

  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
  z-index: 1000;
`;

export const Drawer = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== "maxWidth" && prop !== "isOpen",
})<{
  maxWidth?: string;
  isOpen: boolean;
}>`
  position: fixed;
  top: 0;
  right: 0;
  height: 100%;
  width: ${({ maxWidth }) =>
    maxWidth
      ? `clamp(320px, 92vw, ${maxWidth})`
      : "clamp(320px, 92vw, 1470px)"};
  max-width: ${({ maxWidth }) => maxWidth || "1470px"};
  transform: translateX(${({ isOpen }) => (isOpen ? "0" : "100%")});
  transition: transform 0.3s ease;
  background-color: ${({ theme }) => theme.colors.background};
  box-shadow: -4px 0 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  z-index: 1001;
`;

export const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.neutral300};
  margin: 0 16px;
`;

export const Box = styled.div`
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  gap: 4px;
  h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: ${({ theme }) => theme.colors.neutral900};
  }
  span {
    font-size: 18px;
    font-weight: 600;
    color: ${({ theme }) => theme.colors.neutral700};
  }
`;

export const Content = styled.div.withConfig({
  shouldForwardProp: (prop) =>
    prop !== "padding" && prop !== "display" && prop !== "overflowY",
})<{ padding?: string; display?: string; overflowY?: string }>`
  flex: 1;
  overflow-y: ${({ overflowY }) => overflowY || "auto"};
  padding: ${({ padding }) => padding || "16px 32px"};
  display: ${({ display }) => display || ""};
  min-width: 0;
`;

export const Footer = styled.div`
  padding: 16px 32px;
  border-top: 0.5px solid ${({ theme }) => theme.colors.neutral400};
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background-color: ${({ theme }) => theme.colors.neutral200};
`;
