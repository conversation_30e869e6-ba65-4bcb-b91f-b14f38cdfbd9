import { CSSProperties, ReactNode } from "react";
import { Dropdown } from "@/components/Dropdown";
import { MoreOptionsIcon } from "@/assets/icons/more-options-icon";
import { Wrapper } from "./styles";
import { theme } from "@/styles/theme";

export function MoreOptionsButton({
  navItems,
  isDisabled,
  placement,
  children,
  style,
  arrowPlacement,
  minWidth = 200,
}: {
  navItems: {
    key: string;
    icon?: ReactNode;
    label: ReactNode;
    portfolioId?: string;
    onClick: () => void;
    minWidth?: number;
    fontWeight?: number;
  }[];
  isDisabled?: boolean;
  placement?: "top" | "topRight" | "bottom" | "bottomRight";
  children?: ReactNode;
  style?: CSSProperties;
  arrowPlacement?: "left" | "right" | "center";
  portfolioId?: string;
  minWidth?: number;
}) {
  const dropdownItems = navItems?.map((item) => ({
    key: item.key,
    icon: item.icon,
    label: item.label,
    portfolioId: item.portfolioId,
    onClick: item.onClick,
    disabled: isDisabled,
    style: {
      display: "flex",
      alignItems: "center",
      gap: "8px",
      fontSize: "14px",
      width: "100%",
      height: "100%",
      maxHeight: "34px",
      minWidth: item.minWidth,
      fontWeight: item.fontWeight,
    },
  }));

  return (
    <Wrapper>
      <Dropdown
        trigger={
          <span
            style={
              isDisabled
                ? {
                    cursor: "not-allowed",
                    color: "#B5B6C2",
                    userSelect: "none",
                    ...style,
                  }
                : { cursor: "pointer", userSelect: "none", ...style }
            }
          >
            {children || <MoreOptionsIcon color={theme.colors.labelInput} />}
          </span>
        }
        items={dropdownItems}
        placement={placement}
        minWidth={minWidth}
        arrowPlacement={arrowPlacement}
      />
    </Wrapper>
  );
}
