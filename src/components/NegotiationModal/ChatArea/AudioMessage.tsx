// src/components/NegotiationModal/ChatArea/AudioMessage.tsx
"use client";
import React, { useRef, useState, useEffect } from "react";
import styled from "styled-components";
import { IoIosPlay, IoIosPause } from "react-icons/io";
import { FiVolume2, FiVolumeX } from "react-icons/fi";

const AudioBubble = styled.div<{ role: "user" | "assistant" }>`
  background: ${({ role, theme }) =>
    role === "user" ? theme.colors.neutral100 : theme.colors.disabled};
  border: 1px solid ${({ theme }) => theme.colors.neutral400};
  border-radius: 50px;
  padding: 12px 24px;
  min-width: 350px;
  max-width: 483px;
  box-shadow:
    0px 1px 2px rgba(0, 0, 0, 0.03),
    0px 1px 3px rgba(0, 0, 0, 0.03),
    0px 3px 5px rgba(0, 0, 0, 0.03);

  align-self: ${({ role }) => (role === "user" ? "flex-start" : "flex-end")};
`;

const Controls = styled.div`
  display: flex;
  align-items: center;
  gap: 20px;
  width: 100%;
  min-width: 450px;
  max-width: 450px;
`;

const PlayButton = styled.button`
  cursor: pointer;
  display: flex;
  color: ${({ theme }) => theme.colors.success};
`;

const ProgressContainer = styled.div`
  height: 12px;
  position: relative;
  background: ${({ theme }) => theme.colors.disabled};
  border-radius: 50px;
  overflow: hidden;
  width: 312px;
  min-width: 312px;
  max-width: 312px;
  flex: 0 0 auto;
`;

const Progress = styled.div`
  height: 100%;
  border-radius: 50px;
  background: ${({ theme }) => theme.colors.labelInput};
  transition: width 0.3s cubic-bezier(0.7, 0.3, 0.3, 1);
`;

const Time = styled.span`
  font-size: 18px;
  color: ${({ theme }) => theme.colors.neutral900};
  font-weight: 600;
  min-width: 40px;
  width: 40px;
  text-align: right;
  flex-shrink: 0;
`;

type AudioMessageProps = {
  src: string;
  role: "user" | "assistant";
};

export function AudioMessage({ src, role }: AudioMessageProps) {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [playing, setPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [muted, setMuted] = useState(false);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const onLoaded = () => setDuration(audio.duration);
    const onTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
      setProgress((audio.currentTime / audio.duration) * 100);
    };

    audio.addEventListener("loadedmetadata", onLoaded);
    audio.addEventListener("timeupdate", onTimeUpdate);
    return () => {
      audio.removeEventListener("loadedmetadata", onLoaded);
      audio.removeEventListener("timeupdate", onTimeUpdate);
    };
  }, []);

  const togglePlay = () => {
    const audio = audioRef.current;
    if (!audio) return;
    if (playing) {
      audio.pause();
      setPlaying(false);
    } else {
      audio.play();
      setPlaying(true);
    }
  };

  const fmt = (sec: number) => {
    const minutes = Math.floor(sec / 60);
    const seconds = Math.floor(sec % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  return (
    <AudioBubble role={role}>
      <audio
        ref={audioRef}
        src={src}
        muted={muted}
        style={{ display: "none" }}
      />

      <Controls>
        <Time>{playing ? fmt(currentTime) : fmt(duration)}</Time>
        <div style={{ display: "flex", alignItems: "center", gap: "4px" }}>
          <PlayButton onClick={togglePlay}>
            {playing ? <IoIosPause size={24} /> : <IoIosPlay size={24} />}
          </PlayButton>

          <ProgressContainer
            onClick={(e) => {
              const rect = (e.target as HTMLElement).getBoundingClientRect();
              const pct = (e.clientX - rect.left) / rect.width;
              if (audioRef.current) {
                audioRef.current.currentTime = pct * duration;
              }
            }}
          >
            <Progress style={{ width: `${progress}%` }} />
          </ProgressContainer>
        </div>
        <span
          style={{
            cursor: "pointer",
            display: "flex",
            minWidth: "28px",
            width: "28px",
          }}
          onClick={() => setMuted((prev) => !prev)}
        >
          {muted ? <FiVolumeX size={24} /> : <FiVolume2 size={24} />}
        </span>
      </Controls>
    </AudioBubble>
  );
}
