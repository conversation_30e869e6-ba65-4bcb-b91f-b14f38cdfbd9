// src/components/NegotiationModal/ChatArea/ChatSkeleton.tsx
import React from "react";
import styled, { keyframes } from "styled-components";

const pulse = keyframes`
  0% { background-color: #e0e0e0; }
  50% { background-color: #f0f0f0; }
  100% { background-color: #e0e0e0; }
`;

const Row = styled.div<{
  align: "start" | "end";
  width: string;
  height: string;
}>`
  align-self: ${({ align }) => (align === "start" ? "flex-start" : "flex-end")};
  width: ${({ width }) => width};
  height: ${({ height }) => height};
  border-radius: 8px;
  margin: 8px 0;
  animation: ${pulse} 1.2s ease-in-out infinite;
`;

export function ChatSkeleton({
  rows = 4,
  width = "556px",
  height = "95px",
}: {
  rows?: number;
  width?: string;
  height?: string;
}) {
  return (
    <>
      {Array.from({ length: rows }).map((_, i) => (
        <Row
          key={i}
          align={i % 2 === 0 ? "end" : "start"}
          width={width}
          height={height}
        />
      ))}
    </>
  );
}
