import { DataBox, DetailsRow, DetailsCol } from "./styles";
import { ExpandableProfileBox } from "../ProfileBox";
import StatusTag from "@/components/StatusTag";
import { portfolioItemStatusMap } from "@/constants/portfolioItemStatusMap";
import { IPortfolioItem } from "@/types/portfolio";
import { formatPhone } from "@/utils/formatPhone";
import { formatDateDefault, timeAgo } from "@/utils/formatDate";
import { DetailsColumnSkeleton } from "./DetailsColumnSkeleton";

type Props = {
  item?: IPortfolioItem | null;
  loading?: boolean;
};

export function DetailsColumn({ item, loading }: Props) {
  if (loading) {
    return (
      <DetailsRow>
        <DetailsColumnSkeleton />
      </DetailsRow>
    );
  }
  if (!item) {
    return <div style={{ padding: 24 }}>Contato não encontrado.</div>;
  }

  function getCustomerName(customData?: Record<string, string>) {
    if (!customData) return undefined;
    const key = Object.keys(customData).find(
      (k) =>
        k.toUpperCase() === "NOME_DO_CLIENTE" ||
        k.toUpperCase() === "NOME_CLIENTE"
    );
    return key ? customData[key] : undefined;
  }

  const statusConfig = item.currentStatus
    ? portfolioItemStatusMap[item.currentStatus]
    : undefined;

  const lastInteractionDate = item.lastInteraction
    ? new Date(item.lastInteraction)
    : null;
  const waitingTime = item.waitingBusinessUserResponse
    ? lastInteractionDate
      ? timeAgo(lastInteractionDate)
      : "—"
    : "—";

  const customerName = getCustomerName(item.customData);

  return (
    <DetailsRow>
      <DetailsCol>
        {customerName && (
          <DataBox>
            <p>Nome completo</p>
            <b>{customerName}</b>
          </DataBox>
        )}
        <DataBox>
          <p>Telefone</p>
          <b>{item.phoneNumber ? formatPhone(item.phoneNumber) : "—"}</b>
        </DataBox>
        <DataBox>
          <p>Status</p>
          {statusConfig ? (
            <StatusTag
              variant={statusConfig.variant}
              label={statusConfig.label}
              tooltip={statusConfig.tooltip}
              icon={statusConfig.icon}
            />
          ) : (
            <span>—</span>
          )}
        </DataBox>
        <DataBox>
          <p>Última interação</p>
          <b>
            {item.lastInteraction
              ? formatDateDefault(item.lastInteraction)
              : "—"}
          </b>
        </DataBox>
        <DataBox>
          <p>Aguardando resposta</p>
          <b>
            {item.waitingBusinessUserResponse ? "Sim" : "Não"}
            {waitingTime !== "—" && (
              <span style={{ marginLeft: 6, color: "#888" }}>
                {waitingTime}
              </span>
            )}
          </b>
        </DataBox>
        <DataBox>
          <p>Insights da IA</p>
          <b>{item.insights || "Não há dados"}</b>
        </DataBox>
      </DetailsCol>
      <ExpandableProfileBox profile={item.customData || {}} />
    </DetailsRow>
  );
}
