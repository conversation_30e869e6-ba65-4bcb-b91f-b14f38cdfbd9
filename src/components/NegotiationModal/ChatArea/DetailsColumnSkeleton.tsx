import React from "react";
import styled, { keyframes } from "styled-components";

const pulse = keyframes`
  0% { background-color: #e0e0e0; }
  50% { background-color: #f0f0f0; }
  100% { background-color: #e0e0e0; }
`;

const SkeletonRow = styled.div<{ width: string; height: string }>`
  width: ${({ width }) => width};
  height: ${({ height }) => height};
  border-radius: 4px;
  animation: ${pulse} 1.2s ease-in-out infinite;
`;

const DetailsSkeletonWrapper = styled.div`
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  padding: 8px;
`;

const DetailsSkeletonCol = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 231px;
  padding: 24px 40px 40px 16px;
`;

export function DetailsColumnSkeleton() {
  return (
    <DetailsSkeletonWrapper>
      <DetailsSkeletonCol>
        <SkeletonRow width="150px" height="16px" />
        <SkeletonRow width="180px" height="24px" />

        <SkeletonRow width="100px" height="16px" />
        <SkeletonRow width="140px" height="24px" />

        <SkeletonRow width="100px" height="16px" />
        <SkeletonRow width="80px" height="24px" />

        <SkeletonRow width="120px" height="16px" />
        <SkeletonRow width="160px" height="24px" />

        <SkeletonRow width="140px" height="16px" />
        <SkeletonRow width="100px" height="24px" />

        <SkeletonRow width="120px" height="16px" />
        <SkeletonRow width="200px" height="24px" />
      </DetailsSkeletonCol>
    </DetailsSkeletonWrapper>
  );
}
