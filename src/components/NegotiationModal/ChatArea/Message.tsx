import React from "react";
import { Message as TextBubble } from "./styles";
import styled from "styled-components";
import { IoMdCheckmark } from "react-icons/io";
import { theme } from "@/styles/theme";
import { BsFiletypePdf } from "react-icons/bs";
import { CircleArrowDown } from "lucide-react";
import { AudioMessage } from "./AudioMessage";

import {
  downloadImageFile,
  downloadPdfFile,
} from "@/services/portfolioItemService";

export type MessageProps =
  | {
      id: string;
      type: "text";
      role: "user" | "assistant";
      text: string;
      name?: string;
      date?: string;
      sent?: boolean;
      sent_at?: string;
      time_to_go?: string;
      isScheduled: boolean;
    }
  | {
      id: string;
      type: "audio";
      role: "user" | "assistant";
      src: string;
      name?: string;
      date?: string;
      sent?: boolean;
      sent_at?: string;
      time_to_go?: string;
      audioSrc?: string;
      isScheduled: boolean;
      portfolioItemId?: string;
    }
  | {
      id: string;
      type: "image";
      role: "user" | "assistant";
      url: string;
      filename: string;
      messageText?: string;
      name?: string;
      date?: string;
      sent?: boolean;
      sent_at?: string;
      time_to_go?: string;
      isScheduled: boolean;
      imageSrc?: string;
      portfolioItemId?: string;
    }
  | {
      id: string;
      type: "file";
      role: "user" | "assistant";
      url: string;
      filename: string;
      messageText?: string;
      name?: string;
      date?: string;
      sent?: boolean;
      sent_at?: string;
      time_to_go?: string;
      isScheduled: boolean;
      portfolioItemId?: string;
    };

const MessageWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Meta = styled.div<{ role: "user" | "assistant" }>`
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 16px;
  align-items: ${({ role }) => (role === "user" ? "flex-start" : "flex-end")};

  p {
    display: flex;
    align-items: center;
    gap: 6px;
  }
`;

const FileBubble = styled.div<{ role: "user" | "assistant" }>`
  background: ${({ role, theme }) =>
    role === "user" ? theme.colors.neutral100 : "#F5F6FF"};
  border: ${({ role }) =>
    role === "user"
      ? `1px solid ${theme.colors.neutral400}`
      : "1px solid #D5DEFF"};
  border-radius: 9px;
  padding: 8px;
  max-width: 371px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  align-self: ${({ role }) => (role === "user" ? "flex-start" : "flex-end")};
`;

const FileBox = styled.div`
  display: flex;
  background: ${({ theme }) => theme.colors.background};
  align-items: center;
  gap: 8px;
  max-width: 355px;
  min-height: 72px;
  border-radius: 8px;
  width: 100%;
  padding: 12px;

  span {
    font-size: 16px;
    font-weight: 600;
    color: ${({ theme }) => theme.colors.neutral900};
    word-break: break-all;
  }
`;

const MessageFileBox = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 20px;
  padding: 4px 8px;

  p {
    font-size: 16px;
    font-weight: 400;
    color: ${({ theme }) => theme.colors.neutral900};
    width: 100%;
  }

  svg {
    cursor: pointer;
  }
`;

export function Message(props: MessageProps) {
  const { role, name, date } = props;

  const renderBubble = () => {
    switch (props.type) {
      case "text":
        return <TextBubble role={role}>{props.text}</TextBubble>;

      case "audio":
        return <AudioMessage src={props.audioSrc || props.src} role={role} />;

      case "image":
        const src = props.imageSrc || props.url;
        if (!src) {
          return (
            <FileBubble role={role}>
              <FileBox>
                <span
                  style={{
                    color: theme.colors.neutral700,
                    fontStyle: "italic",
                  }}
                >
                  (imagem indisponível)
                </span>
              </FileBox>
            </FileBubble>
          );
        }
        return (
          <FileBubble role={role}>
            <FileBox>
              <img
                src={src}
                alt={props.filename}
                width={100}
                height={75}
                style={{
                  borderRadius: 6,
                  objectFit: "cover",
                  cursor: "pointer",
                }}
                onClick={() => {}}
              />
            </FileBox>
            <MessageFileBox>
              {/* //TODO: Add message after fix backend */}
              {/* {props.messageText ? (
                <p>{props.messageText}</p>
              ) : (
                <div style={{ flex: 1 }} />
              )} */}
              <CircleArrowDown
                size={18}
                color={theme.colors.link}
                onClick={async () => {
                  if (props.portfolioItemId) {
                    try {
                      const blob = await downloadImageFile(
                        props.portfolioItemId,
                        props.url
                      );
                      const blobUrl = URL.createObjectURL(blob);
                      const link = document.createElement("a");
                      link.href = blobUrl;
                      link.download = props.filename;
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                      URL.revokeObjectURL(blobUrl);
                    } catch (error) {
                      console.error("Error downloading image:", error);
                    }
                  }
                }}
                style={{ cursor: "pointer" }}
              />
            </MessageFileBox>
          </FileBubble>
        );

      case "file":
        return (
          <FileBubble role={role}>
            <FileBox>
              <BsFiletypePdf size={40} color={theme.colors.neutral800} />
              <span>{props.filename}</span>
            </FileBox>
            <MessageFileBox>
              {/* //TODO: Add message after fix backend */}
              {/* {props.messageText ? (
                <p>{props.messageText}</p>
              ) : (
                <div style={{ flex: 1 }} />
              )} */}
              <CircleArrowDown
                size={24}
                color={theme.colors.link}
                onClick={async () => {
                  if (props.portfolioItemId) {
                    try {
                      const blob = await downloadPdfFile(
                        props.portfolioItemId,
                        props.url
                      );
                      const blobUrl = URL.createObjectURL(blob);
                      const link = document.createElement("a");
                      link.href = blobUrl;
                      link.download = props.filename;
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                      URL.revokeObjectURL(blobUrl);
                    } catch (error) {
                      console.error("Error downloading image:", error);
                    }
                  }
                }}
                style={{ cursor: "pointer" }}
              />
            </MessageFileBox>
          </FileBubble>
        );
    }
  };

  return (
    <MessageWrapper>
      {renderBubble()}
      {(name || date) && (
        <Meta role={role}>
          {role === "user" ? (
            <p>
              <b>{name}</b> • <span>{date}</span>
            </p>
          ) : (
            <p>
              {props.isScheduled ? (
                <>
                  <IoMdCheckmark color={theme.colors.neutral700} />
                  <span>{date}</span>
                </>
              ) : (
                <>
                  <IoMdCheckmark color={theme.colors.success} />
                  <span>{date}</span>
                </>
              )}
              • <b>{name}</b>
            </p>
          )}
        </Meta>
      )}
    </MessageWrapper>
  );
}
