// src/components/NegotiationModal/ChatArea/index.tsx
"use client";
import React, { useEffect, useRef, useState } from "react";
import {
  ChatAreaWrapper,
  ChatBox,
  ChatMessages,
  ChatInputRow,
  HiddenInput,
  FilePreviewBox,
  RemoveFileButton,
  FileName,
} from "./styles";
import { Message } from "./Message";
import { DetailsColumn } from "./DetailsColumn";
import Button from "@/components/Button";
import { LuSend } from "react-icons/lu";
import { FiPaperclip, FiX } from "react-icons/fi";
import { theme } from "@/styles/theme";
import { BsFiletypePdf } from "react-icons/bs";
import { mapApiMessageToMessageProps } from "@/utils/messageMapper";
import {
  downloadAudioFile,
  downloadImageFile,
  getConversationHistory,
  sendMessage,
} from "@/services/portfolioItemService";
import { MessageProps } from "./Message";
import styled, { keyframes } from "styled-components";
import { usePortfolioItem } from "@/hooks/usePortfolioItem";
import { ChatSkeleton } from "./ChatSkeleton";
import { getMessageKey } from "@/utils/getMessageKey";
type ChatAreaProps = {
  negotiationId: string;
};

const Overlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
`;

const spin = keyframes`
  to { transform: rotate(360deg); }
`;

const Spinner = styled.div`
  width: 48px;
  height: 48px;
  border: 5px solid #ccc;
  border-top: 5px solid ${({ theme }) => theme.colors.primary};
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
`;

export function ChatArea({ negotiationId }: ChatAreaProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const audioBlobRef = useRef<Record<string, string>>({});
  const imageBlobRef = useRef<Record<string, string>>({});

  const [messages, setMessages] = useState<MessageProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [audioBlobMap, setAudioBlobMap] = useState<Record<string, string>>({});
  const [imageBlobMap, setImageBlobMap] = useState<Record<string, string>>({});
  const [messageText, setMessageText] = useState("");
  const [sending, setSending] = useState(false);

  function getMessageType(file: File | null) {
    if (!file) return "TEXT";
    return file.type || "application/octet-stream";
  }

  useEffect(() => {
    if (!negotiationId) return;
    setLoading(true);
    getConversationHistory(negotiationId)
      .then(async (data) => {
        setMessages(
          data.map((msg) => mapApiMessageToMessageProps(msg, negotiationId))
        );

        const audiosToDownload = data.filter(
          (m) =>
            m.fileUrl &&
            (m.messageType === "audio/mpeg" ||
              m.messageType === "AUDIO" ||
              m.messageType === "audio/mp3")
        );
        const entries = await Promise.all(
          audiosToDownload.map(async (msg) => {
            try {
              const blob = await downloadAudioFile(negotiationId, msg.fileUrl!);
              const blobUrl = URL.createObjectURL(blob);
              return { key: getMessageKey(msg), blobUrl };
            } catch {
              return { key: getMessageKey(msg), blobUrl: "" };
            }
          })
        );
        const newMap: Record<string, string> = {};
        entries.forEach(({ key, blobUrl }) => {
          if (blobUrl) newMap[key] = blobUrl;
        });
        setAudioBlobMap(newMap);
        audioBlobRef.current = newMap;

        const imagesToDownload = data.filter(
          (m) =>
            m.fileUrl &&
            (m.messageType === "image/jpeg" ||
              m.messageType === "image/jpg" ||
              m.messageType === "image/png" ||
              m.messageType === "image/gif" ||
              m.messageType === "IMAGE" ||
              /\.(jpg|jpeg|png|gif)$/i.test(m.fileUrl))
        );
        const imageEntries = await Promise.all(
          imagesToDownload.map(async (msg) => {
            try {
              const blob = await downloadImageFile(negotiationId, msg.fileUrl!);
              const blobUrl = URL.createObjectURL(blob);
              return { key: getMessageKey(msg), blobUrl };
            } catch {
              return { key: getMessageKey(msg), blobUrl: "" };
            }
          })
        );
        const newImageMap: Record<string, string> = {};
        imageEntries.forEach(({ key, blobUrl }) => {
          if (blobUrl) newImageMap[key] = blobUrl;
        });
        setImageBlobMap(newImageMap);
        imageBlobRef.current = newImageMap;
      })
      .finally(() => setLoading(false));

    return () => {
      Object.values(audioBlobRef.current).forEach((blobUrl) => {
        URL.revokeObjectURL(blobUrl);
      });
      Object.values(imageBlobRef.current).forEach((blobUrl) => {
        URL.revokeObjectURL(blobUrl);
      });
    };
  }, [negotiationId]);

  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  const { data: negotiationData, loading: loadingDetails } =
    usePortfolioItem(negotiationId);

  useEffect(() => {
    console.log(messages);
  }, [messages]);

  const handleSendMessage = async () => {
    if (!messageText.trim() && !selectedFile) return;
    setSending(true);
    try {
      const messageType = getMessageType(selectedFile);

      await sendMessage(
        negotiationId,
        messageText,
        selectedFile || undefined,
        messageType
      );

      const data = await getConversationHistory(negotiationId);

      setMessages(
        data.map((msg) => mapApiMessageToMessageProps(msg, negotiationId))
      );

      const audiosToDownload = data.filter(
        (m) =>
          m.fileUrl &&
          (m.messageType === "audio/mpeg" ||
            m.messageType === "AUDIO" ||
            m.messageType === "audio/mp3")
      );
      const entries = await Promise.all(
        audiosToDownload.map(async (msg) => {
          try {
            const blob = await downloadAudioFile(negotiationId, msg.fileUrl!);
            const blobUrl = URL.createObjectURL(blob);
            return { id: msg.id, blobUrl };
          } catch {
            return { id: msg.id, blobUrl: "" };
          }
        })
      );
      const newMap: Record<string, string> = {};
      entries.forEach(({ id, blobUrl }) => {
        if (blobUrl) newMap[id] = blobUrl;
      });
      setAudioBlobMap(newMap);
      audioBlobRef.current = newMap;

      const imagesToDownload = data.filter(
        (m) =>
          m.fileUrl &&
          (m.messageType === "image/jpeg" ||
            m.messageType === "image/jpg" ||
            m.messageType === "image/png" ||
            m.messageType === "image/gif" ||
            m.messageType === "IMAGE" ||
            /\.(jpg|jpeg|png|gif)$/i.test(m.fileUrl))
      );
      const imageEntries = await Promise.all(
        imagesToDownload.map(async (msg) => {
          try {
            const blob = await downloadImageFile(negotiationId, msg.fileUrl!);
            const blobUrl = URL.createObjectURL(blob);
            return { id: msg.id, blobUrl };
          } catch {
            return { id: msg.id, blobUrl: "" };
          }
        })
      );
      const newImageMap: Record<string, string> = {};
      imageEntries.forEach(({ id, blobUrl }) => {
        if (blobUrl) newImageMap[id] = blobUrl;
      });
      setImageBlobMap(newImageMap);
      imageBlobRef.current = newImageMap;

      setMessageText("");
      setSelectedFile(null);
    } catch (error) {
      console.error("Erro ao enviar mensagem", error);
    } finally {
      setSending(false);
    }
  };

  const handleSelectFile = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (
      file &&
      (file.type === "application/pdf" || file.type.startsWith("image/"))
    ) {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }

      if (file.type.startsWith("image/")) {
        const newPreviewUrl = URL.createObjectURL(file);
        setPreviewUrl(newPreviewUrl);
      } else {
        setPreviewUrl(null);
      }

      setSelectedFile(file);
    }
  };

  const handleRemoveFile = () => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
    setSelectedFile(null);
  };

  const handlePaperclipClick = () => {
    fileInputRef.current?.click();
  };

  if (loading) {
    return (
      <ChatAreaWrapper style={{ position: "relative" }}>
        <DetailsColumn item={negotiationData} loading={loadingDetails} />
        <ChatBox>
          <ChatMessages>
            <ChatSkeleton />
          </ChatMessages>
        </ChatBox>
      </ChatAreaWrapper>
    );
  }

  return (
    <ChatAreaWrapper style={{ position: "relative" }}>
      <DetailsColumn item={negotiationData} loading={loadingDetails} />
      {sending && (
        <Overlay>
          <Spinner />
        </Overlay>
      )}

      <ChatBox>
        <ChatMessages>
          {messages.map((msg, i) =>
            msg.type === "audio" ? (
              <Message key={i} {...msg} audioSrc={audioBlobMap[msg.id]} />
            ) : msg.type === "image" ? (
              <Message key={i} {...msg} imageSrc={imageBlobMap[msg.id]} />
            ) : (
              <Message key={i} {...msg} />
            )
          )}
        </ChatMessages>
        {selectedFile && (
          <FilePreviewBox>
            {selectedFile.type === "application/pdf" ? (
              <BsFiletypePdf size={20} color={theme.colors.neutral800} />
            ) : selectedFile.type.startsWith("image/") ? (
              <img
                src={previewUrl || ""}
                alt="Preview"
                style={{
                  width: "24px",
                  height: "24px",
                  objectFit: "cover",
                  borderRadius: "4px",
                }}
              />
            ) : (
              <BsFiletypePdf size={20} color={theme.colors.neutral800} />
            )}
            <FileName>{selectedFile.name}</FileName>
            <RemoveFileButton onClick={handleRemoveFile}>
              <FiX size={18} />
            </RemoveFileButton>
          </FilePreviewBox>
        )}
        <ChatInputRow>
          <FiPaperclip
            size={24}
            style={{ cursor: "pointer" }}
            onClick={handlePaperclipClick}
          />
          <HiddenInput
            ref={fileInputRef}
            type="file"
            accept="application/pdf"
            onChange={handleSelectFile}
          />
          <input
            type="text"
            placeholder="Envie sua mensagem"
            value={messageText}
            onChange={(e) => setMessageText(e.target.value)}
            disabled={sending}
          />
          <Button
            variant="primary"
            size="lg"
            icon={<LuSend size={24} />}
            onClick={handleSendMessage}
            label="Enviar"
            disabled={sending}
          />
        </ChatInputRow>
      </ChatBox>
    </ChatAreaWrapper>
  );
}
