import { theme } from "@/styles/theme";
import styled from "styled-components";

export const ChatAreaWrapper = styled.div`
  display: flex;
  flex: 1;
  overflow: hidden;
  min-width: 0;
`;

export const ChatHeader = styled.h2`
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 16px;
`;

export const DetailsRow = styled.div`
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  padding: 8px;
  border-right: 0.5px solid rgb(220, 220, 220);
  overflow-y: auto;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 5px;
    background: #f0f0f0;
    border-radius: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background: #cccccc;
    border-radius: 8px;
  }
`;

export const DataBox = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;

  p {
    font-size: 14px;
    font-weight: 400;
    margin: 0;
    color: ${({ theme }) => theme.colors.neutral800};
  }

  b {
    font-size: 16px;
    font-weight: 500;
    color: ${({ theme }) => theme.colors.neutral900};
  }
`;

export const DetailsCol = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
  min-width: 280px;
  max-width: 280px;
  padding: 24px 40px 40px 16px;
`;

export const ChatBox = styled.div`
  background: ${({ theme }) => theme.colors.neutral100};
  border-radius: 14px;
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  // max-width: 769px;
  min-width: 0;
  flex: 1;
`;

export const ChatMessages = styled.div`
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 0;
  flex: 1;
  padding: 30px;
`;

export const Message = styled.div<{ role?: string }>`
  background: ${({ role, theme }) =>
    role === "user" ? theme.colors.neutral100 : "#F5F6FF"};
  color: ${({ role, theme }) =>
    role === "user" ? theme.colors.neutral900 : theme.colors.neutral900};
  border: ${({ role }) =>
    role === "user"
      ? `1px solid ${theme.colors.neutral400}`
      : "1px solid #D5DEFF"};
  border-radius: 9px;
  padding: 12px 18px;
  align-self: ${({ role }) => (role === "user" ? "flex-start" : "flex-end")};
  font-size: 16px;
  max-width: 556px;
  line-height: normal;
  font-weight: 400;
  box-shadow:
    0px 1px 2px rgba(0, 0, 0, 0.03),
    0px 1px 3px rgba(0, 0, 0, 0.03),
    0px 3px 5px rgba(0, 0, 0, 0.03);
`;

export const ChatInputRow = styled.div`
  display: flex;
  gap: 24px;
  margin-top: 10px;
  background: #f5f5f5;
  align-items: center;
  padding: 24px;
  // max-width: 769px;
  max-height: 106px;

  input {
    /* max-width: 427px; */
    flex: 1;
    height: 58px;
    padding: 10px 16px;
    background-color: white;
    border-radius: 4px;
    border: 0.5px solid ${({ theme }) => theme.colors.neutral300};
    font-size: 16px;
    box-shadow:
      0px 1px 2px rgba(0, 0, 0, 0.03),
      0px 1px 3px rgba(0, 0, 0, 0.03),
      0px 3px 5px rgba(0, 0, 0, 0.03);
  }
`;

export const ClientDataBox = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  background: white;
  border-radius: 8px;
  padding: 16px;

  box-shadow:
    0px 1px 2px rgba(0, 0, 0, 0.03),
    0px 1px 3px rgba(0, 0, 0, 0.03),
    0px 3px 5px rgba(0, 0, 0, 0.03);

  p {
    font-size: 16px;
    font-weight: 500;
    color: ${({ theme }) => theme.colors.neutral900};
  }
`;

export const HiddenInput = styled.input`
  display: none;
`;

export const FilePreviewBox = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  background: ${({ theme }) => theme.colors.neutral200};
  border-radius: 8px;
  padding: 8px 12px;
  max-width: 350px;
`;

export const FileName = styled.span`
  font-size: 14px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.neutral900};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 250px;
`;

export const RemoveFileButton = styled.button`
  background: transparent;
  border: none;
  cursor: pointer;
  color: ${({ theme }) => theme.colors.neutral700};
  display: flex;
  align-items: center;
  padding: 2px;

  &:hover {
    color: ${({ theme }) => theme.colors.error};
  }
`;
