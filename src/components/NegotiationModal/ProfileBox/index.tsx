"use client";
import React, { useState } from "react";
import {
  Wrapper,
  Toggle,
  ChevronIcon,
  Content,
  FieldLabel,
  FieldValue,
} from "./styles";
import { ChevronDown } from "lucide-react";

function humanizeLabel(label: string) {
  if (!label) return "";
  return label
    .toLowerCase()
    .replace(/_/g, " ")
    .replace(/\s+/g, " ")
    .trim()
    .replace(/\b\w/g, (l) => l.toUpperCase());
}

type ExpandableProfileBoxProps = {
  profile: Record<string, string>;
};

export function ExpandableProfileBox({ profile }: ExpandableProfileBoxProps) {
  const [open, setOpen] = useState(false);

  if (!profile || Object.keys(profile).length === 0) return null;

  return (
    <Wrapper>
      <Toggle onClick={() => setOpen((v) => !v)}>
        {open ? "Minimizar perfil" : "Expandir perfil"}
        <ChevronIcon open={open}>
          <ChevronDown size={22} />
        </ChevronIcon>
      </Toggle>
      {open && (
        <Content>
          {Object.entries(profile).map(([key, value]) => {
            const safeValue =
              typeof value === "string"
                ? value
                : value == null
                  ? ""
                  : String(value);
            return safeValue.trim() ? (
              <React.Fragment key={key}>
                <FieldLabel>{humanizeLabel(key)}</FieldLabel>
                <FieldValue>{safeValue}</FieldValue>
              </React.Fragment>
            ) : null;
          })}
        </Content>
      )}
    </Wrapper>
  );
}
