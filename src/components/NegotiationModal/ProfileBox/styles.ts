import styled, { css } from "styled-components";

export const Wrapper = styled.div`
  background: #fff;
  border-radius: 10px;
  max-width: 280px;
  box-shadow:
    0px 1px 2px rgba(0, 0, 0, 0.03),
    0px 1px 3px rgba(0, 0, 0, 0.03),
    0px 3px 5px rgba(0, 0, 0, 0.03);

  padding: 0;
  margin: 0;
`;

export const Toggle = styled.button`
  width: 100%;
  background: none;
  border: none;
  outline: none;
  padding: 18px 20px 18px 20px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
`;

export const ChevronIcon = styled.span<{ open: boolean }>`
  transition: transform 0.3s;
  display: flex;
  align-items: center;
  ${({ open }) =>
    open &&
    css`
      transform: rotate(-180deg);
    `}
`;

export const Content = styled.div`
  padding: 0px 16px 16px 16px;
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

export const FieldLabel = styled.div`
  font-size: 13px;
  color: #888fa2;
  margin-top: 4px;
`;

export const FieldValue = styled.div`
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 2px;
  word-break: break-all;
  color: #212233;
`;
