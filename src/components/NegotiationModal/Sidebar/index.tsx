// src/components/NegotiationModal/Sidebar/index.tsx
"use client";
import React, { useState } from "react";
import { SidebarWrapper, QueueList } from "./styles";
import Tabs from "@/components/Tabs";
import ServiceQueueItem from "@/components/ServiceQueue/ServiceQueueItem";
import { IPortfolioItem, PortfolioItemStatus } from "@/types/portfolio";
import { formatDateToHour } from "@/utils/formatDate";
import { timeAgo } from "@/utils/formatDate";
import { formatDateLong } from "@/utils/formatDate";
import { portfolioItemStatusMap } from "@/constants/portfolioItemStatusMap";

type SidebarProps = {
  selectedId?: string;
  onSelect?: (id: string) => void;
  collapsed: boolean;
  queueNegotiations: IPortfolioItem[];
  loading?: boolean;
};

export function Sidebar({
  selectedId,
  onSelect,
  collapsed,
  queueNegotiations,
  loading = false,
}: SidebarProps) {
  const [tab, setTab] = useState<"negociacoes" | "fila">("fila");

  const itemss = tab === "negociacoes" ? queueNegotiations : queueNegotiations;

  const tabs = [
    // {
    //   label: "Negociações",
    //   value: "negociacoes",
    //   count: allNegotiations.length,
    // },
    {
      label: "Fila de atendimento",
      value: "fila",
      count: queueNegotiations.length,
    },
  ];

  return (
    <SidebarWrapper collapsed={collapsed}>
      {!collapsed && (
        <>
          <Tabs
            tabs={tabs}
            value={tab}
            onChange={(value) => setTab(value as "negociacoes" | "fila")}
            gap="2px"
          />
          <QueueList>
            {loading ? (
              <div style={{ padding: 16, textAlign: "center", color: "#888" }}>
                Carregando...
              </div>
            ) : (
              itemss.map((item) => {
                const statusConfig =
                  portfolioItemStatusMap[
                    item.currentStatus as PortfolioItemStatus
                  ];
                const lastInteraction = item.lastInteraction
                  ? new Date(item.lastInteraction)
                  : new Date();
                return (
                  <ServiceQueueItem
                    key={item.id}
                    id={item.id}
                    customerName={
                      item.customData?.NOME_DO_CLIENTE ??
                      item.customData?.NOME_CLIENTE ??
                      " Sem nome"
                    }
                    customerPhone={
                      item.customData?.PHONE_NUMBER ?? item.phoneNumber
                    }
                    date={formatDateLong(lastInteraction)}
                    time={formatDateToHour(lastInteraction)}
                    relative={timeAgo(lastInteraction)}
                    status={statusConfig?.variant}
                    onClick={() => onSelect?.(item.id)}
                    selected={selectedId === item.id}
                    showActionIcon={false}
                    maxWidth={342}
                    fontSize={16}
                    relativeFontSize={12}
                    relativeFontWeight={500}
                  />
                );
              })
            )}
          </QueueList>
        </>
      )}
    </SidebarWrapper>
  );
}
