// src/components/NegotiationModal/Sidebar/styles.ts
import styled from "styled-components";

export const SidebarWrapper = styled.aside<{ collapsed: boolean }>`
  min-width: ${({ collapsed }) => (collapsed ? "72px" : "372px")};
  background: ${({ collapsed, theme }) =>
    collapsed ? theme.colors.neutral200 : theme.colors.background};
  border-right: 1px solid rgb(220, 220, 220);
  padding: 24px 0 24px 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px;
  overflow: hidden;
  gap: 23px;
  transition:
    max-width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    min-width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    background 0.2s;
`;

export const QueueList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  overflow-y: auto;
  max-width: 340px;
  background: ${({ theme }) => theme.colors.neutral200};
  padding: 8px;
  border-radius: 8px;
  border: 0.5px solid ${({ theme }) => theme.colors.neutral400};

  &::-webkit-scrollbar {
    width: 5px;
    background: #f0f0f0;
    border-radius: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background: #cccccc;
    border-radius: 8px;
  }
`;
