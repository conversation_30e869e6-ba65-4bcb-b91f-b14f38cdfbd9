// src/components/NegotiationModal/index.tsx
import Modal from "@/components/Modal";
import { Sidebar } from "./Sidebar";
import { ChatA<PERSON> } from "./ChatArea";
import {
  NegotiationModalWrapper,
  ContentArea,
  NegotiationHeader,
  MainHeader,
  SidebarHeader,
  CollapseButton,
  ChatTitle,
  XButton,
} from "./styles";
import { useEffect, useState } from "react";
import { MdKeyboardDoubleArrowRight } from "react-icons/md";
import { theme } from "@/styles/theme";
import { FiXCircle } from "react-icons/fi";
import { useRouter } from "next/navigation";
import { useQueueName } from "@/hooks/useQueueName";
import { HiOutlineDotsVertical } from "react-icons/hi";
import { MoreOptionsButton } from "@/components/MoreOptionsButton";
import { FiX } from "react-icons/fi";
import { IPortfolioItem, PortfolioItemStatus } from "@/types/portfolio";
import { setPortfolioItemStatusToSucceed } from "@/services/portfolioService";
import { setPortfolioItemStatusToFinished } from "@/services/portfolioService";
import { IoMdCheckmarkCircleOutline } from "react-icons/io";
type NegotiationModalProps = {
  isOpen: boolean;
  onClose: () => void;
  initialNegotiationId: string;
  source: "negotiations" | "queue" | null;
  portfolioId: string;
  portfolioItem?: IPortfolioItem | undefined;
};

export default function NegotiationModal({
  isOpen,
  onClose,
  initialNegotiationId,
  portfolioId,
  source,
  portfolioItem,
}: NegotiationModalProps & { portfolioId: string }) {
  const [negotiationId, setNegotiationId] = useState(initialNegotiationId);
  const [collapsed, setCollapsed] = useState(false);
  const router = useRouter();

  const { items: queueNegotiations } = useQueueName();

  useEffect(() => {
    setNegotiationId(initialNegotiationId);
  }, [initialNegotiationId]);

  const handleSelectCard = (id: string) => {
    router.push(
      `/portfolio/${portfolioId}?negotiation=${id}${
        source ? `&source=${source}` : ``
      }`
    );
    setNegotiationId(id);
  };

  const handleUpdatePortfolioItemStatus = async (
    status: PortfolioItemStatus
  ) => {
    if (!portfolioItem) return;

    if (status === PortfolioItemStatus.SUCCEED) {
      await setPortfolioItemStatusToSucceed(portfolioItem.id);
    } else if (status === PortfolioItemStatus.FINISHED) {
      await setPortfolioItemStatusToFinished(portfolioItem.id);
    }

    window.location.reload();
  };

  const navItems = [];

  if (
    portfolioItem?.currentStatus !== undefined &&
    [
      PortfolioItemStatus.UNLINKED,
      PortfolioItemStatus.FOLLOWED_UP,
    ].includes(portfolioItem.currentStatus)
  ) {
    navItems.push({
      key: "complete",
      icon: (
        <IoMdCheckmarkCircleOutline size={16} color={theme.colors.labelInput} />
      ),
      label: "Concluir atendimento",
      onClick: () =>
        handleUpdatePortfolioItemStatus(PortfolioItemStatus.SUCCEED),
      minWidth: 196,
      fontWeight: 400,
    });
  }

  if (
    portfolioItem?.currentStatus !== undefined &&
    [
      PortfolioItemStatus.UNLINKED,
      PortfolioItemStatus.IN_PROGRESS,
      PortfolioItemStatus.FOLLOWED_UP,
    ].includes(portfolioItem.currentStatus)
  ) {
    navItems.push({
      key: "end",
      icon: <FiX size={16} color={theme.colors.labelInput} />,
      label: "Encerrar atendimento",
      onClick: () =>
        handleUpdatePortfolioItemStatus(PortfolioItemStatus.FINISHED),
      minWidth: 196,
      fontWeight: 400,
    });
  }

  if (navItems.length === 0) {
    navItems.push({
      key: "none",
      label: "Nenhuma ação disponível",

      onClick: () => {},
      disabled: true,
      minWidth: 196,
      fontWeight: 400,
    });
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      footer={false}
      maxWidth="1600px"
      padding="0"
      display="flex"
      overflowY="hidden"
    >
      <div style={{ width: "100%" }}>
        <NegotiationHeader>
          {source !== "negotiations" && (
            <SidebarHeader collapsed={collapsed}>
              {!collapsed && <ChatTitle collapsed={collapsed}>Chat</ChatTitle>}
              <CollapseButton
                onClick={() => setCollapsed((c) => !c)}
                aria-label="Colapsar sidebar"
              >
                <MdKeyboardDoubleArrowRight
                  style={{
                    transform: collapsed ? "rotate(180deg)" : "none",
                    width: "24px",
                    height: "24px",
                  }}
                  color={theme.colors.neutral900}
                />
              </CollapseButton>
            </SidebarHeader>
          )}
          <MainHeader>
            <span>Dados do contato</span>{" "}
            <div style={{ display: "flex", alignItems: "center", gap: 10 }}>
              <MoreOptionsButton
                navItems={navItems}
                placement="bottomRight"
                arrowPlacement="right"
              >
                <HiOutlineDotsVertical
                  color={theme.colors.labelInput}
                  style={{ width: 20, height: 20 }}
                />
              </MoreOptionsButton>
              <XButton onClick={onClose}>
                <FiXCircle
                  color={theme.colors.labelInput}
                  style={{ width: 20, height: 20 }}
                />
              </XButton>
            </div>
          </MainHeader>
        </NegotiationHeader>

        <NegotiationModalWrapper>
          {source !== "negotiations" && (
            <Sidebar
              selectedId={negotiationId}
              onSelect={handleSelectCard}
              collapsed={collapsed}
              queueNegotiations={queueNegotiations}
            />
          )}
          <ContentArea>
            <ChatArea key={negotiationId} negotiationId={negotiationId} />
          </ContentArea>
        </NegotiationModalWrapper>
      </div>
    </Modal>
  );
}
