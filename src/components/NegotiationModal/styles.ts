// src/components/NegotiationModal/styles.ts
import styled from "styled-components";
export const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  flex: 1;
  gap: 8px;
`;

export const HeaderRight = styled.div`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.neutral900};
  flex: 1;
  text-align: right;
`;

export const NegotiationModalWrapper = styled.div`
  display: flex;
  min-height: 600px;
  height: calc(100vh - 80px);
  min-width: 980px;
`;

export const ContentArea = styled.div`
  flex: 1;
  background: ${({ theme }) => theme.colors.background};
  padding: 0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
`;

export const NegotiationHeader = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  background: ${({ theme }) => theme.colors.background};
  border-bottom: 1px solid rgb(220, 220, 220);
  min-height: 80px;
  z-index: 10;
`;

export const SidebarHeader = styled.div<{ collapsed: boolean }>`
  display: flex;
  align-items: center;
  height: 80px;
  min-width: ${({ collapsed }) => (collapsed ? "72px" : "372px")};
  padding: 0 16px;
  background: ${({ collapsed, theme }) =>
    collapsed ? theme.colors.neutral200 : theme.colors.background};
  justify-content: ${({ collapsed }) =>
    collapsed ? "center" : "space-between"};
  position: relative;
  border-right: 0.5px solid rgb(220, 220, 220);
  transition:
    min-width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    background 0.2s;
`;

export const ChatTitle = styled.span<{ collapsed: boolean }>`
  font-weight: 600;
  font-size: 24px;
  color: ${({ theme }) => theme.colors.neutral900};
  opacity: ${({ collapsed }) => (collapsed ? 0 : 1)};
  transform: ${({ collapsed }) =>
    collapsed ? "translateX(-10px)" : "translateX(0)"};
  transition:
    opacity 0.2s,
    transform 0.2s;
  pointer-events: none;
  white-space: nowrap;
`;

export const CollapseButton = styled.button`
  border: none;
  background: none;
  cursor: pointer;
  font-size: 22px;
  margin-left: 0;
  padding: 0 4px;
  color: ${({ theme }) => theme.colors.neutral700};
  display: flex;
  align-items: center;
  border-radius: 4px;
  height: 40px;
  &:hover {
    background: ${({ theme }) => theme.colors.neutral100};
  }
`;

export const HeaderDivider = styled.div`
  width: 1px;
  align-self: stretch;
  background: ${({ theme }) => theme.colors.neutral300};
  margin: 0 0;
`;

export const MainHeader = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  font-size: 20px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.neutral900};

  span {
    font-size: 24px;
    font-weight: 600;
    color: ${({ theme }) => theme.colors.neutral900};
  }
`;

export const XButton = styled.button`
  border: none;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  &:hover {
    background: ${({ theme }) => theme.colors.neutral200};
    border-radius: 50px;
  }
`;
