import styled, { keyframes } from "styled-components";

const shimmer = keyframes`
  0%   { background-position: -200px 0; }
  100% { background-position: 200px 0; }
`;

const Sk = styled.div<{ w?: string; h?: string; r?: number }>`
  width: ${({ w }) => w || "100%"};
  height: ${({ h }) => h || "12px"};
  border-radius: ${({ r }) => (r !== undefined ? r : 6)}px;
  background: linear-gradient(90deg, #eee 25%, #f5f5f5 50%, #eee 75%);
  background-size: 400px 100%;
  animation: ${shimmer} 1.2s ease-in-out infinite;
`;

const SkCircle = styled(Sk)`
  border-radius: 999px;
`;

const SideRow = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 10px;
  padding: 10px;
  border-bottom: 0.5px solid #ececec;
`;
const SideLeft = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 0;
`;
export const SidebarSkeleton: React.FC<{ rows?: number }> = ({ rows = 8 }) => (
  <div>
    {Array.from({ length: rows }).map((_, i) => (
      <SideRow key={i}>
        <SideLeft>
          <Sk w="160px" h="14px" />
          <Sk w="110px" h="18px" r={20} />
        </SideLeft>
        <Sk w="90px" h="12px" />
      </SideRow>
    ))}
  </div>
);

const Section = styled.div`
  padding: 8px;
  margin-top: 8px;
`;
export const RightPanelSkeleton: React.FC<{
  tab?: "dados" | "midias" | "atividades";
}> = ({ tab = "dados" }) => {
  if (tab === "midias") {
    return (
      <Section>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            marginBottom: 6,
          }}
        >
          <Sk w="80px" h="14px" />
          <SkCircle w="18px" h="18px" />
        </div>
        {Array.from({ length: 3 }).map((_, i) => (
          <div
            key={i}
            style={{
              display: "grid",
              gridTemplateColumns: "44px 1fr 28px",
              gap: 10,
              padding: "6px 4px",
              borderRadius: 8,
            }}
          >
            <Sk w="44px" h="44px" r={8} />
            <div style={{ display: "flex", flexDirection: "column", gap: 6 }}>
              <Sk w="160px" h="12px" />
              <Sk w="100px" h="12px" />
            </div>
            <SkCircle w="28px" h="28px" />
          </div>
        ))}
      </Section>
    );
  }

  if (tab === "atividades") {
    return (
      <Section>
        <Sk w="120px" h="14px" />
        <div
          style={{
            border: "1px solid #eee",
            borderRadius: 10,
            padding: 16,
            marginTop: 8,
          }}
        >
          {Array.from({ length: 4 }).map((_, i) => (
            <div
              key={i}
              style={{
                position: "relative",
                paddingLeft: 22,
                margin: "18px 0",
              }}
            >
              {i < 3 && (
                <div
                  style={{
                    position: "absolute",
                    left: 8,
                    top: 14,
                    bottom: -18,
                    width: 2,
                    background: "#eee",
                  }}
                />
              )}
              <SkCircle
                w="10px"
                h="10px"
                style={{ position: "absolute", left: 4, top: 6 }}
              />
              <Sk w="180px" h="12px" />
              <Sk w="200px" h="12px" style={{ marginTop: 6 }} />
            </div>
          ))}
        </div>
      </Section>
    );
  }

  return (
    <>
      <Section>
        <Sk w="110px" h="14px" />
        <Sk w="90%" h="48px" style={{ marginTop: 8 }} />
      </Section>
      <Section>
        <Sk w="110px" h="14px" />
        <Sk w="120px" h="22px" r={20} style={{ marginTop: 8 }} />
      </Section>
      <Section>
        <Sk w="110px" h="14px" />
        <Sk w="160px" h="14px" style={{ marginTop: 8 }} />
      </Section>
      <Section>
        <Sk w="110px" h="14px" />
        <Sk w="60%" h="14px" style={{ marginTop: 8 }} />
      </Section>

      {Array.from({ length: 2 }).map((_, i) => (
        <Section key={i}>
          <Sk w="140px" h="14px" />
          <Sk w="80%" h="14px" style={{ marginTop: 8 }} />
        </Section>
      ))}
    </>
  );
};
