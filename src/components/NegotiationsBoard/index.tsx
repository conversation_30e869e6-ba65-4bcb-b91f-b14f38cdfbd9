/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useEffect, useMemo, useRef, useState } from "react";
import { Pagination } from "antd";
import {
  ChevronLeft,
  CircleArrowDown,
  CircleX,
  PanelLeftClose,
  PanelRightOpen,
} from "lucide-react";
import { CloseRightBtn, OpenRightFloatingBtn } from "./styles";
import { LuSend } from "react-icons/lu";
import { FiPaperclip, FiX } from "react-icons/fi";
import { FaRegFileImage } from "react-icons/fa";
import { FaRegFilePdf } from "react-icons/fa6";
import { BsFiletypePdf } from "react-icons/bs";

import StatusTag from "@/components/StatusTag";
import Tabs from "@/components/Tabs";
import Button from "../Button";
import { MoreOptionsButton } from "@/components/MoreOptionsButton";

import {
  Message,
  MessageProps,
} from "@/components/NegotiationModal/ChatArea/Message";
import {
  HiddenInput,
  FilePreviewBox,
  RemoveFileButton,
  FileName,
} from "@/components/NegotiationModal/ChatArea/styles";

import { IPortfolioItem, PortfolioItemStatus } from "@/types/portfolio";
import { portfolioItemStatusMap } from "@/constants/portfolioItemStatusMap";
import { theme } from "@/styles/theme";

import {
  getAllPortfolioItems,
  getAllPortfolios,
  setPortfolioItemStatusToSucceed,
  setPortfolioItemStatusToFinished,
} from "@/services/portfolioService";
import {
  getConversationHistory,
  sendMessage,
  downloadAudioFile,
  downloadImageFile,
  downloadPdfFile,
  getPortfolioItem,
  getExecutionHistoryForPortfolioItem,
  IMessageHistory,
} from "@/services/portfolioItemService";

import { mapApiMessageToMessageProps } from "@/utils/messageMapper";
import { formatPhone } from "@/utils/formatPhone";
import { formatDateShort, timeAgo } from "@/utils/formatDate";

import {
  Container,
  Sidebar,
  SidebarHeader,
  Title,
  NegotiationsList,
  NegotiationItem,
  NegotiationInfo,
  DataInfoTitle,
  PhoneNumber,
  NegotiationStatus,
  NegotiationDate,
  MainContent,
  ChatHeader,
  ChatHeaderLeft,
  ChatUserPhone,
  ChatMessages,
  ChatInputRow,
  PaginationContainer,
  IconButton,
  Overlay,
  Spinner,
  RightPanel,
  InfoSection,
  InfoTitle,
  InfoItem,
  InfoLabel,
  InfoValue,
  MediaHeader,
  MediaHeaderTitle,
  Caret,
  MediaList,
  MediaRow,
  Thumb,
  MediaMeta,
  MediaDate,
  DownloadBtn,
  TimelineBox,
  TLRow,
  TLDot,
  TLTitle,
  TLDate,
  WaitingDot,
} from "./styles";

import {
  TimelineItem,
  getMessageKey,
  extractCustomerName,
  buildCustomDataEntries,
  buildTimelineFromSchemas,
  sortConversationByEffectiveTime,
} from "@/components/NegotiationsBoard/utils";
import { IoMdCheckmarkCircleOutline } from "react-icons/io";
import {
  RightPanelSkeleton,
  SidebarSkeleton,
} from "./NegotiationsList/BoardSkeletons";
import { ChatSkeleton } from "../NegotiationModal/ChatArea/ChatSkeleton";

type Filters = {
  phone: string;
  name: string;
  status: "todos" | PortfolioItemStatus;
  onlyInteraction: boolean;
  waitingBusinessUserResponse: boolean;
  onlyFirstMessageSent?: boolean;
};

type Props = {
  filters: Filters;
  page: number;
  setPage: (page: number) => void;
  negotiationId: string;
  renderEmpty: React.ReactNode;
  setNegotiationId: (id: string) => void;
  source: string;
};

export default function NegotiationsBoard({
  filters,
  page,
  setPage,
  renderEmpty,
  negotiationId,
  source,
  setNegotiationId,
}: Props) {
  const listReqRef = useRef(0);
  const chatReqRef = useRef(0);

  const [portfolios, setPortfolios] = useState<any[]>([]);
  const [items, setItems] = useState<IPortfolioItem[]>([]);
  const [total, setTotal] = useState(0);
  const [loadingList, setLoadingList] = useState(true);
  const [hasFetchedList, setHasFetchedList] = useState(false);
  const [pageSize, setPageSize] = useState(10);

  const activeNegotiation = items.find((i) => i.id === negotiationId) || null;

  const [timeline, setTimeline] = useState<TimelineItem[]>([]);
  const [timelineOpen, setTimelineOpen] = useState(true);
  const [loadingChat, setLoadingChat] = useState(false);
  const [loadingRight, setLoadingRight] = useState(false);

  const [activeDetails, setActiveDetails] = useState<IPortfolioItem | null>(
    null
  );

  const [messages, setMessages] = useState<MessageProps[]>([]);
  const [audioBlobMap, setAudioBlobMap] = useState<Record<string, string>>({});
  const [imageBlobMap, setImageBlobMap] = useState<Record<string, string>>({});
  const audioBlobRef = useRef<Record<string, string>>({});
  const imageBlobRef = useRef<Record<string, string>>({});

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [sending, setSending] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [activeTab, setActiveTab] = useState("dados");
  const [currentMessage, setCurrentMessage] = useState("");
  const [mediaOpen, setMediaOpen] = useState(true);

  const [rightOpen, setRightOpen] = useState(true);

  useEffect(() => {
    getAllPortfolios().then(setPortfolios).catch(console.error);
  }, []);

  const searchParams = useMemo(() => {
    const base: any = {};
    if (filters.phone.trim()) base.phoneNumber = filters.phone;
    if (filters.name && filters.name.trim()) base.name = filters.name;
    if (filters.onlyInteraction) base.lastInteraction = { not: null };
    if (filters.waitingBusinessUserResponse)
      base.waitingBusinessUserResponse = true;
    if (filters.status !== "todos") base.currentStatus = filters.status;
    if (filters.onlyFirstMessageSent) base.firstMessageSentAt = { not: null };
    return base;
  }, [
    filters.phone,
    filters.name,
    filters.onlyInteraction,
    filters.waitingBusinessUserResponse,
    filters.status,
    filters.onlyFirstMessageSent,
  ]);

  useEffect(() => {
    if (portfolios.length === 0) return;

    const reqId = ++listReqRef.current;
    setLoadingList(true);

    (async () => {
      try {
        let sortField: "createdAt" | "lastInteraction" = "createdAt";
        let sortDirection: "asc" | "desc" = "desc";
        if (filters.onlyInteraction) {
          sortField = "lastInteraction";
          sortDirection = "desc";
        } else if (filters.waitingBusinessUserResponse) {
          sortField = "lastInteraction";
          sortDirection = "asc";
        }

        const resp = await getAllPortfolioItems(
          searchParams,
          { page, limit: pageSize },
          portfolios,
          sortField,
          sortDirection
        );

        if (listReqRef.current !== reqId) return;

        setItems(resp.items);
        setTotal(resp.total);
        setHasFetchedList(true);

        const isPortfolioSource = source === "portfolio";
        const hasUrlId =
          negotiationId && resp.items.some((i) => i.id === negotiationId);

        if (
          !isPortfolioSource &&
          (!negotiationId || !hasUrlId) &&
          resp.items.length
        ) {
          setNegotiationId(resp.items[0].id);
        }
      } catch (e) {
        if (listReqRef.current === reqId) {
          console.error("Erro ao carregar negociações:", e);
        }
      } finally {
        if (listReqRef.current === reqId) {
          setLoadingList(false);
          setHasFetchedList(true);
        }
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [portfolios, searchParams, page, pageSize, source, negotiationId]);

  useEffect(() => {
    const current = activeNegotiation?.id;
    const reqId = ++chatReqRef.current;

    setMessages([]);
    setLoadingChat(true);
    setLoadingRight(true);
    setAudioBlobMap({});
    setImageBlobMap({});
    audioBlobRef.current = {};
    imageBlobRef.current = {};
    setActiveDetails(null);

    if (!current) {
      setLoadingChat(false);
      setLoadingRight(false);
      return;
    }

    (async () => {
      try {
        const [details, raw] = await Promise.all([
          getPortfolioItem(current),
          getConversationHistory(current),
        ]);

        if (chatReqRef.current !== reqId) return;

        const orderedRaw =
          sortConversationByEffectiveTime<IMessageHistory>(raw);

        const audioMsgs = orderedRaw.filter(
          (m) =>
            m.fileUrl &&
            (m.messageType === "audio/mpeg" ||
              m.messageType === "AUDIO" ||
              m.messageType === "audio/mp3")
        );

        const audioEntries = await Promise.all(
          audioMsgs.map(async (m) => {
            try {
              const blob = await downloadAudioFile(current, m.fileUrl!);
              const url = URL.createObjectURL(blob);
              return { id: getMessageKey(m), url };
            } catch {
              return { id: getMessageKey(m), url: "" };
            }
          })
        );

        if (chatReqRef.current !== reqId) {
          audioEntries.forEach((e) => e.url && URL.revokeObjectURL(e.url));
          return;
        }

        const audioMap: Record<string, string> = {};
        audioEntries.forEach(({ id, url }) => {
          if (url) audioMap[id] = url;
        });
        setAudioBlobMap(audioMap);
        audioBlobRef.current = audioMap;

        const imageMsgs = orderedRaw.filter(
          (m) =>
            m.fileUrl &&
            (m.messageType === "image/jpeg" ||
              m.messageType === "image/jpg" ||
              m.messageType === "image/png" ||
              m.messageType === "image/gif" ||
              m.messageType === "IMAGE" ||
              /\.(jpg|jpeg|png|gif)$/i.test(m.fileUrl))
        );

        const imageEntries = await Promise.all(
          imageMsgs.map(async (m) => {
            try {
              const blob = await downloadImageFile(current, m.fileUrl!);
              const url = URL.createObjectURL(blob);
              return { id: getMessageKey(m), url };
            } catch {
              return { id: getMessageKey(m), url: "" };
            }
          })
        );

        if (chatReqRef.current !== reqId) {
          imageEntries.forEach((e) => e.url && URL.revokeObjectURL(e.url));
          return;
        }

        const imageMap: Record<string, string> = {};
        imageEntries.forEach(({ id, url }) => {
          if (url) imageMap[id] = url;
        });
        setImageBlobMap(imageMap);
        imageBlobRef.current = imageMap;

        setMessages(
          orderedRaw.map((m) => mapApiMessageToMessageProps(m, current))
        );
        if (details?.id === current) setActiveDetails(details);

        // const exec = await getExecutionHistoryForPortfolioItem(current);
        // if (chatReqRef.current !== reqId) return;
        // const tl = buildTimelineFromSchemas({ exec, chat: orderedRaw, item: details });
        // setTimeline(tl);
      } catch (e) {
        if (chatReqRef.current === reqId) {
          console.error("Erro ao carregar histórico/detalhes do chat:", e);
          setTimeline([]);
        }
      } finally {
        if (chatReqRef.current === reqId) {
          setLoadingChat(false);
          setLoadingRight(false);
        }
      }
    })();

    return () => {
      Object.values(audioBlobRef.current).forEach((url) =>
        URL.revokeObjectURL(url)
      );
      Object.values(imageBlobRef.current).forEach((url) =>
        URL.revokeObjectURL(url)
      );
    };
  }, [activeNegotiation?.id]);

  const tabItems = [
    { value: "dados", label: "Dados" },
    { value: "midias", label: "Mídias" },
    // { value: "atividades", label: "Atividades" },
  ];

  const handleSelectFile = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (
      file &&
      (file.type === "application/pdf" || file.type.startsWith("image/"))
    ) {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
      if (file.type.startsWith("image/")) {
        const newPreviewUrl = URL.createObjectURL(file);
        setPreviewUrl(newPreviewUrl);
      } else {
        setPreviewUrl(null);
      }
      setSelectedFile(file);
    }
  };

  const handleRemoveFile = () => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
    setSelectedFile(null);
  };

  const handlePaperclipClick = () => {
    fileInputRef.current?.click();
  };

  function getMessageType(file: File | null) {
    if (!file) return "TEXT";
    return file.type || "application/octet-stream";
  }

  const handleSendMessage = async () => {
    if (!currentMessage.trim() && !selectedFile) return;
    if (!activeNegotiation) return;
    setSending(true);
    try {
      const messageType = getMessageType(selectedFile);
      await sendMessage(
        activeNegotiation.id,
        currentMessage,
        selectedFile || undefined,
        messageType
      );
      const data = await getConversationHistory(activeNegotiation.id);
      setMessages(
        data.map((msg) =>
          mapApiMessageToMessageProps(msg, activeNegotiation.id)
        )
      );
      setCurrentMessage("");
      setSelectedFile(null);
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
        setPreviewUrl(null);
      }
    } catch (error) {
      console.error("Erro ao enviar mensagem", error);
    } finally {
      setSending(false);
    }
  };

  const handleUpdatePortfolioItemStatus = async (
    status: PortfolioItemStatus
  ) => {
    if (!activeNegotiation) return;

    if (status === PortfolioItemStatus.SUCCEED) {
      await setPortfolioItemStatusToSucceed(activeNegotiation.id);
    } else if (status === PortfolioItemStatus.FINISHED) {
      await setPortfolioItemStatusToFinished(activeNegotiation.id);
    }
    window.location.reload();
  };

  const navItems: any[] = [];
  if (
    activeNegotiation?.currentStatus !== undefined &&
    [PortfolioItemStatus.UNLINKED, PortfolioItemStatus.FOLLOWED_UP].includes(
      activeNegotiation.currentStatus
    )
  ) {
    navItems.push({
      key: "complete",
      icon: (
        <IoMdCheckmarkCircleOutline size={16} color={theme.colors.labelInput} />
      ),
      label: "Concluir atendimento",
      onClick: () =>
        handleUpdatePortfolioItemStatus(PortfolioItemStatus.SUCCEED),
      minWidth: 196,
      fontWeight: 400,
    });
  }
  if (
    activeNegotiation?.currentStatus !== undefined &&
    [
      PortfolioItemStatus.UNLINKED,
      PortfolioItemStatus.IN_PROGRESS,
      PortfolioItemStatus.FOLLOWED_UP,
    ].includes(activeNegotiation.currentStatus)
  ) {
    navItems.push({
      key: "end",
      icon: <FiX size={16} color={theme.colors.labelInput} />,
      label: "Encerrar atendimento",
      onClick: () =>
        handleUpdatePortfolioItemStatus(PortfolioItemStatus.FINISHED),
      minWidth: 196,
      fontWeight: 400,
    });
  }
  if (navItems.length === 0) {
    navItems.push({
      key: "none",
      label: "Nenhuma ação disponível",
      onClick: () => {},
      disabled: true,
      minWidth: 196,
      fontWeight: 400,
    });
  }

  const renderDate = (it: IPortfolioItem) => {
    const date =
      (it as any).lastInteraction ||
      (it as any).lastMessageSentAt ||
      it.createdAt;
    return date ? formatDateShort(date as any) : "";
  };

  const detailsForPanel = useMemo(() => {
    if (
      activeDetails &&
      activeNegotiation &&
      activeDetails.id === activeNegotiation.id
    ) {
      return activeDetails;
    }
    return activeNegotiation ?? null;
  }, [activeDetails, activeNegotiation]);

  const customData = detailsForPanel?.customData;
  const customerName = extractCustomerName(customData);
  const portfolioName =
    activeDetails?.portfolioName ?? activeNegotiation?.portfolioName ?? "-";

  const skipKeys = ["NOME_DO_CLIENTE", "NOME_CLIENTE"];
  const customFields = buildCustomDataEntries(customData, skipKeys);

  const mediaItems = useMemo(
    () =>
      messages
        .filter((m) => m.type === "image" || m.type === "file")
        .map((m) => {
          const isImage = m.type === "image";
          const date = m.date || "";
          const blobUrl = isImage ? imageBlobMap[m.id] : undefined;
          const filename =
            (m as any).filename || (isImage ? "imagem.jpg" : "arquivo.pdf");
          return {
            id: m.id,
            kind: isImage ? "image" : "pdf",
            labelOpen: isImage ? "Abrir imagem" : "Abrir PDF",
            date,
            filename,
            blobUrl,
            url: (m as any).url as string | undefined,
            portfolioItemId: (m as any).portfolioItemId as string | undefined,
          };
        }),
    [messages, imageBlobMap]
  );

  async function openImage(item: (typeof mediaItems)[number]) {
    try {
      let urlToOpen = item.blobUrl;
      if (!urlToOpen && item.portfolioItemId && item.url) {
        const blob = await downloadImageFile(item.portfolioItemId, item.url);
        urlToOpen = URL.createObjectURL(blob);
      }
      if (urlToOpen) window.open(urlToOpen, "_blank", "noopener,noreferrer");
    } catch (e) {
      console.error("Erro ao abrir imagem:", e);
    }
  }

  async function openPdf(item: (typeof mediaItems)[number]) {
    try {
      if (!item.portfolioItemId || !item.url) return;
      const blob = await downloadPdfFile(item.portfolioItemId, item.url);
      const blobUrl = URL.createObjectURL(blob);
      window.open(blobUrl, "_blank", "noopener,noreferrer");
    } catch (e) {
      console.error("Erro ao abrir PDF:", e);
    }
  }

  async function downloadMedia(item: (typeof mediaItems)[number]) {
    try {
      if (!item.portfolioItemId || !item.url) return;
      const blob =
        item.kind === "image"
          ? await downloadImageFile(item.portfolioItemId, item.url)
          : await downloadPdfFile(item.portfolioItemId, item.url);
      const blobUrl = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = blobUrl;
      a.download =
        item.filename || (item.kind === "image" ? "imagem.jpg" : "arquivo.pdf");
      document.body.appendChild(a);
      a.click();
      a.remove();
      URL.revokeObjectURL(blobUrl);
    } catch (e) {
      console.error("Erro ao baixar mídia:", e);
    }
  }

  if (hasFetchedList && !loadingList && items.length === 0 && renderEmpty) {
    return <>{renderEmpty}</>;
  }

  return (
    <Container>
      <Sidebar>
        <SidebarHeader>
          <Title>Listagem de negociações</Title>
        </SidebarHeader>

        <NegotiationsList>
          {loadingList && items.length === 0 && <SidebarSkeleton rows={8} />}

          {items.map((negotiation) => (
            <NegotiationItem
              key={negotiation.id}
              isActive={activeNegotiation?.id === negotiation.id}
              status={negotiation.currentStatus}
              onClick={() => setNegotiationId(negotiation.id)}
            >
              <NegotiationInfo>
                <PhoneNumber>
                  {negotiation.waitingBusinessUserResponse && <WaitingDot />}
                  {formatPhone(negotiation.phoneNumber)}
                </PhoneNumber>
                <NegotiationStatus status={negotiation.currentStatus}>
                  <StatusTag
                    label={
                      portfolioItemStatusMap[negotiation.currentStatus].label
                    }
                    variant={
                      portfolioItemStatusMap[negotiation.currentStatus].variant
                    }
                    icon={
                      portfolioItemStatusMap[negotiation.currentStatus].icon
                    }
                    tooltip={
                      portfolioItemStatusMap[negotiation.currentStatus].tooltip
                    }
                    size="small"
                  />
                  {negotiation.waitingBusinessUserResponse && (
                    <div
                      style={{
                        fontSize: 12,
                        fontWeight: 500,
                        color: theme.colors.neutral800,
                      }}
                    >
                      Aguardando há{" "}
                      {timeAgo(new Date(negotiation.lastInteraction ?? ""))}
                    </div>
                  )}
                </NegotiationStatus>
              </NegotiationInfo>
              <NegotiationDate>{renderDate(negotiation)}</NegotiationDate>
            </NegotiationItem>
          ))}
        </NegotiationsList>

        {total > pageSize && (
          <PaginationContainer>
            <Pagination
              current={page}
              pageSize={pageSize}
              total={total}
              onChange={(p, size) => {
                setPage(p);
                setPageSize(size);
              }}
              showSizeChanger={false}
              showLessItems
              style={{ textAlign: "center", justifyContent: "center" }}
            />
          </PaginationContainer>
        )}
      </Sidebar>

      <MainContent>
        <ChatHeader>
          <ChatHeaderLeft>
            <ChatUserPhone>
              {activeNegotiation?.contactName ||
                (activeNegotiation?.phoneNumber
                  ? formatPhone(activeNegotiation.phoneNumber)
                  : "")}
            </ChatUserPhone>
          </ChatHeaderLeft>
          <IconButton>
            <MoreOptionsButton
              navItems={navItems}
              placement="bottomRight"
              arrowPlacement="right"
            />
          </IconButton>
        </ChatHeader>

        <div
          style={{
            position: "relative",
            flex: 1,
            display: "flex",
            flexDirection: "column",
            maxHeight: "730px",
          }}
        >
          {sending && (
            <Overlay>
              <Spinner />
            </Overlay>
          )}

          <ChatMessages>
            {loadingChat ? (
              <ChatSkeleton />
            ) : (
              <>
                {messages.map((m) =>
                  m.type === "audio" ? (
                    <Message key={m.id} {...m} audioSrc={audioBlobMap[m.id]} />
                  ) : m.type === "image" ? (
                    <Message key={m.id} {...m} imageSrc={imageBlobMap[m.id]} />
                  ) : (
                    <Message key={m.id} {...m} />
                  )
                )}
              </>
            )}
          </ChatMessages>

          {selectedFile && (
            <FilePreviewBox>
              {selectedFile.type === "application/pdf" ? (
                <BsFiletypePdf size={20} color={theme.colors.neutral800} />
              ) : selectedFile.type.startsWith("image/") ? (
                <img
                  src={previewUrl || ""}
                  alt="Preview"
                  style={{
                    width: "24px",
                    height: "24px",
                    objectFit: "cover",
                    borderRadius: "4px",
                  }}
                />
              ) : (
                <BsFiletypePdf size={16} color={theme.colors.neutral800} />
              )}
              <FileName>{selectedFile.name}</FileName>
              <RemoveFileButton onClick={handleRemoveFile}>
                <FiX size={18} />
              </RemoveFileButton>
            </FilePreviewBox>
          )}

          <ChatInputRow>
            <FiPaperclip
              size={24}
              style={{ cursor: "pointer" }}
              onClick={handlePaperclipClick}
            />
            <HiddenInput
              ref={fileInputRef}
              type="file"
              accept="application/pdf,image/*"
              onChange={handleSelectFile}
            />
            <input
              type="text"
              placeholder="Envie sua mensagem"
              value={currentMessage}
              onChange={(e) => setCurrentMessage(e.target.value)}
              disabled={sending}
              onKeyDown={(e) => {
                if (
                  e.key === "Enter" &&
                  !sending &&
                  (currentMessage.trim() || selectedFile)
                ) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
            />
            <Button
              variant="primary"
              size="lg"
              icon={<LuSend size={24} />}
              onClick={handleSendMessage}
              label="Enviar"
              disabled={sending}
            />
          </ChatInputRow>
        </div>
      </MainContent>

      <RightPanel $open={rightOpen}>
        {!rightOpen ? (
          <OpenRightFloatingBtn
            onClick={() => setRightOpen(true)}
            aria-label="Abrir painel"
          >
            <PanelRightOpen color={theme.colors.neutral800} size={18} />
          </OpenRightFloatingBtn>
        ) : loadingRight ? (
          <RightPanelSkeleton tab={activeTab as any} />
        ) : (
          <>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                gap: 10,
              }}
            >
              <Tabs
                tabs={tabItems}
                value={activeTab}
                onChange={setActiveTab}
                gap="0px"
              />
              <CloseRightBtn
                onClick={() => setRightOpen(false)}
                aria-label="Fechar painel"
                title="Fechar"
              >
                <CircleX size={18} color={theme.colors.neutral800} />
              </CloseRightBtn>
            </div>

            {activeTab === "dados" && (
              <>
                <InfoSection>
                  <DataInfoTitle>Análise da IA</DataInfoTitle>
                  <InfoItem>
                    <InfoValue>
                      {(
                        activeDetails?.insights ??
                        activeNegotiation?.insights ??
                        ""
                      )
                        .toString()
                        .trim()
                        ? (activeDetails?.insights ??
                          activeNegotiation?.insights)
                        : "Não há dados"}
                    </InfoValue>
                  </InfoItem>
                </InfoSection>

                {customerName && (
                  <InfoSection>
                    <DataInfoTitle>Nome completo</DataInfoTitle>
                    <InfoItem>
                      <InfoValue>{customerName}</InfoValue>
                    </InfoItem>
                  </InfoSection>
                )}

                <InfoSection>
                  <DataInfoTitle>Status</DataInfoTitle>
                  {(activeDetails ?? activeNegotiation) && (
                    <StatusTag
                      label={
                        portfolioItemStatusMap[
                          (activeDetails ?? activeNegotiation)!.currentStatus
                        ].label
                      }
                      variant={
                        portfolioItemStatusMap[
                          (activeDetails ?? activeNegotiation)!.currentStatus
                        ].variant
                      }
                      icon={
                        portfolioItemStatusMap[
                          (activeDetails ?? activeNegotiation)!.currentStatus
                        ].icon
                      }
                      tooltip={
                        portfolioItemStatusMap[
                          (activeDetails ?? activeNegotiation)!.currentStatus
                        ].tooltip
                      }
                      size="small"
                    />
                  )}
                </InfoSection>

                <InfoSection>
                  <DataInfoTitle>Telefone</DataInfoTitle>
                  <InfoItem>
                    <InfoValue>
                      {formatPhone(detailsForPanel?.phoneNumber || "") || "-"}
                    </InfoValue>
                  </InfoItem>
                </InfoSection>

                <InfoSection>
                  <DataInfoTitle>Portfólio</DataInfoTitle>
                  <InfoItem>
                    <InfoValue>{portfolioName}</InfoValue>
                  </InfoItem>
                </InfoSection>

                {customFields.map((f) => (
                  <InfoSection key={f.key}>
                    <DataInfoTitle>{f.label}</DataInfoTitle>
                    <InfoItem>
                      <InfoValue>{f.value}</InfoValue>
                    </InfoItem>
                  </InfoSection>
                ))}
              </>
            )}

            {activeTab === "midias" && (
              <InfoSection>
                <MediaHeader>
                  <MediaHeaderTitle>Mídias</MediaHeaderTitle>
                  <Caret
                    size={18}
                    $open={mediaOpen}
                    onClick={() => setMediaOpen((v) => !v)}
                  />
                </MediaHeader>

                {mediaOpen && (
                  <MediaList>
                    {mediaItems.length === 0 && (
                      <InfoLabel>Nenhum arquivo encontrado</InfoLabel>
                    )}

                    {mediaItems.map((it) => (
                      <MediaRow key={it.id}>
                        <Thumb>
                          {it.kind === "image" ? (
                            it.blobUrl ? (
                              <img src={it.blobUrl} alt={it.filename} />
                            ) : (
                              <FaRegFileImage size={18} />
                            )
                          ) : (
                            <FaRegFilePdf size={18} />
                          )}
                        </Thumb>

                        <MediaMeta>
                          <MediaDate>{it.date}</MediaDate>
                          {it.kind === "image" ? (
                            <a
                              href="#"
                              onClick={(e) => {
                                e.preventDefault();
                                openImage(it);
                              }}
                            >
                              Abrir imagem
                            </a>
                          ) : (
                            <a
                              href="#"
                              onClick={(e) => {
                                e.preventDefault();
                                openPdf(it);
                              }}
                            >
                              Abrir PDF
                            </a>
                          )}
                        </MediaMeta>

                        <DownloadBtn
                          onClick={() => downloadMedia(it)}
                          title="Download"
                        >
                          <CircleArrowDown color="#2757FF" size={18} />
                        </DownloadBtn>
                      </MediaRow>
                    ))}
                  </MediaList>
                )}
              </InfoSection>
            )}

            {/* {activeTab === "atividades" && (
              <InfoSection>
                <InfoTitle>
                  Linha do tempo{" "}
               
                </InfoTitle>

                {timelineOpen && (
                  <TimelineBox>
                    {timeline.length === 0 ? (
                      <InfoLabel>Nenhum evento disponível.</InfoLabel>
                    ) : (
                      timeline.map((it) => (
                        <TLRow key={`${it.label}-${it.date.toISOString()}`}>
                          <TLDot />
                          <TLTitle>{it.label}</TLTitle>
                          <TLDate>{formatDateShortTZ(it.date)}</TLDate>
                        </TLRow>
                      ))
                    )}
                  </TimelineBox>
                )}
              </InfoSection>
            )} */}
          </>
        )}
      </RightPanel>
    </Container>
  );
}
