import styled, { keyframes } from "styled-components";
import { ChevronUp } from "lucide-react";
import { theme } from "@/styles/theme";

export const Container = styled.div`
  display: flex;
  height: 100vh;
  background: #f5f5f5;
  position: relative;
  padding: 10px;
  max-height: 813px;
  min-height: 0;
  border-radius: 8px;
  box-shadow:
    0px 4px 8px rgba(0, 0, 0, 0.05),
    0px 2px 4px rgba(0, 0, 0, 0.05),
    0px 1px 2px rgba(0, 0, 0, 0.05);
`;

export const Sidebar = styled.div`
  width: 276px;
  max-width: 276px;
  min-width: 276px;
  background: ${({ theme }) => theme.colors.neutral200};
  display: flex;
  border-right: 1px solid ${({ theme }) => theme.colors.neutral200};
  flex-direction: column;
  padding-right: 4px;
  min-height: 0;
`;

export const SidebarHeader = styled.div`
  padding: 12px;
`;

export const Title = styled.h1`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.neutral900};
  margin: 0;
  display: flex;
`;

export const NegotiationsList = styled.div`
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  border: 1px solid ${({ theme }) => theme.colors.neutral600};
  border-radius: 8px;
  max-width: 276px;

  &::-webkit-scrollbar {
    width: 5px;
    background: #f0f0f0;
    border-radius: 8px;
  }
  &::-webkit-scrollbar-thumb {
    background: #cccccc;
    border-radius: 8px;
  }
`;

export const NegotiationItem = styled.div<{
  isActive?: boolean;
  status?: string;
}>`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 276px;
  max-height: 106px;

  background: ${(props) =>
    props.isActive ? "#F5F6FF" : ({ theme }) => theme.colors.background};
  border-bottom: 0.5px solid ${({ theme }) => theme.colors.neutral200};

  &:hover {
    background: ${({ theme }) => theme.colors.neutral200};
  }
`;

export const NegotiationInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

export const PhoneNumber = styled.div`
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 14px;
  color: ${({ theme }) => theme.colors.neutral900};
`;

export const NegotiationStatus = styled.div<{ status?: string }>`
  margin-bottom: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

export const NegotiationDate = styled.div`
  font-size: 14px;
  font-weight: 400;
  margin-right: 10px;
  color: ${({ theme }) => theme.colors.neutral800};
`;

export const MainContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  margin-right: 8px;
  min-width: 0;
  min-height: 0;
`;

export const ChatHeader = styled.div`
  padding: 16px;
  display: flex;
  align-items: center;
  border-radius: 8px;
  justify-content: space-between;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 2px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    background: linear-gradient(90deg, #111c9d 0%, #56c3bd 100%);
    pointer-events: none;
  }
`;

export const ChatHeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

export const ChatUserPhone = styled.div`
  font-weight: 500;
  color: ${({ theme }) => theme.colors.neutral900};
  font-size: 14px;
`;

export const ChatMessages = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: ${({ theme }) => theme.colors.background};
  min-height: 0;
  max-height: 730px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  &::-webkit-scrollbar {
    width: 5px;
    background: #f0f0f0;
    border-radius: 8px;
  }
  &::-webkit-scrollbar-thumb {
    background: #cccccc;
    border-radius: 8px;
  }
`;

export const ChatInputRow = styled.div`
  display: flex;
  gap: 24px;
  margin-top: 10px;
  background: #f5f5f5;
  align-items: center;
  padding: 24px;
  max-height: 106px;

  input {
    flex: 1;
    height: 58px;
    padding: 10px 16px;
    background-color: white;
    border-radius: 4px;
    border: 0.5px solid ${({ theme }) => theme.colors.neutral300};
    font-size: 16px;
    box-shadow:
      0px 1px 2px rgba(0, 0, 0, 0.03),
      0px 1px 3px rgba(0, 0, 0, 0.03),
      0px 3px 5px rgba(0, 0, 0, 0.03);
  }
`;

export const PaginationContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  background-color: ${({ theme }) => theme.colors.neutral200};

  .ant-select-outlined .ant-select-selector {
    border-color: ${({ theme }) => theme.colors.primary};
    &:hover {
      border-color: ${({ theme }) => theme.colors.primary};
    }
  }
  :where(.css-dev-only-do-not-override-vrrzze).ant-select-outlined:not(
      .ant-select-disabled
    ):not(.ant-select-customize-input):not(.ant-pagination-size-changer):hover
    .ant-select-selector {
    border-color: ${({ theme }) => theme.colors.primary};
  }
  .ant-pagination-options {
    span {
      font-family: Switzer;
      font-weight: 500;
    }
    div {
      font-family: Switzer;
    }
  }
  .ant-pagination-item-active {
    background-color: ${({ theme }) => theme.colors.primary};
    border: none;
    a {
      font-family: Switzer;
      font-weight: 500;
      font-size: 16px;
      color: white;
      &:hover {
        color: white;
      }
    }
  }
  .ant-pagination-item:not(.ant-pagination-item-active) {
    background-color: transparent;
    a {
      font-family: Switzer;
      font-weight: 500;
      font-size: 16px;
    }
  }
`;

export const IconButton = styled.button`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: transparent;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
`;

export const Overlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
`;

const spin = keyframes`
  to { transform: rotate(360deg); }
`;

export const Spinner = styled.div`
  width: 48px;
  height: 48px;
  border: 5px solid #ccc;
  border-top: 5px solid ${theme.colors.primary};
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
`;

export const RightPanel = styled.div<{ $open?: boolean }>`
  background: white;
  border-radius: 8px;
  overflow-y: auto;
  min-height: 0;
  transition:
    width 0.2s ease,
    min-width 0.2s ease,
    max-width 0.2s ease,
    padding 0.2s ease,
    border-radius 0.2s ease;

  &::-webkit-scrollbar {
    width: 5px;
    background: #f0f0f0;
    border-radius: 8px;
  }
  &::-webkit-scrollbar-thumb {
    background: #cccccc;
    border-radius: 8px;
  }

  width: ${({ $open }) => ($open ? "285px" : "50px")};
  min-width: ${({ $open }) => ($open ? "285px" : "50px")};
  max-width: ${({ $open }) => ($open ? "285px" : "50px")};
  padding: ${({ $open }) => ($open ? "14px" : "0px")};
  border-radius: ${({ $open }) => ($open ? "8px" : "0px")};
  border-left: ${({ $open, theme }) =>
    $open ? `1px solid ${theme.colors.neutral200}` : "none"};
`;

export const TabsRow = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const CloseRightBtn = styled.button`
  display: inline-flex;

  width: 18px;
  height: 18px;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  &:hover {
    background: ${({ theme }) => theme.colors.neutral200};
    border-radius: 8px;
  }
`;

export const OpenRightFloatingBtn = styled.button`
  position: absolute;
  margin-top: 4px;
  top: 23px;
  right: 22px;
  z-index: 30;
  display: none;
  display: flex;
  align-items: center;

  width: 30px;
  height: 30px;
  align-items: center;
  justify-content: center;

  cursor: pointer;

  &:hover {
    background: ${({ theme }) => theme.colors.neutral200};
    border-radius: 8px;
  }
`;
export const InfoSection = styled.div`
  padding: 8px;
  margin-top: 8px;
`;

export const InfoTitle = styled.h3`
  font-size: 14px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.neutral900};
  margin-bottom: 4px;
`;

export const DataInfoTitle = styled.h3`
  font-size: 14px;
  font-weight: 400;
  color: ${({ theme }) => theme.colors.neutral800};
  margin-bottom: 4px;
`;

export const InfoItem = styled.div`
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
`;

export const InfoLabel = styled.span`
  color: ${({ theme }) => theme.colors.neutral800};
  font-size: 14px;
  font-weight: 400;
`;

export const InfoValue = styled.span`
  color: #1f2937;
  font-weight: 500;
  word-break: break-word;
  white-space: pre-wrap;
`;

export const WaitingDot = styled.span`
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: ${({ theme }) => theme.colors.link};
  margin-right: 4px;
  vertical-align: middle;
`;

/*  midias */

export const MediaHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
`;

export const MediaHeaderTitle = styled(InfoTitle)`
  margin: 0;
`;

export const Caret = styled(ChevronUp)<{ $open: boolean }>`
  transition: transform 0.15s ease;
  transform: rotate(${(p) => (p.$open ? 0 : 180)}deg);
  cursor: pointer;
`;

export const MediaList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

export const MediaRow = styled.div`
  display: grid;
  grid-template-columns: 44px 1fr auto;
  align-items: center;
  gap: 10px;
  padding: 6px 4px;
  border-radius: 8px;

  &:hover {
    background: ${({ theme }) => theme.colors.neutral100};
  }
`;

export const Thumb = styled.div`
  width: 44px;
  height: 44px;
  border-radius: 8px;
  overflow: hidden;
  background: ${({ theme }) => theme.colors.neutral200};
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 44px;
    height: 44px;
    object-fit: cover;
    display: block;
  }
`;

export const MediaMeta = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2px;

  a {
    color: ${({ theme }) => theme.colors.link};
    text-decoration: underline;
    font-size: 13px;
    width: fit-content;
  }
`;

export const MediaDate = styled.div`
  font-size: 13px;
  color: ${({ theme }) => theme.colors.neutral900};
`;

export const DownloadBtn = styled.button`
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: none;
  background: transparent;
  display: grid;
  place-items: center;
  cursor: pointer;

  &:hover {
    background: ${({ theme }) => theme.colors.neutral200};
  }
`;

/*atividades */

export const TimelineBox = styled.div`
  border: 1px solid ${({ theme }) => theme.colors.neutral600};
  border-radius: 10px;
  background: #fafafa;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;

  margin-top: 12px;
`;

export const TLRow = styled.div`
  position: relative;
  padding-left: 22px;

  &:not(:last-child)::after {
    content: "";
    position: absolute;
    left: 8px;
    top: 14px;
    bottom: -18px;
    width: 2px;
    background: ${({ theme }) => theme.colors.neutral600};
  }
`;

export const TLDot = styled.span`
  position: absolute;
  left: 4px;
  top: 6px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${({ theme }) => theme.colors.success};
`;

export const TLTitle = styled.div`
  color: ${({ theme }) => theme.colors.neutral800};
  font-weight: 400;
  font-size: 14px;
  margin-bottom: 6px;
`;

export const TLDate = styled.div`
  color: ${({ theme }) => theme.colors.neutral900};
  font-weight: 500;
  font-size: 14px;
`;
