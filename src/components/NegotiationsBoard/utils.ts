/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  IExecutionHistoryEntry,
  IMessageHistory,
} from "@/services/portfolioItemService";
import { IPortfolioItem } from "@/types/portfolio";

export type TimelineItem = { label: string; date: Date };

const TL_ORDER = [
  "Cobrança iniciada",
  "Primeira interação",
  "Negociação concluída",
  "Aguardando atendimento",
] as const;

export function parseISO(d?: string | Date | null): Date | null {
  if (!d) return null;
  const dt = new Date(d);
  return isNaN(dt.getTime()) ? null : dt;
}

export function firstExecDate(
  entries: IExecutionHistoryEntry[],
  predicate: (e: IExecutionHistoryEntry) => boolean
): Date | null {
  const hit = [...entries]
    .filter(predicate)
    .sort(
      (a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    )[0];
  return hit ? new Date(hit.createdAt) : null;
}

export function buildTimelineFromSchemas(opts: {
  exec: IExecutionHistoryEntry[];
  chat: IMessageHistory[];
  item: IPortfolioItem | null | undefined;
}): TimelineItem[] {
  const { exec, chat, item } = opts;

  const dtStarted = firstExecDate(
    exec,
    (e) =>
      e.newStatus?.toUpperCase() === "IN_PROGRESS" ||
      (e.oldStatus?.toUpperCase() === "PENDING" && !!e.newStatus)
  );

  const dtFirstUserMsg = (() => {
    const userMsgs = chat
      .filter((m) => m.role === "user")
      .map((m) => parseISO(m.createdAt as any))
      .filter(Boolean) as Date[];
    return userMsgs.sort((a, b) => a.getTime() - b.getTime())[0] ?? null;
  })();

  const CONCLUDED_SET = new Set(["SUCCEED", "FINISHED", "CLOSED", "CONCLUDED"]);
  const dtConcluded = firstExecDate(exec, (e) =>
    CONCLUDED_SET.has(e.newStatus)
  );

  const dtWaiting = item?.waitingBusinessUserResponse
    ? parseISO((item as any).lastInteraction || (item as any).updatedAt)
    : null;

  const map: { [K in (typeof TL_ORDER)[number]]?: Date | null } = {
    "Cobrança iniciada": dtStarted,
    "Primeira interação": dtFirstUserMsg,
    "Negociação concluída": dtConcluded,
    "Aguardando atendimento": dtWaiting,
  };

  const timeline = TL_ORDER.reduce<TimelineItem[]>((acc, label) => {
    const d = map[label];
    if (d) acc.push({ label, date: d });
    return acc;
  }, []);

  console.log("timeline", timeline);

  return timeline;
}

export const getMessageKey = (m: IMessageHistory) =>
  `${m.id}:${new Date(m.createdAt).getTime()}`;

export function humanizeLabel(label: string) {
  return String(label || "")
    .toLowerCase()
    .replace(/_/g, " ")
    .replace(/\s+/g, " ")
    .trim()
    .replace(/\b\w/g, (l) => l.toUpperCase());
}

export function extractCustomerName(customData?: Record<string, any>) {
  if (!customData) return undefined;
  const targetKeys = ["NOME_DO_CLIENTE", "NOME_CLIENTE"];
  const entry = Object.entries(customData).find(
    ([k, v]) =>
      targetKeys.includes(k.toUpperCase()) &&
      v != null &&
      String(v).trim() !== ""
  );
  return entry ? String(entry[1]) : undefined;
}

export function buildCustomDataEntries(
  customData?: Record<string, any>,
  skipKeysUpper: string[] = []
): { key: string; label: string; value: string }[] {
  if (!customData) return [];
  return Object.entries(customData)
    .filter(([_, v]) => String(v ?? "").trim() !== "")
    .filter(([k]) => !skipKeysUpper.includes(k.toUpperCase()))
    .map(([k, v]) => ({
      key: k,
      label: humanizeLabel(k),
      value: String(v),
    }))
    .sort((a, b) => a.label.localeCompare(b.label, "pt-BR"));
}

type DateLike = Date | string | number | null | undefined;

export type RawMessageForSort = {
  id?: string | number;
  sent?: boolean;
  sent_at?: DateLike;
  time_to_go?: DateLike;
  createdAt?: DateLike;
  updatedAt?: DateLike;
};

function toTs(v: DateLike): number {
  if (v == null) return 0;
  if (v instanceof Date) return v.getTime();
  if (typeof v === "number") return v;
  if (typeof v === "string") {
    const t = Date.parse(v);
    return Number.isNaN(t) ? 0 : t;
  }
  return 0;
}

export function getEffectiveTimestamp(m: RawMessageForSort): number {
  if (m.sent && m.sent_at) return toTs(m.sent_at);
  if (m.time_to_go) return toTs(m.time_to_go);
  const created = toTs(m.createdAt);
  if (created) return created;
  return toTs(m.updatedAt);
}

export function sortConversationByEffectiveTime<T extends RawMessageForSort>(
  arr: T[]
): T[] {
  return [...arr].sort((a, b) => {
    const ta = getEffectiveTimestamp(a);
    const tb = getEffectiveTimestamp(b);
    if (ta !== tb) return ta - tb;

    const ca = toTs(a.createdAt);
    const cb = toTs(b.createdAt);
    if (ca !== cb) return ca - cb;

    const ua = toTs(a.updatedAt);
    const ub = toTs(b.updatedAt);
    if (ua !== ub) return ua - ub;

    return String(a.id ?? "").localeCompare(String(b.id ?? ""));
  });
}

export function getEffectiveDateISO(m: RawMessageForSort): string | undefined {
  const n = getEffectiveTimestamp(m);
  return n ? new Date(n).toISOString() : undefined;
}
