"use client";

import { useState } from "react";
import { Portfolio, PortfolioExecutionStatus } from "@/types/portfolio";
import Button from "@/components/Button";
import { FiXCircle } from "react-icons/fi";
import {
  //setPortfolioStatusToExecuting,
  setPortfolioStatusToCancelled,
} from "@/services/portfolioService";

type Props = {
  portfolio: Portfolio;
  onChange?: (p: Portfolio) => void;
};

export default function PortfolioActions({ portfolio, onChange }: Props) {
  const [loading, setLoading] = useState<
    null | "pause" | "cancel" | "play" | "delete"
  >(null);

  const handleCancel = async () => {
    setLoading("cancel");
    try {
      const updated = await setPortfolioStatusToCancelled(portfolio.id);
      onChange?.(updated);
    } finally {
      setLoading(null);
    }
  };

  // const handleExecute = async () => {
  //   setLoading("play");
  //   try {
  //     const updated = await setPortfolioStatusToExecuting(portfolio.id);
  //     onChange?.(updated);
  //   } finally {
  //     setLoading(null);
  //   }
  // };

  if (
    [
      PortfolioExecutionStatus.EXECUTING,
      PortfolioExecutionStatus.INBOUND,
    ].includes(portfolio.executionStatus)
  ) {
    return (
      <div style={{ display: "flex", gap: 10 }}>
        <Button
          label="Cancelar"
          icon={<FiXCircle size={24} />}
          variant="outlined"
          size="lg"
          loading={loading === "cancel"}
          onClick={handleCancel}
        />
      </div>
    );
  }

  // if (
  //   [
  //     PortfolioExecutionStatus.PAUSED,
  //     PortfolioExecutionStatus.QUEUED,
  //     PortfolioExecutionStatus.CANCELLED,
  //   ].includes(portfolio.executionStatus)
  // ) {
  //   return (
  //     <div style={{ display: "flex", gap: 10 }}>
  //       <Button
  //         label="Iniciar"
  //         icon={<FiPlayCircle size={24} />}
  //         variant="primary"
  //         size="lg"
  //         loading={loading === "play"}
  //         onClick={handleExecute}
  //       />
  //     </div>
  //   );
  // }

  // if (
  //   [
  //     PortfolioExecutionStatus.FINISHED,
  //     PortfolioExecutionStatus.CANCELLED,
  //   ].includes(portfolio.executionStatus)
  // ) {
  //   return (
  //     <Button
  //       label="Deletar"
  //       icon={<FiTrash2 size={24} />}
  //       variant="outlined"
  //       size="lg"
  //       loading={loading === "delete"}
  //       onClick={handleDelete}
  //     />
  //   );
  // }

  return null;
}
