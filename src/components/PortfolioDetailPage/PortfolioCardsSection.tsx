/* eslint-disable @typescript-eslint/no-explicit-any */
import { Card } from "@/components/Card";
import { FiMessageSquare, FiTrendingUp } from "react-icons/fi";
import { Wallet } from "lucide-react";
import { CardsGrid } from "./styles";
import { CardsRow } from "./PortfolioDetailSkeleton";
import { CardSkeleton } from "./PortfolioDetailSkeleton";

type Props = {
  portfolio?: any;
  totalNegociacoes?: number;
  totalConcluidas?: number;
  dealValue: string;
  loadingCards: boolean;
  portfolioAverageTicket: any;
  portfolioFirstMessageMetrics: any;
};

export function PortfolioCardsSection({
  dealValue,
  loadingCards,
  portfolioAverageTicket,
  portfolioFirstMessageMetrics,
}: Props) {
  if (loadingCards) {
    return (
      <CardsRow>
        <CardSkeleton />
        <CardSkeleton />
        <CardSkeleton />
      </CardsRow>
    );
  }

  const averageTicketValue =
    portfolioAverageTicket?.averageTicket &&
    !portfolioAverageTicket.averageTicket.replace(/\s/g, "").includes("NaN")
      ? portfolioAverageTicket.averageTicket
      : "R$ 0,00";

  return (
    <CardsGrid>
      <Card
        title="Cobranças iniciadas"
        value={portfolioFirstMessageMetrics?.totalFirstMessagesSent || 0}
        subtitle="Total de cobranças"
        icon={<FiMessageSquare size={25} color="#0F1681" />}
      />
      <Card
        title="Ticket médio"
        value={averageTicketValue}
        subtitle="Valor médio por cobrança"
        icon={<Wallet size={25} color="#0F1681" />}
      />
      <Card
        title="Valor recuperado"
        value={dealValue || "-"}
        subtitle="Receita projetada da conversão"
        icon={<FiTrendingUp size={25} color="#0F1681" />}
      />
    </CardsGrid>
  );
}
