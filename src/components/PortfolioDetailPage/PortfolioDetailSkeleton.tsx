import styled, { keyframes } from "styled-components";

const shimmer = keyframes`
  0% { background-position: -500px 0; }
  100% { background-position: 500px 0; }
`;

export const Skeleton = styled.div<{
  width?: string;
  height?: string;
  radius?: string;
}>`
  background: #eee;
  background-image: linear-gradient(90deg, #eee 0px, #f5f5f5 40px, #eee 80px);
  background-size: 600px 100%;
  animation: ${shimmer} 1.2s infinite linear;
  border-radius: ${({ radius }) => radius || "8px"};
  width: ${({ width }) => width || "100%"};
  height: ${({ height }) => height || "20px"};
  margin-bottom: 12px;
`;

const Container = styled.div`
  display: flex;
  gap: 24px;
  max-width: 1204px;
  margin: 0 auto;
  padding: 32px 16px 0 16px;
`;

const DataContainer = styled.div`
  display: flex;
  flex-direction: column;
  max-width: 860px;
  flex: 1;
`;

export const CardsRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
  margin: 24px 0;
`;

export const CardSkeleton = styled(Skeleton)`
  height: 159px;
`;

const TabsSkeleton = styled.div`
  display: flex;
  align-items: center;
  gap: 24px;
  margin: 24px 0 18px 0;
`;

const TabSkeleton = styled(Skeleton)`
  width: 860px;
  height: 37px;
`;

const TableSkeletonWrapper = styled.div`
  width: 100%;
  border-radius: 12px;
  border: 1px solid #ebebeb;
  margin-bottom: 16px;
`;

const TableHeader = styled.div`
  display: flex;
  background: #f6f6f6;
  justify-content: space-between;
  padding: 12px 24px 0px 24px;
  border-bottom: 1px solid #ebebeb;
  height: 50px;
  align-items: center;
  gap: 16px;
`;

const TableCellHeader = styled(Skeleton)`
  height: 18px;
  width: 120px;
`;

const TableRow = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  justify-content: space-between;
  padding: 12px 24px 0px 24px;
  height: 46px;
  border-bottom: 0.5px solid #efefef;
`;

const TableCell = styled(Skeleton)`
  height: 16px;
  width: 110px;
`;

export const ServiceQueueContainer = styled.div`
  display: flex;
  flex-direction: column;
  min-width: 240px;
  width: 280px;
  max-height: 745px;
  padding: 20px;
  gap: 12px;
  margin-top: 4px;
  background-color: #fcfbfb;
  border-radius: 12px;
`;

export const QueueHeader = styled.div`
  margin-bottom: 8px;
`;

export const QueueItem = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  background: #f9f9f9;
  border-radius: 12px;
  height: 64px;
  padding: 0 14px;
  margin-bottom: 8px;
`;

export default function PortfolioDetailSkeleton() {
  return (
    <>
      {/* Header skeleton */}
      <Skeleton
        width="350px"
        height="32px"
        radius="8px"
        style={{ margin: "32px auto 0 32px" }}
      />
      <Container>
        <DataContainer>
          {/* Breadcrumb e filtros */}
          <Skeleton width="254px" height="18px" radius="6px" />
          <Skeleton
            width="505px"
            height="34px"
            radius="8px"
            style={{ marginBottom: 16 }}
          />

          {/* Cards */}
          <CardsRow>
            <CardSkeleton />
            <CardSkeleton />
            <CardSkeleton />
          </CardsRow>

          {/* Abas */}
          <TabsSkeleton>
            <TabSkeleton />
          </TabsSkeleton>

          {/* Filtros tabela */}
          <div style={{ display: "flex", gap: 16, marginBottom: 12 }}>
            <Skeleton width="413px" height="58px" />
            <Skeleton width="182px" height="58px" />
          </div>

          {/* Tabela */}
          <TableSkeletonWrapper>
            <TableHeader>
              {Array.from({ length: 5 }).map((_, i) => (
                <TableCellHeader key={i} />
              ))}
            </TableHeader>
            {/* Linhas */}
            {Array.from({ length: 7 }).map((_, i) => (
              <TableRow key={i}>
                {Array.from({ length: 5 }).map((_, j) => (
                  <TableCell key={j} />
                ))}
              </TableRow>
            ))}
          </TableSkeletonWrapper>
        </DataContainer>

        {/* Service Queue */}
        <ServiceQueueContainer>
          <QueueHeader>
            <Skeleton width="180px" height="24px" />
            <Skeleton width="130px" height="14px" />
          </QueueHeader>
          {Array.from({ length: 4 }).map((_, i) => (
            <QueueItem key={i}>
              <Skeleton width="24px" height="24px" radius="12px" />
              <div style={{ flex: 1 }}>
                <Skeleton width="100px" height="14px" />
                <Skeleton width="55px" height="14px" />
              </div>
            </QueueItem>
          ))}
        </ServiceQueueContainer>
      </Container>
    </>
  );
}
