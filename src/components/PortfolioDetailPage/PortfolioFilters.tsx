/* eslint-disable @typescript-eslint/no-explicit-any */
import Tabs from "@/components/Tabs";
import SelectInput from "@/components/Form/SelectInput";
import TextInput from "@/components/Form/Input";
import { FilterRow, TabsSection, SwitchWrapper } from "./styles";
import { portfolioItemStatusMap } from "@/constants/portfolioItemStatusMap";
import { FiSearch } from "react-icons/fi";
import { theme } from "@/styles/theme";
import { onlyNumbers } from "@/utils/formatPhone";
import { Switch } from "antd";

type Props = {
  tab: string;
  setTab: (t: any) => void;
  totalNegociacoes: number;
  totalConcluidas: number;
  phoneFilter: string;
  setPhoneFilter: (v: string) => void;
  statusFilter: string;
  setStatusFilter: (v: string) => void;
  onlyInteraction: boolean;
  setOnlyInteraction: (v: boolean) => void;
};

export function PortfolioFilters({
  tab,
  setTab,
  totalNegociacoes,
  totalConcluidas,
  phoneFilter,
  setPhoneFilter,
  statusFilter,
  setStatusFilter,
  onlyInteraction,
  setOnlyInteraction,
}: Props) {
  return (
    <TabsSection>
      <Tabs
        tabs={[
          {
            label: "Negociações",
            value: "negociacoes",
            count: totalNegociacoes - totalConcluidas,
          },
          {
            label: "Concluídas",
            value: "concluidas",
            count: totalConcluidas,
          },
        ]}
        value={tab}
        onChange={setTab}
      />

      <FilterRow>
        <div style={{ maxWidth: 700, width: "100%", minWidth: 450 }}>
          <TextInput
            placeholder="Pesquisar por telefone"
            value={phoneFilter}
            iconLeft={<FiSearch size={24} color={theme.colors.labelInput} />}
            onChange={(e) => setPhoneFilter(onlyNumbers(e.target.value))}
            onPaste={(e) => {
              e.preventDefault();
              const text = e.clipboardData.getData("Text");
              setPhoneFilter(onlyNumbers(text));
            }}
            selectLeft={
              <SelectInput
                variant="embedded"
                value="todos"
                placeholder="Telefone"
                options={[{ label: "Telefone", value: "telefone" }]}
                onChange={() => {}}
              />
            }
          />
        </div>
        {tab !== "concluidas" && (
          <>
            <div style={{ minWidth: 203, maxWidth: 400, width: "100%" }}>
              <SelectInput
                labelInside="Status"
                options={[
                  { label: "Todos", value: "todos" },
                  ...Object.entries(portfolioItemStatusMap).map(
                    ([key, conf]) => ({
                      label: conf.label,
                      value: key,
                    })
                  ),
                ]}
                value={statusFilter}
                onChange={setStatusFilter}
              />
            </div>
            <SwitchWrapper>
              <Switch checked={onlyInteraction} onChange={setOnlyInteraction} />
              <p>Apenas com interação</p>
            </SwitchWrapper>
          </>
        )}
      </FilterRow>
    </TabsSection>
  );
}
