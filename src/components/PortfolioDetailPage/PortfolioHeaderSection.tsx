/* eslint-disable @typescript-eslint/no-explicit-any */
import Breadcrumb from "@/components/Breadcrumb";
import DateRangeSelector from "@/components/DateRangeSelector";
import PortfolioActions from "@/components/PortfolioActions";
import { TopSectionWrapper } from "./styles";

type Props = {
  crumbs: any;
  portfolio: any;
  onPortfolioChange: (p: any) => void;
  dateFilter: string;
  setDateFilter: (v: string) => void;
  setCustomRange: (v: [string, string] | undefined) => void;
};

export function PortfolioHeaderSection({
  crumbs,
  portfolio,
  onPortfolioChange,
  dateFilter,
  setDateFilter,
  setCustomRange,
}: Props) {
  return (
    <TopSectionWrapper>
      <div style={{ display: "flex", flexDirection: "column", gap: 24 }}>
        <Breadcrumb items={crumbs} padding="0" margin="0" />
        <DateRangeSelector
          initialValue={dateFilter}
          onChange={(value, dates) => {
            setDateFilter(value);
            if (value === "custom" && dates) {
              setCustomRange(dates);
            } else {
              setCustomRange(undefined);
            }
          }}
        />
      </div>
      <div>
        <PortfolioActions portfolio={portfolio} onChange={onPortfolioChange} />
      </div>
    </TopSectionWrapper>
  );
}
