/* eslint-disable @typescript-eslint/no-explicit-any */
// src/components/PortfolioServiceQueue/index.tsx
"use client";
import { useRouter } from "next/navigation";
import ServiceQueue from "@/components/ServiceQueue";
import { formatDateLong, formatDateToHour, timeAgo } from "@/utils/formatDate";
import { portfolioItemStatusMap } from "@/constants/portfolioItemStatusMap";
import { PortfolioItemStatus } from "@/types/portfolio";
import { useQueueName } from "@/hooks/useQueueName";
import {
  ServiceQueueContainer,
  QueueHeader,
  QueueItem,
  Skeleton,
} from "./PortfolioDetailSkeleton";

export function PortfolioServiceQueue() {
  const router = useRouter();
  const { items, isLoading } = useQueueName();

  function openChat(negotiationId: string) {
    const qs = new URLSearchParams();
    qs.set("negotiationId", negotiationId);
    router.push(`/negotiations/list?${qs.toString()}`);
  }

  if (isLoading) {
    return (
      <ServiceQueueContainer>
        <QueueHeader>
          <Skeleton width="180px" height="24px" />
          <Skeleton width="130px" height="14px" />
        </QueueHeader>
        {Array.from({ length: 4 }).map((_, i) => (
          <QueueItem key={i}>
            <Skeleton width="24px" height="24px" radius="12px" />
            <div style={{ flex: 1 }}>
              <Skeleton width="100px" height="14px" />
              <Skeleton width="55px" height="14px" />
            </div>
          </QueueItem>
        ))}
      </ServiceQueueContainer>
    );
  }

  if (items.length === 0) {
    return null;
  }

  const sortedItems = [...items].sort((a, b) => {
    const dateA = a.lastInteraction ? new Date(a.lastInteraction).getTime() : 0;
    const dateB = b.lastInteraction ? new Date(b.lastInteraction).getTime() : 0;
    return dateA - dateB;
  });

  const serviceItems = sortedItems.map((item: any) => {
    const statusConfig =
      portfolioItemStatusMap[item.currentStatus as PortfolioItemStatus];
    const last = item.lastInteraction
      ? new Date(item.lastInteraction)
      : new Date();

    return {
      id: item.id,
      date: formatDateLong(last),
      time: formatDateToHour(last),
      relative: timeAgo(last),
      customerName:
        item.customData?.NOME_DO_CLIENTE ?? item.customData?.NOME_CLIENTE ?? "",
      customerPhone: item.customData?.PHONE_NUMBER ?? item.phoneNumber ?? "",
      status:
        statusConfig &&
        ["success", "warning", "error", "info", "neutral"].includes(
          statusConfig.variant
        )
          ? (statusConfig.variant as any)
          : undefined,
      onClick: () => openChat(item.id),
    };
  });

  return (
    <ServiceQueue
      title={`Fila de atendimento (${serviceItems.length})`}
      subtitle="Aguardando pelo operador"
      items={serviceItems}
    />
  );
}
