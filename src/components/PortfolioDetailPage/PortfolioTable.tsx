/* eslint-disable @typescript-eslint/no-explicit-any */
import { PortfolioTableWrapper } from "./styles";
import { Table } from "@/components/Table";
import StatusTag from "@/components/StatusTag";
import { ExternalLink } from "lucide-react";
import { theme } from "@/styles/theme";
import { formatDateDefault } from "@/utils/formatDate";
import { formatPhone } from "@/utils/formatPhone";
import { portfolioItemStatusMap } from "@/constants/portfolioItemStatusMap";
import { PortfolioItemStatus } from "@/types/portfolio";
import { useRouter } from "next/navigation";

type Props = {
  portfolioItems: any;
  page: number;
  limit: number;
  loading: boolean;
  setPage: (v: number) => void;
  setLimit: (v: number) => void;
  onOpenChat?: (item: any) => void;
};

export function PortfolioTable({
  portfolioItems,
  page,
  limit,
  loading,
  setPage,
  setLimit,
  onOpenChat,
}: Props) {
  const router = useRouter();

  function openChatFallback(negotiationId: string) {
    router.push(`/negotiations/list/${negotiationId}`);
  }

  return (
    <PortfolioTableWrapper>
      <Table
        totalCount={portfolioItems.total}
        pageIndex={page - 1}
        pageSize={limit}
        isLoading={loading}
        onPageChange={(newPage, newSize) => {
          setPage(newPage + 1);
          setLimit(newSize);
        }}
        columns={[
          { header: "Telefone", accessorKey: "phoneNumber" },
          {
            header: "Última mensagem recebida",
            accessorKey: "lastInteraction",
          },
          {
            header: "Status",
            accessorKey: "statusTagConfig",
            cell: (cell: any) => {
              const config = cell.row.original.statusTagConfig;
              if (!config) return null;
              return (
                <StatusTag
                  label={config.label}
                  variant={config.variant}
                  icon={config.icon}
                  tooltip={config.tooltip}
                  size="medium"
                />
              );
            },
          },
          {
            header: "Chat",
            accessorKey: "buttonChat",
            cell: (cell: any) => (
              <ExternalLink
                size={24}
                color={theme.colors.link}
                style={{ cursor: "pointer", marginLeft: 8 }}
                onClick={() =>
                  onOpenChat
                    ? onOpenChat(cell.row.original)
                    : openChatFallback(cell.row.original.id)
                }
              />
            ),
          },
        ]}
        data={portfolioItems.items.map((item: any) => {
          const statusConfig =
            portfolioItemStatusMap[item.currentStatus as PortfolioItemStatus];
          return {
            ...item,
            lastInteraction: item.lastInteraction
              ? formatDateDefault(new Date(item.lastInteraction))
              : "Não há interação",
            phoneNumber: formatPhone(item.phoneNumber),
            statusTagConfig: statusConfig,
            buttonChat: (
              <ExternalLink
                size={24}
                color={theme.colors.link}
                style={{ cursor: "pointer", marginLeft: 8 }}
                onClick={() =>
                  onOpenChat ? onOpenChat(item) : openChatFallback(item.id)
                }
              />
            ),
          };
        })}
      />
    </PortfolioTableWrapper>
  );
}
