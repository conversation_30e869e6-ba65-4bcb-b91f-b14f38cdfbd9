import styled from "styled-components";
import { theme } from "@/styles/theme";

const RIGHT_MIN = 280;
const GRID_GAP = 24;
const PADX = 80;
export const TWO_COL_BP = `${RIGHT_MIN + 830 + GRID_GAP + PADX}px`;

export const RightRail = styled.aside`
  min-width: 0;
`;

export const PortfolioTableWrapper = styled.div`
  table th:nth-child(1),
  table td:nth-child(1) {
    width: 40%;
  }
  table th:nth-child(2),
  table td:nth-child(2) {
    width: 40%;
  }
  table th:nth-child(3),
  table td:nth-child(3) {
    width: 40%;
  }
  table th:nth-child(4),
  table td:nth-child(4) {
    width: 20%;
  }
`;

export const DataContainer = styled.div`
  display: flex;
  flex-direction: column;
  min-width: 0;
  @media (min-width: ${TWO_COL_BP}) {
    min-width: 830px;
  }
`;

export const ServiceQueueContainer = styled.div`
  display: flex;
  flex-direction: column;
  min-width: 240px;
  flex-wrap: wrap;
`;

export const ContentGrid = styled.div<{ $hasRightRail?: boolean }>`
  display: grid;
  --gap: 16px;
  flex-wrap: wrap;
  gap: var(--gap);
  align-items: start;
  grid-template-columns: 1fr;

  @media (min-width: 1400px) {
    grid-template-columns: ${({ $hasRightRail }) =>
      $hasRightRail
        ? `minmax(830px, 1fr) clamp(${RIGHT_MIN}px, 24vw, 360px)`
        : `1fr`};
  }
`;

export const TopSectionWrapper = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-end;
`;

export const FilterRow = styled.div`
  display: flex;
  gap: 16px;
  flex-direction: row;
`;

export const SwitchWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  min-width: 220px;
  padding: 0 10px;

  p {
    font-size: 14px;
    font-weight: 500;
    color: ${theme.colors.neutral900};
    margin: 0;
  }
`;

export const CardsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-top: 24px;
`;

export const TabsSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 24px;
`;
