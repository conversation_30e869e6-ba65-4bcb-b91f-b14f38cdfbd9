import styled from "styled-components";
import TextInput from "@/components/Form/Input";
import SelectInput from "@/components/Form/SelectInput";
import { FiSearch } from "react-icons/fi";
import { theme } from "@/styles/theme";
import { portfolioStatusOptions } from "@/constants/portfolioStatusMap";

const InputWrapper = styled.div`
  display: flex;
  flex-direction: row;
  gap: 16px;
  max-width: 95%;
  flex-wrap: wrap;
  width: 100%;
`;

const SectionFilters = styled.div`
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 24px;
  gap: 16px;
`;

const InputArea = styled.div`
  flex: 1 1 420px;
  min-width: 400px;
`;

const SelectBox = styled.div`
  flex: 1 1 200px;
  min-width: 200px;
  max-width: 360px;
`;

type PortfolioFiltersProps = {
  searchTerm: string;
  setSearchTerm: (v: string) => void;
  selectedEmbedded: string;
  setSelectedEmbedded: (v: string) => void;
  selectedExecutionStatus: string;
  setSelectedExecutionStatus: (v: string) => void;
  selectedServiceQueue: string;
  setSelectedServiceQueue: (v: string) => void;
};

const searchOptions = [{ label: "Portfólio", value: "all" }];

//waitingBusinessUserResponseOptions no projeto antigo
const serviceQueueOptions = [
  { label: "Todos", value: "all" },
  { label: "Sim", value: "true" },
  { label: "Não", value: "false" },
];

export default function PortfolioFilters({
  searchTerm,
  setSearchTerm,
  selectedEmbedded,
  setSelectedEmbedded,
  selectedExecutionStatus,
  setSelectedExecutionStatus,
  selectedServiceQueue,
  setSelectedServiceQueue,
}: PortfolioFiltersProps) {
  return (
    <SectionFilters>
      <InputWrapper>
        <InputArea>
          <TextInput
            placeholder="Pesquisar por nome"
            iconLeft={<FiSearch size={24} color={theme.colors.labelInput} />}
            selectLeft={
              <SelectInput
                variant="embedded"
                value={selectedEmbedded}
                placeholder="Portfólio"
                options={searchOptions}
                onChange={setSelectedEmbedded}
              />
            }
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </InputArea>
        <SelectBox>
          <SelectInput
            variant="standalone"
            labelInside="Fila de atendimento"
            options={serviceQueueOptions}
            value={selectedServiceQueue}
            onChange={setSelectedServiceQueue}
          />
        </SelectBox>
        <SelectBox>
          <SelectInput
            variant="standalone"
            labelInside="Status"
            options={portfolioStatusOptions}
            value={selectedExecutionStatus}
            onChange={setSelectedExecutionStatus}
          />
        </SelectBox>
      </InputWrapper>
    </SectionFilters>
  );
}
