import React from "react";
import styled, { keyframes } from "styled-components";
import StatusTag from "@/components/StatusTag";
import { portfolioStatusMap } from "@/constants/portfolioStatusMap";
import { formatDateDefault } from "@/utils/formatDate";
import { PortfolioWithQueueFlag } from "@/hooks/usePortfoliosWithQueueFlag";

const GridContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: auto auto;
  height: 132px;
  gap: 20px;
`;

const MetricItem = styled.div`
  display: flex;
  gap: 16px;
  max-height: 122px;
  flex-direction: column;
  justify-content: space-between;
`;

const MetricLabel = styled.p`
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 5px;
  color: ${({ theme }) => theme.colors.neutral800};
`;

const MetricMainValue = styled.h1`
  font-weight: 600;
  font-size: 32px;
  margin-bottom: 5px;
  color: ${({ theme }) => theme.colors.success};
`;

const MetricValue = styled.h1`
  font-weight: 500;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.neutral900};
`;

const shimmer = keyframes`
  0% { background-position: -40px 0; }
  100% { background-position: 40px 0; }
`;

const MiniSkeleton = styled.span`
  display: inline-block;
  width: 32px;
  height: 24px;
  border-radius: 6px;
  background: linear-gradient(90deg, #ececec 25%, #f5f5f5 50%, #ececec 75%);
  background-size: 80px 100%;
  animation: ${shimmer} 1.2s infinite;
`;

type PortfolioGridProps = { portfolio: PortfolioWithQueueFlag };

export default function PortfolioGrid({ portfolio }: PortfolioGridProps) {
  return (
    <GridContainer>
      <MetricItem>
        <div>
          <MetricLabel>Valor recuperado</MetricLabel>
          <MetricMainValue>{portfolio.dealValue}</MetricMainValue>
        </div>
        <div>
          <MetricLabel>Data da importação</MetricLabel>
          <MetricValue>
            {formatDateDefault(portfolio.importFinishedAt)}
          </MetricValue>
        </div>
      </MetricItem>

      <MetricItem>
        <div>
          <MetricLabel>Itens importados</MetricLabel>
          <MetricValue>{portfolio.totalSuccessQuantity}</MetricValue>
        </div>
        <div>
          <MetricLabel>Fila de atendimento</MetricLabel>
          {portfolio.waitingBusinessUserResponseCount !== undefined &&
          portfolio.waitingBusinessUserResponseCount !== null ? (
            <StatusTag
              label={portfolio.waitingBusinessUserResponseCount.toString()}
              variant={
                (portfolio.waitingBusinessUserResponseCount ?? 0) > 0
                  ? "warning"
                  : "neutral"
              }
              size="small"
            />
          ) : (
            <MiniSkeleton />
          )}
        </div>
      </MetricItem>

      <MetricItem>
        <div>
          <MetricLabel>Atendimentos iniciados</MetricLabel>

          <MetricValue>
            {portfolio.startedAttendancesCount !== undefined &&
            portfolio.startedAttendancesCount !== null ? (
              portfolio.startedAttendancesCount
            ) : (
              <MiniSkeleton />
            )}
          </MetricValue>
        </div>
        <div style={{ marginBottom: 3 }}>
          <StatusTag
            label={portfolioStatusMap[portfolio.executionStatus].label}
            variant={portfolioStatusMap[portfolio.executionStatus].variant}
            icon={portfolioStatusMap[portfolio.executionStatus].icon}
            tooltip={portfolioStatusMap[portfolio.executionStatus].tooltip}
            size="medium"
          />
        </div>
      </MetricItem>
    </GridContainer>
  );
}
