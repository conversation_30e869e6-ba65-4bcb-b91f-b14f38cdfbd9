import {
  ProgressFooterWrapper,
  ProgressLabelRow,
  ProgressPercent,
  ProgressLabel,
  ProgressBarContainer,
  ProgressBarTrack,
  ProgressBarFill,
} from "./styles";

type ProgressFooterProps = {
  percent: number;
};

export function ProgressFooter({ percent }: ProgressFooterProps) {
  return (
    <ProgressFooterWrapper>
      <ProgressLabelRow>
        <ProgressPercent>{percent}%</ProgressPercent>
      </ProgressLabelRow>
      <ProgressBarContainer>
        <ProgressLabel>Progresso da importação</ProgressLabel>
        <ProgressBarTrack>
          <ProgressBarFill $percent={percent} />
        </ProgressBarTrack>
      </ProgressBarContainer>
    </ProgressFooterWrapper>
  );
}

export default ProgressFooter;
