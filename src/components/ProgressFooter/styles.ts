import styled from "styled-components";
import { theme } from "@/styles/theme";

export const ProgressFooterWrapper = styled.div`
  width: 100%;
  padding: 8px 16px;
  gap: 16px;
  align-items: flex-end;
  background-color: ${theme.colors.neutral200};
  display: flex;
  flex-direction: row;
  border-radius: 0 0 10px 10px;
  border-top: 0.5px solid ${theme.colors.neutral400};
  min-height: 49px;
`;

export const ProgressLabelRow = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
`;

export const ProgressPercent = styled.span`
  color: ${theme.colors.primary};
  font-size: 14px;
  font-weight: 500;
  text-align: left;
`;

export const ProgressLabel = styled.span`
  color: ${theme.colors.neutral700};
  font-size: 12px;
  font-weight: 500;
`;

export const ProgressBarContainer = styled.div`
  width: 100%;
  margin-top: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

export const ProgressBarTrack = styled.div`
  width: 100%;
  height: 8px;
  background: ${theme.colors.neutral600};
  border-radius: 4px;
  border: 0.5px solid ${theme.colors.neutral400};
  overflow: hidden;
  min-height: 9px;
`;

export const ProgressBarFill = styled.div<{ $percent: number }>`
  height: 100%;
  background: ${theme.colors.primary};
  border-radius: 4px;
  width: ${({ $percent }) => Math.max(0, Math.min($percent, 100))}%;
  transition: width 0.3s cubic-bezier(0.7, 0.3, 0.3, 1);
`;
