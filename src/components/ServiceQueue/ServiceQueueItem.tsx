"use client";
import React from "react";
import {
  Card,
  StatusBar,
  Content,
  DateInfo,
  ActionIcon,
  Relative,
  TimeInfo,
} from "./styles";
import { Calendar, Clock, ExternalLink, Phone, User } from "lucide-react";
import { theme } from "@/styles/theme";
import { formatPhone } from "@/utils/formatPhone";

export interface ServiceQueueItemProps {
  id: string;
  date: string;
  time: string;
  relative: string;
  customerName: string;
  customerPhone: string;
  status?: "info" | "success" | "warning" | "error" | "neutral";
  onClick?: () => void;
  selected?: boolean;
  showActionIcon?: boolean;
  onActionClick?: () => void;
  maxWidth?: string | number;
  fontSize?: number | string;
  relativeFontSize?: number | string;
  relativeFontWeight?: number | string;
}

export default function ServiceQueueItem({
  date,
  time,
  relative,
  customerName,
  customerPhone,
  status = "neutral",
  onClick,
  selected,
  showActionIcon = true,
  onActionClick,
  maxWidth = 342,
  fontSize = 16,
  relativeFontSize = 13,
  relativeFontWeight = 500,
}: ServiceQueueItemProps) {
  return (
    <Card
      onClick={onClick}
      $selected={selected}
      $maxWidth={maxWidth}
      tabIndex={0}
      role="button"
    >
      <StatusBar status={status} />
      <div style={{ display: "flex", gap: 8, flexDirection: "column" }}>
        <Content
          style={{ display: "flex", gap: 8, justifyContent: "space-between" }}
        >
          <div style={{ display: "flex", gap: 4 }}>
            <div>
              <User
                size={18}
                color={selected ? theme.colors.link : theme.colors.neutral800}
              />
            </div>
            <DateInfo $fontSize={fontSize} $selected={selected}>
              {customerName}
            </DateInfo>
          </div>
          {showActionIcon && (
            <ActionIcon onClick={onActionClick}>
              <ExternalLink size={20} />
            </ActionIcon>
          )}
        </Content>
        <Content>
          <div>
            <Phone
              size={18}
              color={selected ? theme.colors.link : theme.colors.neutral800}
            />
          </div>
          <DateInfo $fontSize={fontSize} $selected={selected}>
            {formatPhone(customerPhone)}
          </DateInfo>
        </Content>
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "flex-end",
            gap: 24,
          }}
        >
          <Content>
            <div>
              <Calendar
                size={18}
                color={selected ? theme.colors.link : theme.colors.neutral800}
              />
            </div>
            <DateInfo $fontSize={fontSize} $selected={selected}>
              {date}
            </DateInfo>
          </Content>

          <Content style={{ marginTop: 4 }}>
            <div>
              <Clock
                size={18}
                color={selected ? theme.colors.link : theme.colors.neutral800}
              />
            </div>
            <TimeInfo $fontSize={fontSize} $selected={selected}>
              {time}
            </TimeInfo>
          </Content>
        </div>
        <Relative $fontSize={relativeFontSize} $fontWeight={relativeFontWeight}>
          Aguardando há {relative}
        </Relative>{" "}
      </div>
    </Card>
  );
}
