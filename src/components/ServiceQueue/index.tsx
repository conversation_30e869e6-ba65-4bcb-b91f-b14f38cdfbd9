"use client";
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ems<PERSON>ist, ItemWrapper } from "./styles";
import ServiceQueueItem from "./ServiceQueueItem";
import { ServiceQueueProps } from "./types";

const ServiceQueue = ({ title, subtitle, items }: ServiceQueueProps) => (
  <QueueContainer>
    <QueueHeader>
      <h2>{title}</h2>
      {subtitle && <p>{subtitle}</p>}
    </QueueHeader>
    <ItemsList>
      {items.map((item) => (
        <ItemWrapper key={item.id}>
          <ServiceQueueItem {...item} />
        </ItemWrapper>
      ))}
    </ItemsList>
  </QueueContainer>
);

export default ServiceQueue;
