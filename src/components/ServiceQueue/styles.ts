import styled from "styled-components";

export const QueueContainer = styled.aside`
  max-width: 370px;
  background: ${({ theme }) => theme.colors.background};
  border: 0.5px solid ${({ theme }) => theme.colors.neutral400};
  border-radius: 8px;
  overflow: hidden;
  //  height: 100%;
`;

export const QueueHeader = styled.header`
  padding: 16px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.neutral300};
  h2 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: ${({ theme }) => theme.colors.neutral900};
  }
  p {
    margin: 4px 0 0;
    font-size: 0.875rem;
    color: ${({ theme }) => theme.colors.neutral800};
  }
`;

export const ItemsList = styled.ul`
  list-style: none;
  margin: 0;
  padding: 8px;
  max-height: 990px;
  overflow-y: auto;
  background-color: ${({ theme }) => theme.colors.neutral200};
`;

export const ItemWrapper = styled.li`
  margin-bottom: 8px;
  &:last-child {
    margin-bottom: 0;
  }
`;

// Service Queue Item

export const Card = styled.div<{
  $selected?: boolean;
  $maxWidth?: number | string;
}>`
  display: flex;
  flex-direction: column;
  padding: 12px;
  background: ${({ theme, $selected }) =>
    $selected ? "#EEF5FF" : theme.colors.background};
  border-radius: 8px;
  border: 1.5px solid ${({ theme }) => theme.colors.neutral400};
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  cursor: pointer;
  position: relative;
  max-width: ${({ $maxWidth }) =>
    typeof $maxWidth === "number" ? `${$maxWidth}px` : $maxWidth};
  transition:
    border-color 0.15s,
    background 0.12s;
`;

export const StatusBar = styled.div<{ status?: string }>`
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: ${({ status, theme }) => {
    switch (status) {
      case "success":
        return theme.colors.success;
      case "warning":
        return theme.colors.pending ?? "#FBBF24";
      case "error":
        return theme.colors.error;
      case "info":
        return theme.colors.primary ?? "#2563EB";
      default:
        return theme.colors.neutral400;
    }
  }};
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
`;

export const Content = styled.div`
  display: grid;
  grid-template-columns: auto 1fr auto;
  column-gap: 4px;
`;

export const DateInfo = styled.div<{
  $fontSize?: number | string;
  $selected?: boolean;
}>`
  display: flex;
  flex-direction: column;
  span,
  & {
    font-size: ${({ $fontSize }) =>
      typeof $fontSize === "number" ? `${$fontSize}px` : $fontSize};
    color: ${({ theme, $selected }) =>
      $selected ? theme.colors.link : theme.colors.neutral900};
    font-weight: ${({ $selected }) => ($selected ? 500 : 400)};
  }
`;

export const TimeInfo = styled.span<{
  $fontSize?: number | string;
  $selected?: boolean;
}>`
  font-size: ${({ $fontSize }) =>
    typeof $fontSize === "number" ? `${$fontSize}px` : $fontSize};
  color: ${({ theme, $selected }) =>
    $selected ? theme.colors.link : theme.colors.neutral900};
  font-weight: ${({ $selected }) => ($selected ? 500 : 400)};
`;

export const ActionIcon = styled.div<{ $selected?: boolean }>`
  color: ${({ theme }) => theme.colors.link};
  svg {
    width: 20px;
    height: 20px;
    pointer-events: none;
  }
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const Relative = styled.span<{
  $fontSize?: number | string;
  $fontWeight?: number | string;
}>`
  font-size: ${({ $fontSize }) =>
    typeof $fontSize === "number" ? `${$fontSize}px` : $fontSize};
  font-weight: ${({ $fontWeight }) => $fontWeight};
  color: ${({ theme }) => theme.colors.neutral800};
  margin-left: 6px;
  margin-top: 2px;
`;
