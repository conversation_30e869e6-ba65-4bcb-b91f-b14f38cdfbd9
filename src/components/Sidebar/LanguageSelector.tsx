import React, { useState, useRef, useEffect } from "react";
import { FaGlobe } from "react-icons/fa";
import { ChevronUp, ChevronDown } from "lucide-react";

import { theme } from "@/styles/theme";

import {
  LanguageContainer,
  LanguageSelector as StyledLanguageSelector,
  LanguageFlag,
  LanguageName,
  Dropdown,
  DropdownItem,
  LanguageLabelGroup,
} from "./styles";
import { LanguageSelectorProps } from "./types";

const LanguageSelector = ({
  currentLanguage,
  languages,
  onLanguageChange,
  isOpen,
}: LanguageSelectorProps & { isOpen: boolean }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  return (
    <LanguageContainer isOpen={isOpen} ref={dropdownRef}>
      <StyledLanguageSelector onClick={toggleDropdown}>
        <LanguageFlag>
          <FaGlobe color={theme.colors.neutral700} size={18} />
        </LanguageFlag>

        {isOpen && (
          <LanguageLabelGroup>
            <LanguageName isOpen={isOpen}>{currentLanguage}</LanguageName>
            {isDropdownOpen ? (
              <ChevronUp size={16} color={theme.colors.neutral700} />
            ) : (
              <ChevronDown size={16} color={theme.colors.neutral700} />
            )}
          </LanguageLabelGroup>
        )}
      </StyledLanguageSelector>

      <Dropdown isOpen={isDropdownOpen && isOpen}>
        {languages.map((language) => (
          <DropdownItem
            key={language}
            isActive={language === currentLanguage}
            onClick={() => {
              onLanguageChange(language);
              setIsDropdownOpen(false);
            }}
          >
            <span>{language}</span>
          </DropdownItem>
        ))}
      </Dropdown>
    </LanguageContainer>
  );
};

export default LanguageSelector;
