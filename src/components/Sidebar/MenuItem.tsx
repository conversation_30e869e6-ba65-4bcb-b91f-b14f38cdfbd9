import React from "react";
import { MenuItem as StyledMenuItem, MenuItemLabel, Tooltip } from "./styles";
import { MenuItemProps } from "./types";

interface Props extends MenuItemProps {
  isOpen: boolean;
}

const MenuItem = (props: Props) => {
  const {
    icon,
    label,
    isActive = false,
    isOpen,
    hasSub = false,
    onClick,
    children,
  } = props;
  return (
    <StyledMenuItem
      isActive={isActive}
      isOpen={isOpen}
      hasSub={hasSub}
      onClick={onClick}
    >
      {icon}
      <MenuItemLabel isOpen={isOpen}>{label}</MenuItemLabel>
      {!isOpen && <Tooltip>{label}</Tooltip>}
      {children}
    </StyledMenuItem>
  );
};

export default MenuItem;
