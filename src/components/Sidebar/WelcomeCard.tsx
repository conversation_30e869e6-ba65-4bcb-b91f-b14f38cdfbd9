"use client";
import React, { useState } from "react";
import { CircleCheckBig, CircleX } from "lucide-react";
import {
  WelcomeCardContainer,
  WelcomeTitle,
  WelcomeText,
  StepContainer,
  StepText,
  ProgressIndicator,
  ProgressCheckIcon,
  CloseButton,
} from "./styles";
import { WelcomeCardProps } from "./types";
import { theme } from "@/styles/theme";

const WelcomeCard = ({
  completedSteps = 1,
  totalSteps = 6,
  isOpen,
}: WelcomeCardProps & { isOpen: boolean }) => {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible || !isOpen) return null;

  return (
    <WelcomeCardContainer isOpen={isOpen}>
      <CloseButton onClick={() => setIsVisible(false)}>
        <CircleX color={theme.colors.neutral700} size={16} />
      </CloseButton>
      <WelcomeTitle>Bem vindo a DigAí!</WelcomeTitle>
      <WelcomeText>Preparado para transformar sua experiência?</WelcomeText>
      <StepContainer>
        <StepText>
          Criamos um passo-a-passo para você começar sua jornada com a gente!
        </StepText>
        <ProgressIndicator>
          <ProgressCheckIcon>
            <CircleCheckBig size={14} color={theme.colors.neutral900} />
          </ProgressCheckIcon>
          <p>
            Ver conquistas - {completedSteps}/{totalSteps}
          </p>
        </ProgressIndicator>
      </StepContainer>
    </WelcomeCardContainer>
  );
};

export default WelcomeCard;
