"use client";

import React from "react";
import { usePathname } from "next/navigation";
import { Menu, ChevronUp } from "lucide-react";

import {
  SidebarContainer,
  LogoContainer,
  Logo,
  MenuContainer,
  ToggleButton,
  Text,
  SubMenuContainer,
} from "./styles";
import MenuItem from "./MenuItem";
import LanguageSelector from "./LanguageSelector";
import { SidebarProps } from "./types";
import { useSidebar } from "@/hooks/useSidebar";
import { DigaiLogo } from "@/assets/logos/DigaiLogo";
import { theme } from "@/styles/theme";

export default function Sidebar({
  isOpen: propIsOpen,
  toggleSidebar: propToggle,
  menuItems,
  languages = ["Português", "Inglês", "Espanhol"],
}: SidebarProps) {
  const pathname = usePathname() || "";
  const { isOpen: stateOpen, toggle, navigate } = useSidebar(menuItems);

  const isOpen = propIsOpen ?? stateOpen;
  const onToggle = propToggle ?? toggle;

  function matchDynamicPath(itemPath: string, pathname: string) {
    if (itemPath.includes("[id]")) {
      const base = itemPath.replace("[id]", "");
      return pathname.startsWith(base) && pathname !== base.slice(0, -1);
    }
    return pathname === itemPath;
  }

  return (
    <SidebarContainer isOpen={isOpen}>
      <LogoContainer isOpen={isOpen}>
        <Logo>{isOpen && <DigaiLogo />}</Logo>
        <ToggleButton onClick={onToggle}>
          <Menu color={theme.colors.neutral700} size={24} />
        </ToggleButton>
      </LogoContainer>

      <MenuContainer>
        {menuItems.map((item) => {
          if (item.type === "section") {
            return (
              isOpen && (
                <Text key={item.id} isOpen={isOpen}>
                  {item.label}
                </Text>
              )
            );
          }

          const hasSub =
            Array.isArray(item.children) && item.children.length > 0;
          const isActiveChild =
            hasSub &&
            item.children!.some((child) =>
              matchDynamicPath(child.path || "", pathname)
            );

          const isActiveItem =
            pathname === item.path ||
            isActiveChild ||
            (!!item.path && pathname.startsWith(item.path + "/"));
          const isExpanded = hasSub && isActiveChild;

          if (hasSub && isExpanded && isOpen) {
            return (
              <React.Fragment key={item.id}>
                <MenuItem
                  icon={item.icon!}
                  label={item.label}
                  isActive={false}
                  isOpen={isOpen}
                  hasSub
                  onClick={() => navigate(item.id, item.path)}
                >
                  <ChevronUp color={theme.colors.primary} size={16} />
                </MenuItem>
                <SubMenuContainer>
                  {item.children!.map((child) => {
                    const isActiveChild = matchDynamicPath(
                      child.path || "",
                      pathname
                    );
                    return (
                      <MenuItem
                        key={child.id}
                        icon={child.icon!}
                        label={child.label}
                        isActive={isActiveChild}
                        isOpen={isOpen}
                        onClick={() => navigate(child.id, child.path)}
                      />
                    );
                  })}
                </SubMenuContainer>
              </React.Fragment>
            );
          }

          return (
            <MenuItem
              key={item.id}
              icon={item.icon!}
              label={item.label}
              isActive={isActiveItem}
              isOpen={isOpen}
              onClick={() => navigate(item.id, item.path)}
            />
          );
        })}
      </MenuContainer>

      {/* <WelcomeCard isOpen={isOpen} /> */}
      <LanguageSelector
        isOpen={isOpen}
        currentLanguage={languages[0]}
        languages={languages}
        onLanguageChange={() => {}}
      />
    </SidebarContainer>
  );
}
