import styled, { keyframes } from "styled-components";
import { theme } from "@/styles/theme";

const fadeIn = keyframes`
  from { opacity: 0; }
  to   { opacity: 1; }
`;

export const SidebarContainer = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== "isOpen",
})<{ isOpen: boolean }>`
  display: flex;
  flex-direction: column;
  background-color: ${({ theme }) => theme.colors.neutral200};
  height: 100vh;
  width: ${({ isOpen }) => (isOpen ? "280px" : "80px")};
  transition: width 0.3s ease;
  border-right: 0.5px solid ${({ theme }) => theme.colors.neutral400};
  position: fixed;
  animation: ${fadeIn} 0.5s ease;
`;

export const LogoContainer = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== "isOpen",
})<{ isOpen: boolean }>`
  display: flex;
  align-items: center;
  padding: ${({ isOpen }) => (isOpen ? "1.5rem 1rem" : "1.75rem 2rem")};
  border-bottom: 0.5px solid ${({ theme }) => theme.colors.neutral400};
  justify-content: ${({ isOpen }) => (isOpen ? "space-between" : "center")};
`;

export const Logo = styled.div`
  font-size: 1.5rem;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.primary};
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

export const MenuContainer = styled.nav`
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 1rem 0;
`;

export const ToggleButton = styled.button`
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }
`;

export const Text = styled.span.withConfig({
  shouldForwardProp: (prop) => prop !== "isOpen",
})<{ isOpen: boolean }>`
  color: ${({ theme }) => theme.colors.primary};
  font-size: ${({ theme }) => theme.fontSize.xs};
  font-weight: ${({ theme }) => theme.fontWeight.medium};
  margin: 0.75rem 1.5rem 0.25rem;
`;

export const SubMenuContainer = styled.div`
  padding-left: 24px;
  margin-top: 6px;
`;

export const Tooltip = styled.div`
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(8px);
  background-color: rgba(33, 37, 41, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition:
    opacity 0.2s ease,
    transform 0.2s ease;
  z-index: 10;

  &::before {
    content: "";
    position: absolute;
    top: -6px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #333;
  }
`;

export const MenuItem = styled.div.withConfig({
  shouldForwardProp: (prop) => !["isActive", "isOpen", "hasSub"].includes(prop),
})<{ isActive?: boolean; isOpen: boolean; hasSub?: boolean }>`
  display: flex;
  align-items: center;
  padding: 12px;
  margin: 0rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  color: ${({ isActive }) => (isActive ? "#ffffff" : theme.colors.neutral900)};
  background-color: ${({ isActive, hasSub }) => {
    if (isActive) return theme.colors.primary;
    if (hasSub) return "#E9E9E9";

    return "transparent";
  }};
  font-weight: ${({ isActive }) => (isActive ? "500" : "400")};

  &:hover {
    background-color: ${({ isActive }) =>
      isActive ? theme.colors.primary : theme.colors.neutral300};
    transform: translateY(-0.5px);
    transform: translateX(0.5px);
  }

  &:hover ${Tooltip} {
    opacity: 2;
    transition: none;
  }

  svg {
    margin-right: 0.75rem;
    min-width: 1.5rem;
    color: ${({ isActive, theme, isOpen }) =>
      isActive
        ? theme.colors.background
        : !isOpen
          ? theme.colors.neutral900
          : theme.colors.neutral700};
  }
  svg:last-child {
    margin-left: 110px;
  }
`;

export const ProgressCheckIcon = styled.span`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;
`;

export const MenuItemLabel = styled.span.withConfig({
  shouldForwardProp: (prop) => prop !== "isOpen",
})<{ isOpen: boolean }>`
  opacity: ${({ isOpen }) => (isOpen ? 1 : 0)};
  visibility: ${({ isOpen }) => (isOpen ? "visible" : "hidden")};
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease;
  font-size: ${({ theme }) => theme.fontSize.sm};
  font-weight: ${({ theme }) => theme.fontWeight.medium};
  white-space: nowrap;
`;

export const WelcomeCardContainer = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== "isOpen",
})<{ isOpen: boolean }>`
  margin: 1.5rem;
  padding: 1rem;
  background-color: ${({ theme }) => theme.colors.background};
  border: 0.5px solid ${({ theme }) => theme.colors.neutral400};
  border-radius: 8px;
  display: ${({ isOpen }) => (isOpen ? "block" : "none")};
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  position: relative;
  animation: ${fadeIn} 0.5s ease;
`;

export const CloseButton = styled.button`
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: none;
  border: none;
  color: ${theme.colors.neutral700};
  cursor: pointer;
  font-size: 1rem;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
`;

export const WelcomeTitle = styled.h3`
  font-size: 1rem;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.primary};
  margin-bottom: 0.5rem;
`;

export const WelcomeText = styled.p`
  font-size: 0.8rem;
  color: ${({ theme }) => theme.colors.neutral700};
  margin-bottom: 0.75rem;
  font-weight: 500;
  line-height: 1.4;
`;

export const StepContainer = styled.div`
  margin-top: 0.5rem;
`;

export const StepText = styled.p`
  font-size: 0.8rem;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.neutral900};
  display: flex;
  align-items: center;
  margin: 0.5rem 0;

  svg {
    // color: ${theme.colors.success};
    margin-right: 0.5rem;
  }
`;

export const ProgressIndicator = styled.div`
  display: flex;
  align-items: center;
  margin-top: 0.75rem;
  font-size: 0.8rem;
  color: ${theme.colors.neutral700};
  border: 0.5px solid ${({ theme }) => theme.colors.neutral400};
  border-radius: 4px;
  padding: 5px 8px;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

  p {
    font-size: 14px;
    font-weight: 500;
    color: ${({ theme }) => theme.colors.neutral900};
  }
`;

export const Dropdown = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== "isOpen",
})<{ isOpen: boolean }>`
  position: absolute;
  bottom: 100%;
  left: 1rem;
  background-color: ${theme.colors.background};
  border: 1px solid ${theme.colors.neutral300};
  border-radius: 0.25rem;
  padding: 0.5rem 0;
  width: 150px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  z-index: 100;
  visibility: ${({ isOpen }) => (isOpen ? "visible" : "hidden")};
  opacity: ${({ isOpen }) => (isOpen ? 1 : 0)};
  transform: ${({ isOpen }) => (isOpen ? "translateY(0)" : "translateY(10px)")};
  transition:
    opacity 0.2s ease,
    transform 0.2s ease,
    visibility 0.2s ease;
`;

export const DropdownItem = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== "isActive",
})<{ isActive?: boolean }>`
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: ${({ isActive }) =>
    isActive ? theme.colors.primary : theme.colors.neutral900};
  background-color: ${({ isActive }) =>
    isActive ? theme.colors.neutral300 : "transparent"};
  font-weight: ${({ isActive }) => (isActive ? "500" : "normal")};

  &:hover {
    background-color: ${theme.colors.neutral300};
  }

  span {
    margin-right: 0.5rem;
  }
`;

export const LanguageContainer = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== "isOpen",
})<{ isOpen: boolean }>`
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: ${({ isOpen }) => (isOpen ? "flex-start" : "center")};
  position: relative;
`;

export const LanguageSelector = styled.div`
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  width: 100%;

  &:hover {
    background-color: ${theme.colors.neutral300};
  }
`;

export const LanguageLabelGroup = styled.div`
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
`;

export const LanguageFlag = styled.span`
  margin-right: 0.5rem;
  opacity: 0.8;
`;

export const LanguageName = styled.span.withConfig({
  shouldForwardProp: (prop) => prop !== "isOpen",
})<{ isOpen: boolean }>`
  font-size: 0.875rem;
  color: ${theme.colors.neutral900};
  font-weight: 500;
  opacity: ${({ isOpen }) => (isOpen ? 1 : 0)};
  visibility: ${({ isOpen }) => (isOpen ? "visible" : "hidden")};
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease;
`;
