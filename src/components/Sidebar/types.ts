import { ReactNode } from "react";

export interface MenuItemData {
  id: string;
  icon?: ReactNode;
  label: string;
  path?: string;
  type?: "section";
  children?: MenuItemData[];
}

export interface SidebarProps {
  isOpen?: boolean;
  toggleSidebar?: () => void;
  menuItems: MenuItemData[];
  languages?: string[];
}

export interface MenuItemProps {
  icon: ReactNode;
  label: string;
  isActive?: boolean;
  onClick?: () => void;
  children?: ReactNode;
  hasSub?: boolean;
}

export interface WelcomeCardProps {
  completedSteps?: number;
  totalSteps?: number;
}

export interface LanguageSelectorProps {
  currentLanguage: string;
  languages: string[];
  onLanguageChange: (language: string) => void;
}
