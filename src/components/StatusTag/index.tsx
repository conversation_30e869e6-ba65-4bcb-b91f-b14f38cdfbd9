import { TagWrapper } from "./styles";
import { StatusTagProps } from "./types";
import { Tooltip } from "antd";

const StatusTag = ({
  label,
  variant = "neutral",
  icon,
  size = "medium",
  tooltip,
  className,
  backgroundColor,
}: StatusTagProps) => {
  const content = (
    <TagWrapper
      variant={variant}
      size={size}
      className={className}
      backgroundColor={backgroundColor}
    >
      {icon}
      {label}
    </TagWrapper>
  );

  return tooltip ? (
    <Tooltip title={tooltip} placement="bottom">
      {content}
    </Tooltip>
  ) : (
    content
  );
};

export default StatusTag;
