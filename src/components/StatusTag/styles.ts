import styled, { css } from "styled-components";
import { StatusTagProps } from "./types";

const variants = {
  success: css`
    background-color: ${({ theme }) => theme.colors.success};
    color: white;
    border: 0.5px solid ${({ theme }) => theme.colors.border.success};
    width: max-content;
  `,
  error: css`
    background-color: ${({ theme }) => theme.colors.error};
    color: white;
    border: 0.5px solid ${({ theme }) => theme.colors.border.error};
    width: max-content;
  `,
  warning: css`
    background-color: ${({ theme }) => theme.colors.pending};
    color: ${({ theme }) => theme.colors.neutral900};
    border: 0.5px solid ${({ theme }) => theme.colors.border.pending};
    width: max-content;
  `,
  info: css`
    background-color: ${({ theme }) => theme.colors.purple};
    color: white;
    border: 0.5px solid ${({ theme }) => theme.colors.purple};
    width: max-content;
  `,
  neutral: css`
    background-color: ${({ theme }) => theme.colors.disabled};
    color: ${({ theme }) => theme.colors.neutral900};
    border: 0.5px solid ${({ theme }) => theme.colors.neutral400};
    width: max-content;
  `,
  disabled: css`
    background-color: ${({ theme }) => theme.colors.disabled};
    color: ${({ theme }) => theme.colors.disabledText};
    width: max-content;
  `,
};

const sizes = {
  small: css`
    height: 24px;
    font-size: 12px;
    padding: 0 8px;
    min-width: 37px;
  `,
  medium: css`
    height: 32px;
    font-size: 14px;
    padding: 7px 16px;
    min-width: 118px;
  `,
};

export const TagWrapper = styled.div<{
  variant: StatusTagProps["variant"];
  size: StatusTagProps["size"];
  backgroundColor: StatusTagProps["backgroundColor"];
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  background-color: ${({ backgroundColor }) => backgroundColor};
  font-weight: 500;
  border-radius: 999px;
  position: relative;
  cursor: default;
  ${({ variant = "neutral" }) => variants[variant]};
  ${({ size = "medium" }) => sizes[size]};

  box-shadow:
    0px 1px 2px rgba(0, 0, 0, 0.03),
    0px 1px 3px rgba(0, 0, 0, 0.03),
    0px 3px 5px rgba(0, 0, 0, 0.03);
`;
