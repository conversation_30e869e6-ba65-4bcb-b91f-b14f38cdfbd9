// src/components/Table/CellWithTooltip.tsx
"use client";
import { Tooltip } from "antd";
import styled from "styled-components";
import { useRef, useState, useEffect } from "react";

const EllipsisWrapper = styled.span.withConfig({
  shouldForwardProp: (prop) => prop !== "asDiv",
})<{
  asDiv?: boolean;
}>`
  display: inline-block;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const StyledTooltip = styled(Tooltip)`
  .ant-tooltip .ant-tooltip-content .ant-tooltip-inner {
    font-size: 12px;
  }
  .ant-tooltip {
    font-size: 12px;
  }

  .ant-tooltip-inner {
    font-size: 12px;
  }
`;

interface CellTooltipProps {
  value: string;
  as?: "span" | "div";
}

export default function CellTooltip({ value, as = "span" }: CellTooltipProps) {
  const ref = useRef<HTMLElement>(null);
  const [isOverflowed, setIsOverflowed] = useState(false);

  useEffect(() => {
    const checkOverflow = () => {
      const el = ref.current;
      if (el) {
        setIsOverflowed(el.scrollWidth > el.clientWidth);
      }
    };

    checkOverflow();

    window.addEventListener("resize", checkOverflow);
    return () => {
      window.removeEventListener("resize", checkOverflow);
    };
  }, [value]);

  const Element = as === "div" ? "div" : "span";

  const content = (
    <EllipsisWrapper asDiv={as === "div"} as={Element} ref={ref}>
      {value}
    </EllipsisWrapper>
  );

  return isOverflowed ? (
    <StyledTooltip title={value}>{content}</StyledTooltip>
  ) : (
    content
  );
}
