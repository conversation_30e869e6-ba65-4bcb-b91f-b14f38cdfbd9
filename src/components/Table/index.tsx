// src/components/Table/index.tsx
"use client";
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  flexRender,
  ColumnDef,
} from "@tanstack/react-table";
import { TableProps } from "./types";
import {
  TableContainer,
  HeaderTable,
  BodyWrapper,
  BodyTable,
  PaginationContainer,
} from "./styles";
import { Pagination, Skeleton } from "antd";

export function Table<TData>({
  columns,
  data,
  totalCount,
  pageIndex = 0,
  pageSize = 10,
  isLoading,
  onPageChange,
}: TableProps<TData>) {
  const table = useReactTable({
    data,
    columns: columns as ColumnDef<TData>[],
    state: { pagination: { pageIndex, pageSize } },
    manualPagination: true,
    pageCount: Math.ceil(totalCount / pageSize),
    onPaginationChange: (updater) => {
      const next =
        typeof updater === "function"
          ? updater({ pageIndex, pageSize })
          : updater;
      onPageChange(next.pageIndex, next.pageSize);
    },
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const paginatedRows = table.getPaginationRowModel().rows;

  return (
    <TableContainer>
      <HeaderTable>
        <thead>
          {table.getHeaderGroups().map((hg) => (
            <tr key={hg.id}>
              {hg.headers.map((header) => (
                <th key={header.id}>
                  {flexRender(
                    header.column.columnDef.header,
                    header.getContext()
                  )}
                </th>
              ))}
            </tr>
          ))}
        </thead>
      </HeaderTable>

      <BodyWrapper>
        {isLoading ? (
          <Skeleton
            active
            title={false}
            paragraph={{
              rows: pageSize,
              width: Array(columns.length).fill("100%"),
            }}
          />
        ) : (
          <BodyTable>
            <tbody>
              {paginatedRows.map((row) => (
                <tr key={row.id}>
                  {row.getVisibleCells().map((cell) => (
                    <td key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </BodyTable>
        )}
      </BodyWrapper>
      {!isLoading && (
        <PaginationContainer>
          <Pagination
            current={pageIndex + 1}
            pageSize={pageSize}
            total={totalCount}
            showSizeChanger
            pageSizeOptions={["10", "20", "50", "100"]}
            onChange={(page, size) => onPageChange(page - 1, size)}
          />
        </PaginationContainer>
      )}
    </TableContainer>
  );
}
