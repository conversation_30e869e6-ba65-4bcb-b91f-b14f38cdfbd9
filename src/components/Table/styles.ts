import styled from "styled-components";

export const TableContainer = styled.div`
  border: 0.5px solid ${({ theme }) => theme.colors.neutral400};
  border-radius: 8px;
  overflow: hidden;
  box-shadow:
    0px 1px 2px rgba(0, 0, 0, 0.03),
    0px 2px 3px rgba(0, 0, 0, 0.04),
    0px 3px 6px rgba(0, 0, 0, 0.03);
`;

export const HeaderTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;

  th,
  td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  th {
    text-align: start;
    padding: 12px 18px;
    background-color: ${({ theme }) => theme.colors.background};
    font-weight: 500;
    font-size: 14px;
    color: ${({ theme }) => theme.colors.neutral900};
    border-bottom: 0.5px solid ${({ theme }) => theme.colors.neutral400};
  }

  tr th:first-child {
    border-top-left-radius: 8px;
  }
  tr th:last-child {
    border-top-right-radius: 8px;
  }
`;
export const BodyWrapper = styled.div`
  background-color: ${({ theme }) => theme.colors.neutral200};
  padding: 8px;
`;

export const BodyTable = styled.table`
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  table-layout: fixed;
  border-radius: 8px;
  border: 0.5px solid ${({ theme }) => theme.colors.neutral400};

  tr {
  }

  th,
  td {
    overflow: hidden;
    text-overflow: ellipsis;
  }

  td {
    padding: 12px 16px;
    font-size: 16px;
    color: ${({ theme }) => theme.colors.neutral900};
    background-color: ${({ theme }) => theme.colors.background};
    border-bottom: 0.5px solid ${({ theme }) => theme.colors.neutral400};
  }

  tr:last-child td {
    border-bottom: none;
  }
  tr:first-child td:first-child {
    border-top-left-radius: 8px;
  }
  tr:first-child td:last-child {
    border-top-right-radius: 8px;
  }
  tr:last-child td:first-child {
    border-bottom-left-radius: 8px;
  }
  tr:last-child td:last-child {
    border-bottom-right-radius: 8px;
  }
`;

export const PaginationContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  background-color: ${({ theme }) => theme.colors.neutral200};

  .ant-select-outlined .ant-select-selector {
    border-color: ${({ theme }) => theme.colors.primary};
    &:hover {
      border-color: ${({ theme }) => theme.colors.primary};
    }
  }

  :where(.css-dev-only-do-not-override-vrrzze).ant-select-outlined:not(
      .ant-select-disabled
    ):not(.ant-select-customize-input):not(.ant-pagination-size-changer):hover
    .ant-select-selector {
    border-color: ${({ theme }) => theme.colors.primary};
  }

  .ant-pagination-options {
    span {
      font-family: Switzer;
      font-weight: 500;
    }

    div {
      font-family: Switzer;
    }
  }

  .ant-pagination-item-active {
    background-color: ${({ theme }) => theme.colors.primary};
    border: none;
    a {
      font-family: Switzer;
      font-weight: 500;
      font-size: 16px;
      color: white;
      &:hover {
        color: white;
      }
    }
  }
  .ant-pagination-item:not(.ant-pagination-item-active) {
    background-color: transparent;
    a {
      font-family: Switzer;
      font-weight: 500;
      font-size: 16px;
    }
  }
`;
