import { TabsProps, TabItem } from "./types";
import { TabsContainer, TabButton, Counter } from "./styles";
import { useTabs } from "@/hooks/useTabs";

const Tabs = ({ tabs, value, onChange, gap }: TabsProps) => {
  const { tabsRef, isDisabled, handleSelect, handleKeyDown } = useTabs(
    tabs,
    value,
    onChange
  );

  return (
    <TabsContainer gap={gap} role="tablist">
      {tabs.map((tab: TabItem, i) => {
        const active = value === tab.value;
        const disabled = isDisabled(i);

        return (
          <TabButton
            key={tab.value}
            ref={(el) => {
              tabsRef.current[i] = el;
            }}
            isActive={active}
            isDisabled={disabled}
            role="tab"
            aria-selected={active}
            aria-disabled={disabled}
            tabIndex={disabled ? -1 : 0}
            onClick={() => handleSelect(i)}
            onKeyDown={(e) => handleKeyDown(e, i)}
          >
            {tab.label}
            {typeof tab.count === "number" && (
              <Counter isActive={active}>{tab.count}</Counter>
            )}
          </TabButton>
        );
      })}
    </TabsContainer>
  );
};

export default Tabs;
