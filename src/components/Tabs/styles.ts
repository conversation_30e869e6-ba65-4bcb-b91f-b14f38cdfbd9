import styled, { css } from "styled-components";

export const TabsContainer = styled.div<{ gap?: string }>`
  display: flex;
  align-items: center;
  gap: ${({ gap = "10px" }) => gap};
  background-color: ${({ theme }) => theme.colors.neutral200};
  padding: 4px;
  flex-wrap: wrap;
  border-radius: 4px;
`;

interface TabButtonProps {
  isActive: boolean;
  isDisabled?: boolean;
}

export const TabButton = styled.button.withConfig({
  shouldForwardProp: (prop) => prop !== "isActive" && prop !== "isDisabled",
})<TabButtonProps>`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  border: none;
  background-color: transparent;
  font-size: 16px;
  cursor: pointer;
  transition: 0.2s;

  ${({ isActive, theme }) =>
    isActive &&
    css`
      background-color: ${theme.colors.neutral100};
      font-weight: 500;
      color: ${theme.colors.primary};
    `}

  ${({ isActive, theme }) =>
    !isActive &&
    css`
      font-weight: 400;
      color: ${theme.colors.neutral700};
    `}

  ${({ isDisabled }) =>
    isDisabled &&
    css`
      cursor: not-allowed;
      opacity: 0.5;
    `}
`;

export const Counter = styled.span.withConfig({
  shouldForwardProp: (prop) => prop !== "isActive",
})<{ isActive: boolean }>`
  font-size: 14px;
  padding: 2px 6px;
  border-radius: 6px;
  font-weight: 500;

  ${({ isActive, theme }) =>
    isActive
      ? css`
          background-color: rgba(17, 28, 157, 0.1);
          color: ${theme.colors.primary};
        `
      : css`
          background-color: #e9e9e9;
          color: ${theme.colors.neutral700};
        `}
`;
