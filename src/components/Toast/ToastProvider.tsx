"use client";
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";
import { createPortal } from "react-dom";
import { Toast, ToastProps, ToastVariant } from "./index";

interface AddToastOptions {
  variant: ToastVariant;
  message: string;
  actionText?: string;
  onAction?: () => void;
  duration?: number;
  persistent?: boolean;
}

interface ToastContextValue {
  addToast: (opts: AddToastOptions) => void;
  clearToasts: () => void;
}

export const ToastContext = createContext<ToastContextValue | undefined>(
  undefined
);

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [toasts, setToasts] = useState<ToastProps[]>([]);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const removeToast = useCallback((id: string) => {
    setToasts((prev) => prev.filter((t) => t.id !== id));
  }, []);

  const clearToasts = useCallback(() => {
    setToasts([]);
  }, []);

  const addToast = useCallback(
    (opts: AddToastOptions) => {
      const id = String(Date.now());
      setToasts((prev) => [
        ...prev,
        {
          id,
          variant: opts.variant,
          message: opts.message,
          actionText: opts.actionText,
          onAction: opts.onAction,
          duration: opts.duration,
          persistent: opts.persistent,
          onClose: () => removeToast(id),
        },
      ]);
    },
    [removeToast]
  );

  return (
    <ToastContext.Provider value={{ addToast, clearToasts }}>
      {children}
      {mounted &&
        typeof window !== "undefined" &&
        createPortal(
          <div style={{ position: "fixed", top: 90, right: 16, zIndex: 9999 }}>
            {toasts.map((t) => (
              <Toast key={t.id} {...t} />
            ))}
          </div>,
          document.body
        )}
    </ToastContext.Provider>
  );
};

export const useToastContext = (): ToastContextValue => {
  const ctx = useContext(ToastContext);
  if (!ctx)
    throw new Error("useToastContext must be used within a ToastProvider");
  return ctx;
};
