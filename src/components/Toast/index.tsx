import React, { useEffect } from "react";
import {
  ToastWrapper,
  Icon<PERSON>rapper,
  Message,
  ActionButton,
  CloseButton,
} from "./styles";
import { XCircle, CheckCircle, Info, X } from "lucide-react";

export type ToastVariant = "error" | "success" | "info";

export interface ToastProps {
  id: string;
  variant: ToastVariant;
  message: string;
  actionText?: string;
  onAction?: () => void;
  duration?: number;
  persistent?: boolean;
  onClose: () => void;
}

const iconMap = {
  error: <XCircle size={16} />,
  success: <CheckCircle size={20} />,
  info: <Info size={20} />,
};

export const Toast: React.FC<ToastProps> = ({
  variant,
  message,
  actionText,
  onAction,
  duration = 5000,
  persistent = false,
  onClose,
}) => {
  useEffect(() => {
    if (!persistent) {
      const timer = setTimeout(() => onClose(), duration);
      return () => clearTimeout(timer);
    }
  }, [duration, onClose, persistent]);

  return (
    <ToastWrapper variant={variant}>
      <IconWrapper variant={variant}>{iconMap[variant]}</IconWrapper>
      <Message>{message}</Message>

      {actionText && onAction && (
        <ActionButton onClick={onAction}>{actionText}</ActionButton>
      )}

      <CloseButton onClick={onClose}>
        <X size={16} />
      </CloseButton>
    </ToastWrapper>
  );
};

export default Toast;
