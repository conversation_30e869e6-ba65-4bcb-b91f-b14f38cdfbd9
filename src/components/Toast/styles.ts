import { theme } from "@/styles/theme";
import styled, { css } from "styled-components";

const variants = {
  error: {
    iconColor: theme.colors.error,
  },
  success: {
    iconColor: theme.colors.success,
  },
  info: {
    iconColor: theme.colors.link,
  },
};

export const ToastWrapper = styled.div<{
  variant: "error" | "success" | "info";
}>`
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: ${theme.colors.background};
  max-width: 380px;
  min-height: 34px;
  border: 0.5px solid ${theme.colors.neutral400};
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  & + & {
    margin-top: 8px;
  }
`;

export const IconWrapper = styled.div<{
  variant: "error" | "success" | "info";
}>`
  margin-right: 12px;
  display: flex;
  align-items: center;
  ${({ variant }) => css`
    color: ${variants[variant].iconColor};
  `}
`;

export const Message = styled.div`
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: ${theme.colors.neutral900};
`;

export const ActionButton = styled.button`
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 500;
  margin-left: 12px;
  cursor: pointer;
  color: ${theme.colors.link};
  text-decoration: underline;
`;

export const CloseButton = styled.button`
  margin-left: 12px;
  background: none;
  border: none;
  cursor: pointer;
  color: #888;
  display: flex;
  align-items: center;
`;
