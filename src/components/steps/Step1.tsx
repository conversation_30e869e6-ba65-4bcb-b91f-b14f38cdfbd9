// src/components/steps/step1.tsx
"use client";
import TextInput from "@/components/Form/Input";
import CardWorkflow, {
  CardWorkflowProps,
} from "@/components/Card/CardWorkflow/CardWorkflow";
import {
  GeneralDataContainer,
  InputsContainer,
  TextContainer,
  WorkflowContainer,
  Wrapper,
  ContactInitiativeContainer,
  SwitchContainer,
  Box,
} from "./styles";
import CardWorkflowSkeleton from "../Card/CardWorkflow/CardWorkflowSkeleton";
import SelectInput from "../Form/SelectInput";
import { Switch } from "antd";
import { Divider } from "antd";
import { CHANNEL_OPTIONS } from "@/constants/channels";

export interface Step1Props {
  title: string;
  onChangeTitleAction: (value: string) => void;
  workflow?: string;
  onSelectWorkflowAction: (id: string) => void;
  onDownloadTemplateAction: (id: string) => void;
  workflows: CardWorkflowProps[];
  isLoading: boolean;
  communicationChannel: string;
  onChangeCommunicationChannelAction: (value: string) => void;
  executeImmediately: boolean;
  onChangeExecuteImmediatelyAction: (value: boolean) => void;
}

export default function Step1({
  title,
  onChangeTitleAction,
  workflow,
  onSelectWorkflowAction,
  onDownloadTemplateAction,
  workflows,
  isLoading,
  communicationChannel,
  onChangeCommunicationChannelAction,
  executeImmediately,
  onChangeExecuteImmediatelyAction,
}: Step1Props) {
  return (
    <Wrapper>
      <GeneralDataContainer>
        <TextContainer>
          <h2>Dados Gerais</h2>
          <p>Insira um título para criar o portfólio.</p>
        </TextContainer>
        <InputsContainer>
          <TextInput
            label="Título"
            placeholder="Título"
            value={title}
            onChange={(e) => onChangeTitleAction(e.target.value)}
          />
          <SelectInput
            labelInside="Canal de comunicação"
            placeholder="Selecione o canal"
            options={CHANNEL_OPTIONS}
            value={communicationChannel}
            onChange={onChangeCommunicationChannelAction}
          />
        </InputsContainer>
      </GeneralDataContainer>
      <ContactInitiativeContainer>
        <h2>Iniciativa de contato</h2>
        <Box>
          <SwitchContainer executeImmediately={executeImmediately}>
            <Switch
              checked={executeImmediately}
              onChange={onChangeExecuteImmediatelyAction}
            />
            <p className="switch-text">
              {executeImmediately ? "Ativo" : "Receptivo"}
            </p>
          </SwitchContainer>
          <p className="switch-description">
            Defina como a IA deverá se comportar em relação aos contatos
            importados. No <b>modo receptivo</b>, o sistema ficará no aguardo
            até que uma mensagem seja recebida de forma espontânea. Já no
            <b> modo ativo</b>, o sistema fará o envio ativo de uma mensagem,
            iniciando o contato.
          </p>
        </Box>
        <Divider />
      </ContactInitiativeContainer>
      <WorkflowContainer>
        <TextContainer>
          <h3>Workflow</h3>
          <p>
            Escolha o workflow que deseja incluir em seu portfólio e baixe o
            template base correspondente.
          </p>
          <p>
            <span>Atenção:</span> Apenas esse formato será aceito no momento de
            upload do próximo passo.
          </p>
        </TextContainer>

        {isLoading
          ? Array.from({ length: 1 }).map((_, i) => (
              <CardWorkflowSkeleton key={i} />
            ))
          : workflows.map((wf) => (
              <CardWorkflow
                key={wf.id}
                {...wf}
                selected={workflow === wf.id}
                onSelect={onSelectWorkflowAction}
                onDownloadTemplate={onDownloadTemplateAction}
              />
            ))}
      </WorkflowContainer>
    </Wrapper>
  );
}
