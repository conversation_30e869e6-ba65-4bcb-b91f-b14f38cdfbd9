"use client";
import { useState, useMemo } from "react";
import { Divider } from "antd";
import <PERSON> from "papaparse";
import Button from "@/components/Button";
import UploadInput from "../Form/UploadInput";
import Modal from "@/components/Modal";
import { Table as AntdTable } from "antd";
import {
  TextContainer,
  Container,
  FilledInputContainer,
  AntdTablePaginationCenter,
} from "./styles";
import CellTooltip from "@/components/Table/CellTooltip";
import FilledInput from "../Form/FilledInput";
import { CircleArrowDown } from "lucide-react";
import { CHANNEL_LABELS } from "@/constants/channels";
import { useCsvDelimiter } from "@/hooks/useCsvDelimiter";

type CsvRow = Record<string, string>;

export interface Step2Props {
  title: string;
  workflow?: string;
  onDownloadTemplateAction: (id: string) => void;
  file: File | null;
  onChangeFileAction: (file: File | null) => void;
  communicationChannel: string;
  executeImmediately: boolean;
  workflowTitle?: string;
}

export default function Step2({
  title,
  workflow,
  onDownloadTemplateAction,
  file,
  onChangeFileAction,
  communicationChannel,
  executeImmediately,
  workflowTitle,
}: Step2Props) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [csvData, setCsvData] = useState<CsvRow[]>([]);
  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  const delimiter = useCsvDelimiter();

  const handleReviewClick = () => {
    if (file) {
      Papa.parse<CsvRow>(file, {
        header: true,
        skipEmptyLines: true,
        delimiter,
        complete: (result) => {
          setCsvData(result.data);
          setIsModalOpen(true);
        },
      });
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const antdColumns = useMemo(() => {
    if (csvData.length === 0) return [];
    const validKeys = Object.keys(csvData[0]).filter(
      (key) => key && key.trim() !== ""
    );
    const uniqueKeys = Array.from(new Set(validKeys));
    return uniqueKeys.map((key) => ({
      title: <CellTooltip value={key} as="div" />,
      dataIndex: key,
      key,
      render: (value: string) => <CellTooltip value={value || ""} />,
    }));
  }, [csvData]);

  return (
    <>
      <AntdTablePaginationCenter />
      <div>
        <Container>
          <TextContainer>
            <h2>Upload</h2>
          </TextContainer>
          <FilledInputContainer>
            <FilledInput label="Título" value={title} />
            <FilledInput
              label="Canal de comunicação"
              value={CHANNEL_LABELS[communicationChannel]}
            />
            <FilledInput
              label="Iniciativa de contato"
              value={executeImmediately ? "Ativo" : "Receptivo"}
            />
            <FilledInput label="Workflow" value={workflowTitle ?? ""} />
          </FilledInputContainer>
        </Container>

        <div style={{ display: "flex", flexDirection: "column" }}>
          <Divider />
          <Container>
            <TextContainer>
              <h3>Arquivo</h3>
              <p>
                O upload do arquivo deve seguir o mesmo padrão do template do
                workflow.
              </p>
            </TextContainer>

            {workflow && (
              <Button
                variant="neutral"
                size="xsm"
                label="Download do template"
                icon={<CircleArrowDown size={14} />}
                onClick={() => onDownloadTemplateAction(workflow)}
              />
            )}
            <UploadInput
              file={file}
              onChange={(file) => onChangeFileAction(file)}
              accept=".csv"
              onReviewClick={handleReviewClick}
            />
          </Container>
        </div>

        <Modal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          title="Revisar upload"
          maxWidth="90%"
        >
          {csvData.length > 0 ? (
            <AntdTable
              bordered
              columns={antdColumns}
              dataSource={csvData.slice(
                pageIndex * pageSize,
                pageIndex * pageSize + pageSize
              )}
              pagination={{
                current: pageIndex + 1,
                pageSize,
                total: csvData.length,
                onChange: (page: number, pageSize: number) => {
                  setPageIndex(page - 1);
                  setPageSize(pageSize);
                },
              }}
              rowKey={(record: CsvRow) => JSON.stringify(record)}
              loading={false}
              scroll={{ x: "max-content" }}
            />
          ) : (
            <p>Carregando ou sem dados.</p>
          )}
        </Modal>
      </div>
    </>
  );
}
