import styled from "styled-components";
import { createGlobalStyle } from "styled-components";

export const Wrapper = styled.div`
  display: flex;
  gap: 35px;
  flex-direction: column;
  width: 100%;
`;

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 22px;
`;

export const GeneralDataContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
`;

export const TextContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 5px;

  h2 {
    font-size: 24px;
    font-weight: 600;
  }

  h3 {
    font-size: 18px;
    font-weight: 600;
  }
  span {
    font-weight: 700;
    color: ${({ theme }) => theme.colors.error};
  }
`;

export const FilledInputContainer = styled.div`
  display: flex;
  gap: 20px;
  width: 100%;

  @media (min-width: 2100px) {
    display: grid;
    grid-template-columns: repeat(2, minmax(220px, 1fr));
  }
`;

export const InputsContainer = styled.div`
  display: grid;
  gap: 20px;
  width: 100%;
  grid-template-columns: repeat(2, minmax(260px, 1fr));

  @media (max-width: 900px) {
    grid-template-columns: 1fr;
  }
`;

export const WorkflowContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
`;

export const ContactInitiativeContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;

  h2 {
    font-size: 18px;
    font-weight: 600;
  }

  button {
    box-shadow:
      0px 1px 2px rgba(0, 0, 0, 0.03),
      0px 1px 3px rgba(0, 0, 0, 0.03),
      0px 3px 5px rgba(0, 0, 0, 0.03);
  }

  :where(.css-dev-only-do-not-override-vrrzze).ant-switch.ant-switch-checked {
    background: #24a148;
  }

  :where(.css-vrrzze).ant-switch.ant-switch-checked .ant-switch-inner {
    background: #24a148;
  }

  :where(
      .css-dev-only-do-not-override-vrrzze
    ).ant-switch.ant-switch-checked:hover:not(.ant-switch-disabled) {
    background: #399d54;
  }

  .ant-divider-horizontal {
    margin: 0;
  }
`;

export const SwitchContainer = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== "executeImmediately",
})<{ executeImmediately: boolean }>`
  display: flex;
  flex-direction: row;
  gap: 10px;
  min-width: 112px;
  .switch-text {
    font-size: 14px;
    font-weight: 500;
    color: ${({ theme, executeImmediately }) =>
      executeImmediately ? theme.colors.success : theme.colors.neutral900};
  }
`;

export const Box = styled.div`
  display: grid;
  grid-template-columns: max-content 1fr;
  gap: 24px;
  align-items: center;

  @media (max-width: 900px) {
    grid-template-columns: 1fr;
    align-items: start;
  }

  .switch-description {
    font-size: 16px;
    font-weight: 400;
    color: ${({ theme }) => theme.colors.neutral900};

    b {
      font-weight: 700;
    }
  }
`;

export const AntdTablePaginationCenter = createGlobalStyle`
  .ant-table-wrapper .ant-table-pagination.ant-table-pagination-right {
    justify-content: center !important;
  }
  .ant-select-outlined .ant-select-selector {
    border-color: ${({ theme }) => theme.colors.primary} !important;
    &:hover {
      border-color: ${({ theme }) => theme.colors.primary} !important;
    }
  }
  .ant-select-outlined:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer):hover
    .ant-select-selector {
    border-color: ${({ theme }) => theme.colors.primary} !important;
  }
  .ant-pagination-options {
    span, div {
      font-family: Switzer !important;
      font-weight: 500 !important;
    }
  }

  .ant-pagination-item-active {
    background-color: ${({ theme }) => theme.colors.primary} !important;
    border: none !important;
    a {
      font-family: Switzer !important;
      font-weight: 500 !important;
      font-size: 16px !important;
      color: white !important;
      &:hover {
        color: white !important;
      }
    }
  }
  .ant-pagination-item:not(.ant-pagination-item-active) {
    background-color: transparent !important;
    a {
      font-family: Switzer !important;
      font-weight: 500 !important;
      font-size: 16px !important;
    }
  }

  span {
    font-family: Switzer !important;
  }
`;
