import { PortfolioItemStatus } from "@/types/portfolio";
import { ReactNode } from "react";
import { MdKeyboardDoubleArrowRight } from "react-icons/md";
import {
  FiPauseCircle,
  FiXCircle,
  FiClock,
  FiUser,
  FiUserCheck,
  FiUserX,
  FiMinusCircle,
} from "react-icons/fi";
import { CircleCheckBig } from "lucide-react";
import { LiaUserClockSolid } from "react-icons/lia";

export type StatusTagConfig = {
  label: string;
  variant: "success" | "warning" | "error" | "info" | "neutral";
  icon: ReactNode;
  tooltip: string;
};

export const portfolioItemStatusMap: Record<
  PortfolioItemStatus,
  StatusTagConfig
> = {
  [PortfolioItemStatus.PENDING]: {
    label: "Pendente",
    variant: "warning",
    icon: <FiClock size={16} />,
    tooltip: "Aguardando envio ou recebimento da primeira mensagem",
  },

  [PortfolioItemStatus.IN_PROGRESS]: {
    label: "Em andamento",
    variant: "success",
    icon: <MdKeyboardDoubleArrowRight color="white" size={16} />,
    tooltip: "Negociação em andamento",
  },

  [PortfolioItemStatus.PAUSED]: {
    label: "Pausado",
    variant: "neutral",
    icon: <FiPauseCircle size={16} />,
    tooltip: "Negociação pausada manualmente",
  },

  [PortfolioItemStatus.UNLINKED]: {
    label: "Atendimento manual",
    variant: "neutral",
    icon: <FiUser color="black" size={16} />,
    tooltip: "Negociação interrompida pelo operador",
  },

  [PortfolioItemStatus.FOLLOWED_UP]: {
    label: "Em acompanhamento",
    variant: "warning",
    icon: <FiUserCheck size={16} />,
    tooltip: "A retomada da negociação está ocorrendo de forma automatizada",
  },

  [PortfolioItemStatus.SCHEDULED_FOLLOW_UP]: {
    label: "Retorno agendado",
    variant: "warning",
    icon: <LiaUserClockSolid size={16} />,
    tooltip: "A retomada da negociação foi agendada",
  },

  [PortfolioItemStatus.OPTED_OUT]: {
    label: "Não perturbe",
    variant: "error",
    icon: <FiUserX color="white" size={16} />,
    tooltip:
      "Encerramento da interação automatizada mediante solicitação do devedor",
  },

  [PortfolioItemStatus.FINISHED]: {
    label: "Finalizada",
    variant: "info",
    icon: <CircleCheckBig color="white" size={16} />,
    tooltip:
      "O processo de retomada da negociação alcançou o limite de tentativas de contato",
  },

  [PortfolioItemStatus.CANCELLED]: {
    label: "Cancelado",
    variant: "error",
    icon: <FiXCircle color="white" size={16} />,
    tooltip: "Negociação cancelada manualmente",
  },

  [PortfolioItemStatus.FAILED]: {
    label: "Falhou",
    variant: "error",
    icon: <FiXCircle color="white" size={16} />,
    tooltip: "Negociação finalizada sem sucesso",
  },

  [PortfolioItemStatus.SUCCEED]: {
    label: "Concluído",
    variant: "info",
    icon: <CircleCheckBig color="white" size={16} />,
    tooltip: "Negociação concluída com sucesso",
  },

  [PortfolioItemStatus.IDLE]: {
    label: "Inativo",
    variant: "neutral",
    icon: <FiMinusCircle size={16} />,
    tooltip: "Sem atividade no momento",
  },

  [PortfolioItemStatus.NUMBER_NOT_EXISTS]: {
    label: "WhatsApp Não Encontrado",
    variant: "error",
    icon: <FiXCircle color="white" size={16} />,
    tooltip: "WhatsApp Não Encontrado",
  },
};
