import { PortfolioExecutionStatus } from "@/types/portfolio";
import { ReactNode } from "react";
import { MdKeyboardDoubleArrowRight } from "react-icons/md";
import { RiLoader2Line } from "react-icons/ri";
import { FiPauseCircle } from "react-icons/fi";
import { FiXCircle } from "react-icons/fi";

import { CircleCheckBig } from "lucide-react";

import { LuSave } from "react-icons/lu";

type StatusTagConfig = {
  label: string;
  variant: "success" | "warning" | "error" | "info" | "neutral";
  icon: ReactNode;
  tooltip: string;
};

export const portfolioStatusMap: Record<
  PortfolioExecutionStatus,
  StatusTagConfig
> = {
  EXECUTING: {
    label: "Em andamento",
    variant: "success",
    icon: <MdKeyboardDoubleArrowRight color="white" size={16} />,
    tooltip: "Atendimento dos itens importados em andamento",
  },
  INBOUND_EXECUTING: {
    label: "Receptivo",
    variant: "success",
    icon: <LuSave color="white" size={16} />,
    tooltip: "Operando em atendimentos de forma receptiva",
  },
  QUEUED: {
    label: "Fila de Execução",
    variant: "warning",
    icon: <RiLoader2Line size={16} />,
    tooltip: "Analisando dados do arquivo",
  },
  PAUSED: {
    label: "Pausado",
    variant: "neutral",
    icon: <FiPauseCircle size={16} />,
    tooltip: "Portfólio pausado",
  },
  WAITING: {
    label: "Salvando",
    variant: "warning",
    icon: <LuSave color="white" size={16} />,
    tooltip: "Salvando os itens para processamento",
  },
  CANCELLED: {
    label: "Cancelado",
    variant: "error",
    icon: <FiXCircle color="white" size={16} />,
    tooltip: "Portfólio cancelado",
  },
  FINISHED: {
    label: "Finalizado",
    variant: "info",
    icon: <CircleCheckBig color="white" size={16} />,
    tooltip: "Todas as ações deste portfólio foram finalizadas",
  },
};

export const portfolioStatusOptions = [
  { label: "Todos", value: "all" },
  { label: "Em andamento", value: "EXECUTING" },
  { label: "Em fila", value: "QUEUED" },
  { label: "Pausado", value: "PAUSED" },
  { label: "Salvando", value: "WAITING" },
  { label: "Cancelado", value: "CANCELLED" },
  { label: "Receptivo", value: "INBOUND_EXECUTING" },
  { label: "Finalizado", value: "FINISHED" },
];
