export interface IEnvironmentConfig {
  NODE_ENV?: string;
  TRANSCENDENCE_SERVICE_URL?: string;
  API_TARGET?: string;
  COMMIT_HASH?: string;
  FORCE_CUSTOMER_ID?: string | boolean;
  SERVICE_MOCK_RESPONSE: boolean;
  DEFAULT_LOCALE?: string;
}

function parseEnv(): IEnvironmentConfig {
  return {
    NODE_ENV: process.env.NEXT_PUBLIC_NODE_ENV,
    TRANSCENDENCE_SERVICE_URL:
      process.env.NEXT_PUBLIC_TRANSCENDENCE_SERVICE_URL,
    API_TARGET: process.env.NEXT_PUBLIC_API_TARGET,
    COMMIT_HASH: process.env.NEXT_PUBLIC_COMMIT_HASH,
    FORCE_CUSTOMER_ID: process.env.NEXT_PUBLIC_FORCE_CUSTOMER_ID,
    SERVICE_MOCK_RESPONSE:
      process.env.NEXT_PUBLIC_SERVICE_MOCK_RESPONSE === "true",
    DEFAULT_LOCALE: process.env.NEXT_PUBLIC_DEFAULT_LOCALE,
  };
}

const envConfig = parseEnv();

function getEnv(name: keyof IEnvironmentConfig): string | boolean | undefined {
  const value = envConfig[name];
  if (value === undefined) {
    throw new Error(`Environment variable ${name} is not defined.`);
  }
  return value;
}

export default getEnv;
