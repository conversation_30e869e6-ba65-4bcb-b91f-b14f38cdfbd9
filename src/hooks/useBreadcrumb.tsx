"use client";

import { usePathname } from "next/navigation";
import { routeLabels } from "@/config/routes";
import type { Crumb } from "@/components/Breadcrumb/types";

export function useBreadcrumbs(): Crumb[] {
  const pathname = usePathname() || "";
  const segments = pathname.split("/").filter(Boolean);

  return segments.map((_, idx) => {
    const href = "/" + segments.slice(0, idx + 1).join("/");
    let label = routeLabels[href];
    if (!label && href.match(/^\/portfolio\/[^/]+$/)) {
      label = routeLabels["/portfolio/[id]"];
    }
    if (!label) {
      label = segments[idx]
        .replace(/-/g, " ")
        .replace(/\b\w/g, (l) => l.toUpperCase());
    }

    return { label, href };
  });
}
