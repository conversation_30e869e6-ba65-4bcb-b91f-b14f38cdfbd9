import { useEffect, useState } from "react";
import { getCustomer } from "@/services/customerService";
import {
  fetchCustomerCSVDelimiter,
  CSV_DELIMITER,
} from "@/services/customerService";

export function useCsvDelimiter() {
  const [delimiter, setDelimiter] = useState<string>(CSV_DELIMITER);

  useEffect(() => {
    const cust = getCustomer();
    if (!cust?.id) return;
    fetchCustomerCSVDelimiter(cust.id)
      .then(setDelimiter)
      .catch(() => setDelimiter(CSV_DELIMITER));
  }, []);

  return delimiter;
}
