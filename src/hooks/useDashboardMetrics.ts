/* eslint-disable @typescript-eslint/no-explicit-any */
// src/hooks/useDashboardMetrics.ts
import { useState, useEffect } from "react";
import {
  fetchFirstMessagesSentMetrics,
  fetchTotalDeals,
  getAverageTicket,
  getTotalDealValue,
} from "@/services/metricsService";

export interface DashboardMetrics {
  cobrancasIniciadas: number;
  acordosIA: number;
  ticketMedio: string;
  valorRecuperado: string;
  loading: boolean;
  error?: Error;
}

export function useDashboardMetrics(
  rangeIso: [string, string] | undefined
): DashboardMetrics {
  const [state, setState] = useState<DashboardMetrics>({
    cobrancasIniciadas: 0,
    acordosIA: 0,
    ticketMedio: "-",
    valorRecuperado: "-",
    loading: false,
  });

  useEffect(() => {
    async function load() {
      setState((s) => ({ ...s, loading: true, error: undefined }));
      try {
        const [cobranças, acordosIA, ticket, deal] = await Promise.all([
          rangeIso
            ? fetchFirstMessagesSentMetrics(rangeIso[0], rangeIso[1])
            : Promise.resolve({ totalFirstMessagesSent: { total: 0 } }),

          rangeIso
            ? fetchTotalDeals(rangeIso[0], rangeIso[1])
            : Promise.resolve(0),
          rangeIso
            ? getAverageTicket(rangeIso[0], rangeIso[1])
            : Promise.resolve({ averageTicket: "-" }),
          rangeIso
            ? getTotalDealValue(rangeIso[0], rangeIso[1])
            : Promise.resolve({ totalDealValue: "-" }),
        ]);

        setState({
          cobrancasIniciadas: (cobranças as any).totalFirstMessagesSent.total,
          acordosIA: acordosIA as number,
          ticketMedio: (ticket as any).averageTicket,
          valorRecuperado: (deal as any).totalDealValue,
          loading: false,
        });
      } catch (error: any) {
        setState((s) => ({ ...s, loading: false, error }));
      }
    }
    load();
  }, [rangeIso]);

  return state;
}
