import { useState, useMemo, useCallback } from "react";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
dayjs.extend(utc);

export type DateRangeOptionValue =
  | "30"
  | "15"
  | "7"
  | "today"
  | "all"
  | "custom";

export function useDateRange(initialValue: DateRangeOptionValue = "today") {
  const [selection, setSelection] =
    useState<DateRangeOptionValue>(initialValue);
  const [customDates, setCustomDates] = useState<[string, string] | null>(null);

  const { startDate, endDate } = useMemo(() => {
    const nowUtc = dayjs.utc();
    let start: string | undefined;
    let end: string | undefined;

    switch (selection) {
      case "today":
        start = dayjs().startOf("day").toISOString();
        end = dayjs().endOf("day").toISOString();
        break;

      case "7":
      case "15":
      case "30": {
        const days = Number(selection) - 1;
        start = nowUtc.subtract(days, "day").startOf("day").toISOString();
        end = nowUtc.endOf("day").toISOString();
        break;
      }

      case "custom":
        if (customDates) {
          const [d0, d1] = customDates;
          start = dayjs.utc(d0, "YYYY-MM-DD").startOf("day").toISOString();
          end = dayjs.utc(d1, "YYYY-MM-DD").endOf("day").toISOString();
        }
        break;

      case "all":
        start = undefined;
        end = undefined;
        break;
    }
    return { startDate: start, endDate: end };
  }, [selection, customDates]);

  const onChange = useCallback(
    (value: DateRangeOptionValue, dates?: [string, string]) => {
      setSelection(value);
      if (value === "custom" && dates?.[0] && dates?.[1]) {
        const d0 = dayjs(dates[0]).format("YYYY-MM-DD");
        const d1 = dayjs(dates[1]).format("YYYY-MM-DD");
        setCustomDates([d0, d1]);
      } else {
        setCustomDates(null);
      }
    },
    []
  );

  return { selection, startDate, endDate, onChange };
}
