/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from "react";
import <PERSON> from "papaparse";
import { downloadImportErrorsFile } from "@/services/portfolioService";

type FailRow = { line: number; reason: string };

export function useImportFailRows() {
  const [failRows, setFailRows] = useState<FailRow[]>([]);
  const [loading, setLoading] = useState(false);

  async function fetchFailRows(portfolioId: string) {
    setLoading(true);
    try {
      const blob = await downloadImportErrorsFile(portfolioId);
      const text = await blob.text();
      const { data } = Papa.parse(text, {
        header: true,
        skipEmptyLines: true,
      });

      setFailRows(
        (data as any[]).map((row) => ({
          line: row.Linha || row.line || row["linha"] || row["Line"] || "",
          reason:
            row.Motivo || row.reason || row["motivo"] || row["Reason"] || "",
        }))
      );
    } catch (e) {
      console.error("Error fetching fail rows:", e);
      setFailRows([]);
    }
    setLoading(false);
  }

  return { failRows, loading, fetchFailRows };
}
