/* eslint-disable @typescript-eslint/no-explicit-any */

import { useEffect, useState } from "react";
import {
  getAllPortfolioItems,
  getAllPortfolios,
} from "@/services/portfolioService";
import { IPortfolioItem, PortfolioItemStatus } from "@/types/portfolio";
import { useDebounce } from "./useDebounce";

export function useNegotiations(tab: "negociacoes" | "concluidas") {
  const [items, setItems] = useState<IPortfolioItem[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [concludedCount, setConcludedCount] = useState(0);
  const [portfolios, setPortfolios] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [filteredTotal, setFilteredTotal] = useState(0);

  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [searchPhone, setSearchPhone] = useState("");
  const [statusFilter, setStatusFilter] = useState<
    "todos" | PortfolioItemStatus
  >("todos");
  const [onlyInteraction, setOnlyInteraction] = useState(false);
  const [waitingBusinessUserResponse, setWaitingBusinessUserResponse] =
    useState(false);
  const debouncedPhone = useDebounce(searchPhone, 2000);

  useEffect(() => {
    getAllPortfolios().then(setPortfolios);
  }, []);

  useEffect(() => {
    async function fetchCounts() {
      if (portfolios.length === 0) return;
      const base: any = {};
      if (debouncedPhone.trim()) base.phoneNumber = debouncedPhone;
      if (onlyInteraction) base.lastInteraction = { not: null };
      if (waitingBusinessUserResponse) base.waitingBusinessUserResponse = true;

      const respTotal = await getAllPortfolioItems(
        base,
        { page: 1, limit: 1 },
        portfolios
      );
      setTotalCount(respTotal.total);

      const respConcl = await getAllPortfolioItems(
        { ...base, currentStatus: PortfolioItemStatus.SUCCEED },
        { page: 1, limit: 1 },
        portfolios
      );
      setConcludedCount(respConcl.total);
    }
    fetchCounts();
  }, [
    debouncedPhone,
    onlyInteraction,
    waitingBusinessUserResponse,
    portfolios,
  ]);

  useEffect(() => {
    async function fetchList() {
      if (portfolios.length === 0) return;
      setLoading(true);

      const base: any = {};
      if (debouncedPhone.trim()) base.phoneNumber = debouncedPhone;
      if (onlyInteraction) base.lastInteraction = { not: null };
      if (waitingBusinessUserResponse) base.waitingBusinessUserResponse = true;

      if (tab === "concluidas") {
        base.currentStatus = PortfolioItemStatus.SUCCEED;
      } else if (statusFilter !== "todos") {
        base.currentStatus = statusFilter;
      }

      const sortField: "createdAt" | "lastInteraction" =
        onlyInteraction || waitingBusinessUserResponse
          ? "lastInteraction"
          : "createdAt";

      const respList = await getAllPortfolioItems(
        base,
        { page: pageIndex + 1, limit: pageSize },
        portfolios,
        sortField
      );

      setItems(respList.items);
      setFilteredTotal(respList.total);
      setLoading(false);
    }
    fetchList();
  }, [
    tab,
    pageIndex,
    pageSize,
    debouncedPhone,
    statusFilter,
    onlyInteraction,
    waitingBusinessUserResponse,
    portfolios,
  ]);

  return {
    items,
    totalCount,
    concludedCount,
    filteredTotal,
    pageIndex,
    pageSize,
    setPageIndex,
    setPageSize,
    searchPhone,
    setSearchPhone,
    statusFilter,
    setStatusFilter,
    onlyInteraction,
    setOnlyInteraction,
    waitingBusinessUserResponse,
    setWaitingBusinessUserResponse,
    loading,
  };
}
