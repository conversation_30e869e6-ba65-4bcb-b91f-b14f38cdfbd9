/* eslint-disable @typescript-eslint/no-explicit-any */

import { useState, useEffect } from "react";
import {
  searchPortfolios,
  getAllPortfolioItems,
} from "@/services/portfolioService";
import { Portfolio } from "@/types/portfolio";

export interface PortfolioWithQueueFlag extends Portfolio {
  hasWaitingBusinessUserResponse: boolean;
}

export interface PaginatedPortfolios {
  items: PortfolioWithQueueFlag[];
  total: number;
  page: number;
  totalPages: number;
}

export function usePaginatedPortfolios(
  name: string,
  page: number,
  limit: number = 10
) {
  const [data, setData] = useState<PaginatedPortfolios>({
    items: [],
    total: 0,
    page: 1,
    totalPages: 0,
  });
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    let active = true;

    async function load() {
      setLoading(true);
      try {
        const resp = await searchPortfolios(name, page, limit);
        const pageItems: Portfolio[] = Array.isArray(resp)
          ? resp
          : ((resp as any).items ?? (resp as any).data ?? []);

        const queueResp: any = await getAllPortfolioItems(
          { waitingBusinessUserResponse: true },
          { page: 1, limit: 9999 },
          []
        );
        const queuedIds = new Set(
          queueResp.items.map((i: any) => i.portfolioId)
        );

        const withFlag = pageItems.map((p) => ({
          ...p,
          hasWaitingBusinessUserResponse: queuedIds.has(p.id),
        }));

        if (active) {
          setData({
            items: withFlag,
            total: resp.total,
            page: Array.isArray(resp) ? page : resp.page,
            totalPages: Array.isArray(resp) ? 1 : resp.totalPages,
          });
        }
      } finally {
        if (active) setLoading(false);
      }
    }

    load();
    return () => {
      active = false;
    };
  }, [name, page, limit]);

  return {
    portfolios: data.items,
    loading,
    total: data.total,
    page: data.page,
    totalPages: data.totalPages,
  };
}
