/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useMemo, useState } from "react";
import { PortfolioImportStatus } from "@/types/portfolio";

export function useImportsFilters(cardList: any[]) {
  function getTabData(cards: any[]) {
    let andamento = 0;
    let concluidas = 0;

    cards.forEach((card: any) => {
      const status =
        card.originalImportStatus ||
        card.importStatus ||
        card.header?.importStatus;
      if (
        status === PortfolioImportStatus.UPLOADED ||
        status === PortfolioImportStatus.PROCESSING
      )
        andamento += 1;
      else if (status === PortfolioImportStatus.SUCCESS) concluidas += 1;
    });

    return {
      emAndamento: andamento,
      concluidas,
      todas: cards.length,
    };
  }

  const tabCounts = useMemo(() => getTabData(cardList), [cardList]);

  function getInitialTab(tabCounts: {
    emAndamento: number;
    concluidas: number;
    todas: number;
  }) {
    if (tabCounts.emAndamento > 0) return "em-andamento";
    if (tabCounts.concluidas > 0) return "concluidas";
    return "todas";
  }

  const [currentTab, setCurrentTab] = useState(() => getInitialTab(tabCounts));

  const [selectedEmbedded, setSelectedEmbedded] = useState("all");
  const [selectedExecutionStatus, setSelectedExecutionStatus] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");

  const filterCards = useCallback(
    (tab: string, list: any[]) => {
      let filtered = list;

      if (tab === "em-andamento")
        filtered = list.filter(
          (c: any) =>
            c.originalImportStatus === PortfolioImportStatus.UPLOADED ||
            c.originalImportStatus === PortfolioImportStatus.PROCESSING
        );
      else if (tab === "concluidas")
        filtered = list.filter(
          (c) => c.originalImportStatus === PortfolioImportStatus.SUCCESS
        );

      if (tab === "todas" && selectedExecutionStatus !== "all") {
        filtered = filtered.filter(
          (c) => c.originalExecutionStatus === selectedExecutionStatus
        );
      }
      if (searchTerm.trim()) {
        filtered = filtered.filter(
          (c) =>
            typeof c.header?.title === "string" &&
            c.header.title.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }

      return filtered;
    },
    [selectedExecutionStatus, searchTerm]
  );

  return {
    tabCounts,
    currentTab,
    setCurrentTab,
    selectedEmbedded,
    setSelectedEmbedded,
    selectedExecutionStatus,
    setSelectedExecutionStatus,
    searchTerm,
    setSearchTerm,
    filterCards,
    getInitialTab,
  };
}
