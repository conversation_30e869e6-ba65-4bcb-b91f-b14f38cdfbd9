import { useEffect, useState } from "react";
import { getPortfolioItem } from "@/services/portfolioItemService";
import { IPortfolioItem } from "@/types/portfolio";

export function usePortfolioItem(negotiationId?: string) {
  const [data, setData] = useState<IPortfolioItem | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!negotiationId) return;
    setLoading(true);
    getPortfolioItem(negotiationId)
      .then(setData)
      .finally(() => setLoading(false));
  }, [negotiationId]);

  return { data, loading };
}
