/* eslint-disable @typescript-eslint/no-explicit-any */
import { PortfolioExecutionStatus } from "@/types/portfolio";
import { useEffect, useMemo, useState } from "react";
import { PortfolioWithQueueFlag } from "./usePortfoliosWithQueueFlag";

export function usePortfolioTabs(cardList: any[]) {
  function getTabData(cards: any[]) {
    let ativas = 0;
    let concluidas = 0;
    let arquivadas = 0;
    cards.forEach((card: any) => {
      const status =
        card.originalExecutionStatus ||
        card.executionStatus ||
        card.header?.executionStatus;
      if (
        status === PortfolioExecutionStatus.EXECUTING ||
        status === PortfolioExecutionStatus.QUEUED ||
        status === PortfolioExecutionStatus.WAITING ||
        status === PortfolioExecutionStatus.INBOUND
      )
        ativas += 1;
      else if (status === PortfolioExecutionStatus.FINISHED) concluidas += 1;
      else if (
        status === PortfolioExecutionStatus.CANCELLED ||
        status === PortfolioExecutionStatus.PAUSED
      )
        arquivadas += 1;
    });

    return {
      ativas,
      concluidas,
      arquivadas,
    };
  }

  const tabCounts = useMemo(() => getTabData(cardList), [cardList]);

  function getInitialTab(tabCounts: {
    ativas: number;
    concluidas: number;
    arquivadas: number;
  }) {
    if (tabCounts.ativas > 0) return "ativas";
    if (tabCounts.concluidas > 0) return "concluidas";
    return "arquivadas";
  }

  const filterByTab = (list: PortfolioWithQueueFlag[]) => {
    switch (currentTab) {
      case "ativas":
        return list.filter((p) =>
          ["EXECUTING", "QUEUED", "WAITING", "INBOUND_EXECUTING"].includes(
            p.executionStatus
          )
        );
      case "concluidas":
        return list.filter((p) => p.executionStatus === "FINISHED");
      case "arquivadas":
        return list.filter((p) =>
          ["CANCELLED", "PAUSED"].includes(p.executionStatus)
        );
      default:
        return list;
    }
  };

  const [currentTab, setCurrentTab] = useState(() => getInitialTab(tabCounts));

  useEffect(() => {
    const initialTab = getInitialTab(tabCounts);
    setCurrentTab((prev) => {
      if (prev !== initialTab) return initialTab;
      return prev;
    });
  }, [tabCounts]);

  return {
    currentTab,
    setCurrentTab,
    tabCounts,
    getInitialTab,
    filterByTab,
  };
}
