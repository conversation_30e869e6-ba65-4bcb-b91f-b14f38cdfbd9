/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState, useRef } from "react";
import { getAllPortfolios, getPortfolio } from "@/services/portfolioService";
import { portfolioToCardData } from "@/utils/portfolioAdapter";
import { PortfolioImportStatus, Portfolio } from "@/types/portfolio";

type Handlers = { onFailClick: (id: string) => void };

interface PollInfo {
  last: number;
  count: number;
}

export function usePortfoliosImports({
  onFailClick,
  pollInterval = 4000,
}: Handlers & { pollInterval?: number }) {
  const [data, setData] = useState<any[]>([]);
  const [loadingInitial, setLoadingInitial] = useState(true);
  const pollers = useRef<Record<string, NodeJS.Timeout>>({});
  const pollInfo = useRef<Record<string, PollInfo>>({});

  const onFailClickRef = useRef(onFailClick);
  useEffect(() => {
    onFailClickRef.current = onFailClick;
  }, [onFailClick]);

  useEffect(() => {
    let canceled = false;

    const fetchAll = async () => {
      try {
        const portfolios = await getAllPortfolios();
        if (canceled) return;
        setData(
          portfolios.map((p) =>
            portfolioToCardData(p, {
              onFailClick: () => onFailClickRef.current(p.id),
            })
          )
        );
      } finally {
        if (!canceled) setLoadingInitial(false);
      }
    };

    fetchAll();
    return () => {
      canceled = true;

      Object.values(pollers.current).forEach(clearInterval);
    };
  }, []);

  useEffect(() => {
    data.forEach((card) => {
      const st = card.originalImportStatus as PortfolioImportStatus;
      const active =
        st === PortfolioImportStatus.PROCESSING ||
        st === PortfolioImportStatus.UPLOADED;

      if (active && !pollers.current[card.id]) {
        const pct0 = computePercent(card);
        pollInfo.current[card.id] = { last: pct0, count: 0 };

        pollers.current[card.id] = setInterval(async () => {
          try {
            const updated: Portfolio = await getPortfolio(card.id);
            const pctNew = computePercent(updated);
            const info = pollInfo.current[card.id]!;

            if (pctNew === info.last) {
              info.count++;
            } else {
              info.count = 0;
              info.last = pctNew;
            }

            setData((prev) =>
              prev.map((c) =>
                c.id === card.id
                  ? portfolioToCardData(updated, {
                      onFailClick: () => onFailClickRef.current(updated.id),
                    })
                  : c
              )
            );

            const st2 = updated.importStatus;
            const stuckTooLong = info.count > 15;
            if (
              (st2 !== PortfolioImportStatus.PROCESSING &&
                st2 !== PortfolioImportStatus.UPLOADED) ||
              stuckTooLong
            ) {
              clearInterval(pollers.current[card.id]);
              delete pollers.current[card.id];
              delete pollInfo.current[card.id];
            }
          } catch {}
        }, pollInterval);
      }
    });
  }, [data, pollInterval]);

  return { data, loadingInitial };
}

function computePercent(p: {
  processedQuantity: number;
  totalQuantity: number;
}) {
  if (p.totalQuantity <= 0) return 0;
  return Math.round((p.processedQuantity / p.totalQuantity) * 100);
}
