/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

type ParamDefs<T> = {
  [K in keyof T]: {
    default: T[K];
    serialize?: (v: T[K]) => string;
    deserialize?: (s: string) => T[K];
  };
};

export function useQuerySync<T extends Record<string, any>>(
  defs: ParamDefs<T>
) {
  const router = useRouter();
  const params = useSearchParams();

  const [state, setState] = useState<T>(() => {
    const out = {} as T;
    for (const key of Object.keys(defs) as Array<keyof T>) {
      const raw = params.get(key as string);
      if (raw != null && defs[key].deserialize) {
        out[key] = defs[key].deserialize!(raw);
      } else if (raw != null) {
        out[key] = raw as any;
      } else {
        out[key] = defs[key].default;
      }
    }
    return out;
  });

  useEffect(() => {
    const qp = new URLSearchParams();
    for (const key of Object.keys(defs) as Array<keyof T>) {
      const val = state[key];

      if (val === defs[key].default) continue;

      const s = defs[key].serialize ? defs[key].serialize(val) : String(val);
      qp.set(key as string, s);
    }
    router.replace(window.location.pathname + "?" + qp.toString(), {
      scroll: false,
    });
  }, [state, router, defs]);

  function setQueryState<K extends keyof T>(key: K, value: T[K]) {
    setState((s) => ({ ...s, [key]: value }));
  }

  return [state, setQueryState] as const;
}
