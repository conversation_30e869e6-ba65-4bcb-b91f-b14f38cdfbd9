import { useEffect, useState } from "react";
import { getAllPortfolioItems } from "@/services/portfolioService";
import { getPortfolioItem } from "@/services/portfolioItemService";
import { IPortfolioItem } from "@/types/portfolio";
import { useParams } from "next/navigation";

export function useQueueName(page = 1, limit = 9999) {
  const [items, setItems] = useState<IPortfolioItem[]>([]);
  const [isLoading, setLoading] = useState(true);

  const { id } = useParams();
  const portfolioId = id as string;

  useEffect(() => {
    if (!portfolioId) return;
    let cancel = false;
    async function load() {
      setLoading(true);
      try {
        const resp = await getAllPortfolioItems(
          { waitingBusinessUserResponse: true, portfolioId },
          { page, limit },
          []
        );
        const detailed = await Promise.all(
          resp.items.map(async (item) => {
            const full = await getPortfolioItem(item.id);
            return {
              ...item,
              customData: full.customData,
            };
          })
        );
        if (!cancel) setItems(detailed);
      } catch (err) {
        console.error("Erro ao carregar fila com customer:", err);
      } finally {
        if (!cancel) setLoading(false);
      }
    }
    load();
    return () => {
      cancel = true;
    };
  }, [page, limit, portfolioId]);

  return { items, isLoading };
}
