import { useState, useEffect, useCallback } from "react";
import { useRouter, usePathname } from "next/navigation";
import { MenuItemData } from "@/components/Sidebar/types";

export function useSidebar(menuItems: MenuItemData[]) {
  const router = useRouter();
  const pathname = usePathname();

  const [isOpen, setIsOpen] = useState(true);
  const [activeId, setActiveId] = useState<string>("");

  useEffect(() => {
    let found = "";
    for (const item of menuItems) {
      if (item.path === pathname) {
        found = item.id;
        break;
      }
      if (item.children) {
        const child = item.children.find((c) => c.path === pathname);
        if (child) {
          found = child.id;
          break;
        }
      }
    }
    setActiveId(found);
  }, [pathname, menuItems]);

  const toggle = useCallback(() => {
    setIsOpen((prev) => !prev);
  }, []);

  const navigate = useCallback(
    (id: string, path?: string) => {
      setActiveId(id);
      if (path) router.push(path);
    },
    [router]
  );

  return { isOpen, activeId, toggle, navigate };
}
