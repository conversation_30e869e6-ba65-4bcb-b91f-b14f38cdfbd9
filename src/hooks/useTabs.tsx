import { useRef, useEffect, useCallback } from "react";
import { TabItem } from "@/components/Tabs/types";

export function useTabs(
  tabs: TabItem[],
  value: string,
  onChange: (value: string) => void
) {
  const tabsRef = useRef<(HTMLButtonElement | null)[]>([]);

  const enabled = tabs
    .map((tab, i) => ((tab.count ?? 1) > 0 ? i : null))
    .filter((i): i is number => i !== null);

  const isDisabled = useCallback(
    (index: number) => (tabs[index].count ?? 1) === 0,
    [tabs]
  );

  const handleSelect = useCallback(
    (i: number) => {
      if (!isDisabled(i)) onChange(tabs[i].value);
    },
    [isDisabled, onChange, tabs]
  );

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent, index: number) => {
      if (isDisabled(index)) return;

      const pos = enabled.indexOf(index);
      if (pos === -1) return;

      let nextPos = pos;
      if (e.key === "ArrowRight") nextPos = (pos + 1) % enabled.length;
      if (e.key === "ArrowLeft")
        nextPos = (pos - 1 + enabled.length) % enabled.length;
      if (e.key === "Enter") return handleSelect(index);

      tabsRef.current[enabled[nextPos]]?.focus();
    },
    [enabled, handleSelect, isDisabled]
  );

  useEffect(() => {
    return () => {};
  }, []);

  return { tabsRef, isDisabled, handleSelect, handleKeyDown };
}
