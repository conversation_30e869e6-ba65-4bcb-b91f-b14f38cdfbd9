import { getAccessToken, getRefreshToken } from "@/interceptors/getJWTToken";

function Fetch() {
  return {
    get: request("GET"),
    post: request("POST"),
    put: request("PUT"),
    delete: request("DELETE"),
    patch: request("PATCH"),
    getRaw: requestRaw("GET"),
  };

  function request(method: string) {
    return async (url: string, body?: object) => {
      const accessToken = await getAccessToken();
      const startTime = performance.now();
      const requestOptions: RequestInit = {
        method,
        credentials: "include",
      };

      requestOptions.headers = {
        ...requestOptions.headers,
        Authorization: `Bearer ${accessToken}`,
        "x-digai-client-domain": window.location.hostname,
      };

      if (body && method !== "GET" && method !== "HEAD") {
        if (!(body instanceof FormData)) {
          requestOptions.headers = {
            ...requestOptions.headers,
            "Content-Type": "application/json",
          };
          requestOptions.body = JSON.stringify(body);
        } else {
          requestOptions.body = body;
        }
      }
      if (body) {
        // In case it is not a Audio
        if (!(body instanceof FormData)) {
          requestOptions.headers = {
            ...requestOptions.headers,
            "Content-Type": "application/json",
          };
          requestOptions.body = JSON.stringify(body);
        } else {
          requestOptions.body = body;
        }
      }

      try {
        console.info(`Request: [${method}] request to ${url}`);

        const response = await fetchWithRetry(url, requestOptions);
        const endTime = performance.now();

        console.info(
          `Response: [${method}] request to ${url} completed in ${endTime - startTime}ms`
        );

        return await handleResponse(response, url, body, method);
      } catch (error) {
        console.error(`Error in ${method} request to ${url}:`, error);
        throw error;
      }
    };
  }

  function requestRaw(method: string) {
    return async (url: string) => {
      const accessToken = await getAccessToken();
      const requestOptions: RequestInit = {
        method,
        credentials: "include",
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "x-digai-client-domain": window.location.hostname,
          "Content-Type": "application/json",
        },
      };
      const response = await fetchWithRetry(url, requestOptions);
      return await response.json();
    };
  }

  async function fetchWithRetry(
    url: string,
    options: RequestInit,
    retries = 2
  ): Promise<Response> {
    try {
      return await fetch(url, options);
    } catch (error) {
      console.error(`Attempt failed for ${url}. Error: ${error}`);
      if (retries > 0) {
        console.warn(
          `Retrying request: to ${url}. Remaining attempts: ${retries - 1}`
        );

        await new Promise((resolve) => setTimeout(resolve, 1000));
        return fetchWithRetry(url, options, retries - 1);
      }
      throw new Error(
        `Internet connection error. Please verify your connection and try again.`
      );
    }
  }

  async function handleResponse(
    response: Response,
    url: string,
    body: object | undefined,
    method: string
  ): Promise<unknown> {
    const contentType = response.headers.get("content-type");

    if (!response.ok) {
      const payload = await response.json();
      const message: string | null =
        (typeof payload.message === "string" && payload.message) ||
        (Array.isArray(payload.message) && payload.message.join(". \n")) ||
        null;

      if (response.status === 401) {
        return handleUnauthorized(url, body, method);
      }

      console.error(`HTTP error! status: ${response.status}: ${payload}`);
      throw new Error(message ?? response.statusText);
    }

    try {
      if (
        contentType?.includes("text/csv") ||
        contentType?.includes(
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        ) ||
        contentType?.includes("application/pdf")
      ) {
        return await response.blob();
      } else if (contentType?.includes("audio")) {
        return response.body;
      } else {
        const payload = await response.json();
        return payload.data ?? payload;
      }
    } catch (error) {
      console.error(`Error processing response:`, error);
      throw new Error(
        error instanceof Error
          ? `Error processing response: ${error.message}`
          : "An unknown error occurred while processing the response"
      );
    }
  }

  async function handleUnauthorized(
    url: string,
    body: object | undefined,
    method: string
  ) {
    try {
      const refreshResponse = await fetch(
        `${process.env.NEXT_PUBLIC_TRANSCENDENCE_SERVICE_URL}/v1/auth/session/refresh-access-token`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${await getRefreshToken()}`,
          },
          credentials: "include",
        }
      );

      if (refreshResponse.ok) {
        return await request(method)(url, body);
      } else {
        window.location.replace("/");
        throw new Error("Session expired. Please log in again.");
      }
    } catch (error) {
      console.error(`Error refreshing token:`, error);
      throw new Error("Authentication error. Please log in again.");
    }
  }
}

export { Fetch };
