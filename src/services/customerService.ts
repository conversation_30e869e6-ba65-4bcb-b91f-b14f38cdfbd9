"use client";
import getEnv from "@/hooks/getEnv";
import { Fetch } from "@/interceptors/Fetch";
import { Customer } from "@/types/customer";
//import getEnv from "@/hooks/getEnv";

export function getCustomer(): Customer {
  //   if (getEnv("SERVICE_MOCK_RESPONSE")) {
  //     return mockedCustomer;
  //   }

  const storedData = localStorage.getItem("customer");
  const customerRaw = storedData ? JSON.parse(storedData) : {};

  const customer: Customer = {
    id: customerRaw.customerId,
    userId: customerRaw.userId,
    cnpj: customerRaw.cnpj,
    email: customerRaw.email,
    name: customerRaw.firstname + " " + customerRaw.lastname,
    segment: customerRaw.segment,
    phone: customerRaw.phone,
    whatsappPhone: customerRaw.whatsappPhone,
    accountId: customerRaw.accountId,
  };
  return customer;
}

export const CSV_DELIMITER = ",";

interface CustomerPreferencesResponse {
  portfolio?: {
    customImportConfig?: {
      delimiter?: string;
    };
  };
}

export async function fetchCustomerCSVDelimiter(
  customerId: string
): Promise<string> {
  try {
    // if (getEnv("SERVICE_MOCK_RESPONSE")) {
    //   // Return default delimiter in mock mode
    //   return CSV_DELIMITER;
    // }

    const fetch = Fetch();
    const transcendenceServiceEndpoint = getEnv("TRANSCENDENCE_SERVICE_URL");

    if (!transcendenceServiceEndpoint) {
      throw new Error("Transcendence service endpoint is not defined");
    }

    const response = (await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/customer-preferences/${customerId}`
    )) as CustomerPreferencesResponse;

    const delimiter = response?.portfolio?.customImportConfig?.delimiter;

    return delimiter || CSV_DELIMITER;
  } catch (error) {
    console.warn("Failed to fetch customer CSV delimiter preferences:", error);
    return CSV_DELIMITER;
  }
}
