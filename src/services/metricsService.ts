import getEnv from "@/hooks/getEnv";
import { Fetch } from "@/interceptors/Fetch";
import { buildLocalDayRange } from "@/utils/formatDate";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
dayjs.extend(utc);
dayjs.extend(timezone);
const TZ = "America/Sao_Paulo";

const transcendenceServiceEndpoint = getEnv("TRANSCENDENCE_SERVICE_URL");

function formatDateWithTimezone(dateStr: string): string {
  return dayjs.utc(dateStr).tz(TZ).format();
}

/* eslint-disable @typescript-eslint/no-explicit-any */
function normalizeFirstMessagesSentResponse(raw: any) {
  const dailyCandidates: Array<Record<string, number> | undefined> = [
    raw?.totalFirstMessagesSent?.dailyTotals,
    raw?.data?.dailyTotals,
    raw?.dailyTotals,

    typeof raw?.data === "object" && !Array.isArray(raw.data)
      ? raw.data
      : undefined,
  ];

  let dailyTotals: Record<string, number> = {};
  for (const cand of dailyCandidates) {
    if (cand && typeof cand === "object") {
      dailyTotals = cand as Record<string, number>;
      break;
    }
  }

  let total: number | undefined =
    raw?.totalFirstMessagesSent?.total ??
    raw?.data?.total ??
    raw?.total ??
    (typeof raw?.data === "number" ? raw.data : undefined);

  if (typeof total !== "number") {
    total = Object.values(dailyTotals || {}).reduce(
      (acc, v) => acc + (Number(v) || 0),
      0
    );
  }

  return {
    totalFirstMessagesSent: {
      total: total || 0,
      dailyTotals: dailyTotals || {},
    },
  };
}

export async function fetchFirstMessagesSentMetrics(
  dateStart: string,
  dateEnd: string
) {
  const fetch = Fetch();
  const { startDate: s, endDate: e } = buildLocalDayRange(dateStart, dateEnd);

  try {
    const newRaw = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/first-messages-sent?dateStart=${s}&dateEnd=${e}`
    );

    const normalizedNew = normalizeFirstMessagesSentResponse(newRaw);
    const totalNew = normalizedNew?.totalFirstMessagesSent?.total ?? 0;

    if (totalNew > 0) {
      return normalizedNew;
    }
  } catch (err) {
    console.warn("new endpoint failed, using fallback message-hub:", err);
  }

  try {
    const oldRaw = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/message-hub/metrics/first-messages-sent?dateStart=${s}&dateEnd=${e}`
    );
    return normalizeFirstMessagesSentResponse(oldRaw);
  } catch (err) {
    console.error("Fallback (message-hub) também falhou:", err);

    return {
      totalFirstMessagesSent: { total: 0, dailyTotals: {} },
    };
  }
}

// Mensagens recebidas
//exemplo de data aceita aqui: 2025-07-31
export async function fetchMessagesReceivedMetrics(
  dateStart: string,
  dateEnd: string
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/message-hub/metrics/messages-received?dateStart=${dateStart}&dateEnd=${dateEnd}`
    );

    return data;
  } catch (error) {
    console.error("Error fetching messages received metrics:", error);
    throw error;
  }
}

// Respostas enviadas
//exemplo de data aceita aqui: 2025-07-31
export async function fetchAnswerMessagesSentMetrics(
  dateStart: string,
  dateEnd: string
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/message-hub/metrics/answer-messages-sent?dateStart=${dateStart}&dateEnd=${dateEnd}`
    );
    return data;
  } catch (error) {
    console.error("Error fetching answer messages sent metrics:", error);
    throw error;
  }
}

//Acordos Concluídos com IA - Total Deals
//exemplo de data aceita aqui: 2025-07-31
export async function fetchTotalDeals(startDate: string, endDate: string) {
  try {
    const { startDate: localStart, endDate: localEnd } = buildLocalDayRange(startDate, endDate);
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/total-deals?startDate=${localStart}&endDate=${localEnd}`
    );

    return data;
  } catch (error) {
    console.error("Error fetching portfolio items with IA 100 metrics:", error);
    throw error;
  }
}
//Negociações com interação
//exemplo de data aceita aqui: 2025-07-31
export async function fetchPortfolioItemsWithInteractionMetrics(
  startDate: string,
  endDate: string,
  groupByDate: string = "DAY"
) {
  try {
    const { startDate: localStart, endDate: localEnd } = buildLocalDayRange(startDate, endDate);
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/portfolio/items/with-interaction?startDate=${localStart}&endDate=${localEnd}&groupByDate=${groupByDate}`
    );

    return data;
  } catch (error) {
    console.error(
      "Error fetching portfolio items with interaction metrics:",
      error
    );
    throw error;
  }
}

//Acordos fechados
//exemplo de data aceita aqui: 2025-07-31
export async function fetchPortfolioItemsGroupedByDateMetrics(
  startDate: string,
  endDate: string,
  groupByDate: string = "DAY",
  currentStatus: string = "SUCCEED"
) {
  try {
    const { startDate: localStart, endDate: localEnd } = buildLocalDayRange(startDate, endDate);
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/portfolio/items/grouped-by-date?startDate=${localStart}&endDate=${localEnd}&groupByDate=${groupByDate}&currentStatus=${currentStatus}`
    );

    return data;
  } catch (error) {
    console.error(
      "Error fetching portfolio items grouped by date metrics:",
      error
    );
    throw error;
  }
}

//Ticket Médio
//exemplo de data aceita aqui: 2025-07-31T02:59:59-03:00
export async function getAverageTicket(startDate?: string, endDate?: string) {
  try {
    const fetch = Fetch();

    const params = new URLSearchParams();

    // Se ambas as datas estão presentes, usar o range local
    if (startDate && endDate) {
      const { startDate: localStart, endDate: localEnd } = buildLocalDayRange(startDate, endDate);
      params.append("startDate", localStart);
      params.append("endDate", localEnd);
    } else {
      // Fallback para comportamento anterior se apenas uma data for fornecida
      if (startDate)
        params.append("startDate", formatDateWithTimezone(startDate));
      if (endDate) params.append("endDate", formatDateWithTimezone(endDate));
    }

    const qs = params.toString() ? `?${params.toString()}` : "";

    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/customer/average-ticket${qs}`
    );

    return data;
  } catch (error) {
    console.error("Error fetching average ticket ", error);
    throw error;
  }
}

//Valor recuperado
//exemplo de data aceita aqui: 2025-07-31T02:59:59-03:00
export async function getTotalDealValue(startDate?: string, endDate?: string) {
  try {
    const fetch = Fetch();

    const params = new URLSearchParams();

    // Se ambas as datas estão presentes, usar o range local
    if (startDate && endDate) {
      const { startDate: localStart, endDate: localEnd } = buildLocalDayRange(startDate, endDate);
      params.append("startDate", localStart);
      params.append("endDate", localEnd);
    } else {
      // Fallback para comportamento anterior se apenas uma data for fornecida
      if (startDate)
        params.append("startDate", formatDateWithTimezone(startDate));
      if (endDate) params.append("endDate", formatDateWithTimezone(endDate));
    }

    const qs = params.toString() ? `?${params.toString()}` : "";

    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/portfolio/deal-value${qs}`
    );
    return data;
  } catch (error) {
    console.error("Error fetching total deal value", error);
    throw error;
  }
}

//Cobranças iniciadas por portfólio
//Tipo de data aceita: 2025-07-31T02:59:59-03:00
export async function getPortfolioFirstMessageMetrics(
  portfolioId: string,
  startDate: string,
  endDate: string
) {
  try {
    const { startDate: localStart, endDate: localEnd } = buildLocalDayRange(startDate, endDate);

    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/first-messages-sent/${portfolioId}?dateStart=${localStart}&dateEnd=${localEnd}`
    );
    return data;
  } catch (error) {
    console.error("Error fetching portfolio first message metrics:", error);
    throw error;
  }
}

//Ticket medio por portfólio
export async function getPortfolioAverageTicket(
  portfolioId: string,
  startDate?: string,
  endDate?: string
) {
  try {
    const fetch = Fetch();

    const params = new URLSearchParams();

    // Se ambas as datas estão presentes, usar o range local
    if (startDate && endDate) {
      const { startDate: localStart, endDate: localEnd } = buildLocalDayRange(startDate, endDate);
      params.append("startDate", localStart);
      params.append("endDate", localEnd);
    } else {
      // Fallback para comportamento anterior se apenas uma data for fornecida
      if (startDate)
        params.append("startDate", formatDateWithTimezone(startDate));
      if (endDate) params.append("endDate", formatDateWithTimezone(endDate));
    }

    const qs = params.toString() ? `?${params.toString()}` : "";

    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/portfolio/average-ticket/${portfolioId}${qs}`
    );
    return data;
  } catch (error) {
    console.error("Error fetching average ticket ", error);
    throw error;
  }
}

//Itens importados
//Tipo de data aceita: 2025-07-31
export async function fetchPortfolioItemsCreatedMetrics(
  startDate: string,
  endDate: string
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/portfolio/items/created?startDate=${startDate}&endDate=${endDate}`
    );

    return data;
  } catch (error) {
    console.error("Error fetching portfolio items metrics:", error);
    throw error;
  }
}
