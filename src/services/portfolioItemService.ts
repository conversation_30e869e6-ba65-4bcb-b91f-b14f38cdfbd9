/* eslint-disable @typescript-eslint/no-explicit-any */
// src/services/portfolioItemService.ts
import getEnv from "@/hooks/getEnv";
import { Fetch } from "@/interceptors/Fetch";
import { getAccessToken } from "@/interceptors/getJWTToken";
import { IPortfolioItem } from "@/types/portfolio";

export interface IMessageHistory {
  id: string;
  role: string;
  messageText: string;
  messageType: string;
  lang: string;
  fileUrl?: string;
  createdAt: Date;
  updatedAt: Date;
  sent?: boolean;
  sent_at?: Date;
  time_to_go?: Date;
}

export interface IExecutionHistoryEntry {
  id: string;
  portfolioItemId: string;
  newStatus: string;
  oldStatus: string | null;
  createdAt: string; // ISO
  updatedAt: string; // ISO
  reason: string | null;
  status: string; // ACTIVE, etc.
}

const transcendenceServiceEndpoint = getEnv("TRANSCENDENCE_SERVICE_URL");

export async function getPortfolioItem(id: string): Promise<IPortfolioItem> {
  try {
    // if (getEnv("SERVICE_MOCK_RESPONSE")) {
    //   const item =
    //     mockedPortfolioItems.find((item) => item.id === id) ||
    //     mockedPortfolioItems[0];
    //   return Promise.resolve(item);
    // }

    const fetch = Fetch();
    const data: any = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/${id}`
    );

    const middlewareResponses: Record<string, string> = {};

    const getFirstPropertyValue = (obj: any): any => {
      if (obj && typeof obj === "object") {
        const firstKey = Object.keys(obj)[0];
        return obj[firstKey];
      }
      return null;
    };

    if (
      data.middlewareResponseOutput &&
      typeof data.middlewareResponseOutput === "object"
    ) {
      Object.entries(data.middlewareResponseOutput).forEach(([key, value]) => {
        if (value && typeof value === "object") {
          middlewareResponses[key] = getFirstPropertyValue(value);
        }
      });
    }

    const insights = Object.values(middlewareResponses).join("\n");

    const portfolioItem = data as IPortfolioItem;
    portfolioItem.insights = insights;

    return portfolioItem;
  } catch (error) {
    console.error("Error fetching portfolio item: " + id, error);
    throw error;
  }
}

export async function getConversationHistory(
  id: string
): Promise<IMessageHistory[]> {
  try {
    //   if (getEnv("SERVICE_MOCK_RESPONSE")) {
    //     return Promise.resolve(mockedConversationHistory);
    //   }

    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/${id}/conversation-history`
    );

    return data as IMessageHistory[];
  } catch (error) {
    console.error("Error fetching portfolio item: " + id, error);
    throw error;
  }
}

export async function sendMessage(
  id: string,
  message: string,
  file?: File,
  messageType: string = "TEXT"
): Promise<IMessageHistory[]> {
  try {
    //   if (getEnv("SERVICE_MOCK_RESPONSE")) {
    //     return Promise.resolve(mockedConversationHistory);
    //   }

    const fetch = Fetch();
    const formData = new FormData();
    formData.append("message", message);
    formData.append("messageType", messageType);
    formData.append("roleType", "attendant");

    if (file) {
      formData.append("file", file);
    }

    const data = await fetch.post(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/${id}/conversation-history`,
      formData
    );

    return data as IMessageHistory[];
  } catch (error) {
    console.error("Error sending message:", error);
    throw error;
  }
}

export async function downloadAudioFile(
  id: string,
  filePath: string
): Promise<Blob> {
  const encodedPath = encodeURIComponent(filePath);
  const token = await getAccessToken();

  const response = await fetch(
    `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/${id}/conversation-history/files/audio/${encodedPath}`,
    {
      method: "GET",
      credentials: "include",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Erro ao baixar áudio: ${response.status}`);
  }
  const blob = await response.blob();

  console.log("DEBUG BLOB", blob.type, blob.size);

  return blob;
}

export async function downloadImageFile(
  id: string,
  filePath: string
): Promise<Blob> {
  const encodedPath = encodeURIComponent(filePath);
  const token = await getAccessToken();

  const response = await fetch(
    `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/${id}/conversation-history/files/image/${encodedPath}`,
    {
      method: "GET",
      credentials: "include",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Erro ao baixar imagem: ${response.status}`);
  }
  const blob = await response.blob();

  console.log("DEBUG IMAGE BLOB", blob.type, blob.size);

  return blob;
}

export async function downloadPdfFile(
  id: string,
  filePath: string
): Promise<Blob> {
  const encodedPath = encodeURIComponent(filePath);
  const token = await getAccessToken();

  const response = await fetch(
    `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/${id}/conversation-history/files/pdf/${encodedPath}`,
    {
      method: "GET",
      credentials: "include",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Erro ao baixar imagem: ${response.status}`);
  }
  const blob = await response.blob();

  console.log("DEBUG IMAGE BLOB", blob.type, blob.size);

  return blob;
}

export async function getExecutionHistoryForPortfolioItem(
  portfolioItemId: string
): Promise<IExecutionHistoryEntry[]> {
  const fetch = Fetch();
  const resp: any = await fetch.get(
    `${transcendenceServiceEndpoint}/v1/business-base/portfolio/items/execution/history/portfolio-item/${portfolioItemId}`
  );

  return Array.isArray(resp) ? resp : (resp?.data ?? []);
}
