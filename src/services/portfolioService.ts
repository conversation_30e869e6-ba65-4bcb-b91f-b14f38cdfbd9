/* eslint-disable @typescript-eslint/no-explicit-any */
// src/services/portfolioService.ts
import {
  IPortfolioUpload,
  Portfolio,
  IPortfolioItem,
  PortfolioItemStatus,
  Paginated,
  AverageTicketResponse,
} from "@/types/portfolio";
import getEnv from "@/hooks/getEnv";
import { Fetch } from "@/interceptors/Fetch";
import { getCustomer } from "@/services/customerService";
const transcendenceServiceEndpoint = getEnv("TRANSCENDENCE_SERVICE_URL");

export async function createPortfolio(
  portfolioToUpload: IPortfolioUpload
): Promise<any> {
  const formData = new FormData();
  formData.append("file", portfolioToUpload.file);
  formData.append("name", portfolioToUpload.name);
  formData.append("workflowId", portfolioToUpload.workflowId);
  formData.append("workExpression", "* 8-20 * * 1-5");
  formData.append(
    "executeImmediately",
    String(portfolioToUpload.executeImmediately ?? false)
  );
  formData.append(
    "communicationChannel",
    portfolioToUpload.communicationChannel
  );

  const fetch = Fetch();

  const data = await fetch.post(
    `${transcendenceServiceEndpoint}/v1/business-base/portfolios/import`,
    formData
  );

  return data as Portfolio;
}

export async function getPortfolio(
  id: string,
  startDate?: string,
  endDate?: string
): Promise<Portfolio> {
  try {
    const fetch = Fetch();
    let url = `${transcendenceServiceEndpoint}/v1/business-base/portfolios/${id}`;
    const params = [];
    if (startDate) params.push(`startDate=${startDate}`);
    if (endDate) params.push(`endDate=${endDate}`);
    if (params.length) url += `?${params.join("&")}`;

    const data = await fetch.get(url);
    return data as Portfolio;
  } catch (error) {
    console.error("Error fetching portfolio: " + id, error);
    throw error;
  }
}

export async function getAllPortfolios(
  currentPage?: number
): Promise<Portfolio[]> {
  const page = currentPage || 1;
  try {
    const fetch = Fetch();
    // pagination still not implemented on the backend
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios?page=${page}`
    );

    return data as Portfolio[];
  } catch (error) {
    console.error("Error fetching portfolios", error);
    throw error;
  }
}

export async function getAllPortfolioItems(
  searchParams: {
    currentStatus?: PortfolioItemStatus;
    portfolioId?: string;
    phoneNumber?: string | { contains: string };
    waitingBusinessUserResponse?: boolean;
    lastInteraction?: any;
    name?: string | { contains: string; mode: "insensitive" };
  },
  pagination: { page?: number; limit?: number },
  portfolios: Portfolio[],
  sortField: "createdAt" | "lastInteraction" = "createdAt",
  sortDirection: "asc" | "desc" = "desc"
): Promise<Paginated<IPortfolioItem>> {
  try {
    if (
      typeof searchParams.phoneNumber === "string" &&
      searchParams.phoneNumber.trim() !== ""
    ) {
      searchParams.phoneNumber = { contains: searchParams.phoneNumber };
    }
    if (
      typeof searchParams.name === "string" &&
      searchParams.name.trim() !== ""
    ) {
      searchParams.name = { contains: searchParams.name, mode: "insensitive" };
    }

    const page = pagination.page || 1;
    const limit = pagination.limit || 10;

    const customer = getCustomer();
    const customerId = customer.id;

    let sortJson = "";
    if (sortField === "lastInteraction") {
      sortJson = `{\"lastInteraction\": \"${sortDirection}\"}`;
    } else {
      sortJson = '{"createdAt": "desc"}';
    }

    const api = `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/search/paginated/${customerId}?sort=${sortJson}&page=${page}&limit=${limit}`;

    const fetch = Fetch();
    const data = await fetch.post(api, searchParams);

    const portfolioItems = data as Paginated<IPortfolioItem>;
    portfolios = portfolios.length > 0 ? portfolios : await getAllPortfolios();

    portfolioItems.items = portfolioItems.items.map((item) => ({
      ...item,
      portfolioName:
        portfolios.find((p) => p.id === item.portfolioId)?.name || "",
    }));

    return portfolioItems;
  } catch (error) {
    console.error("Error fetching portfolios", error);
    throw error;
  }
}

export async function downloadOriginalFile(id: string): Promise<Blob> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      // Return a mock Blob for testing purposes
      const mockBlob = new Blob(["Mock content"], { type: "text/plain" });
      return mockBlob;
    }

    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios/${id}/download`
    );

    const blob = new Blob([data as BlobPart]);
    return blob;
  } catch (error) {
    console.error("Error fetching portfolio: " + id, error);
    throw error;
  }
}

export async function downloadImportErrorsFile(id: string): Promise<Blob> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      const mockBlob = new Blob(["Mock content"], { type: "text/plain" });
      return mockBlob;
    }

    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios/${id}/download/import-errors`
    );

    const blob = new Blob([data as BlobPart]);
    return blob;
  } catch (error) {
    console.error("Error fetching portfolio: " + id, error);
    throw error;
  }
}

export async function exportPortfolioData(
  id: string,
  delimiter?: string
): Promise<Blob> {
  try {
    const fetch = Fetch();
    const data = await fetch.post(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios/${id}/export`,
      delimiter ? { delimiter } : {}
    );

    const blob = new Blob([data as BlobPart]);
    return blob;
  } catch (error) {
    console.error("Error fetching portfolio: " + id, error);
    throw error;
  }
}

export async function setPortfolioStatusToExecuting(
  portfolioId: string
): Promise<Portfolio> {
  try {
    const fetch = Fetch();
    const data = await fetch.put(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios/${portfolioId}/execute`
    );

    return data as Portfolio;
  } catch (error) {
    console.error("Error fetching portfolio performance:", error);
    throw error;
  }
}

export async function setPortfolioStatusToPaused(
  portfolioId: string
): Promise<Portfolio> {
  try {
    const fetch = Fetch();
    const data = await fetch.put(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios/${portfolioId}/pause`
    );

    return data as Portfolio;
  } catch (error) {
    console.error("Error fetching portfolio performance:", error);
    throw error;
  }
}

export async function setPortfolioStatusToCancelled(
  portfolioId: string
): Promise<Portfolio> {
  try {
    const fetch = Fetch();
    const data = await fetch.put(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios/${portfolioId}/cancel`
    );

    return data as Portfolio;
  } catch (error) {
    console.error("Error fetching portfolio performance:", error);
    throw error;
  }
}

export async function setPortfolioStatusToDeleted(
  portfolioId: string
): Promise<Portfolio> {
  try {
    const fetch = Fetch();
    const data = await fetch.delete(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios/${portfolioId}`
    );

    return data as Portfolio;
  } catch (error) {
    console.error("Error fetching portfolio performance:", error);
    throw error;
  }
}

export async function setPortfolioItemStatusToFinished(
  portfolioItemId: string
): Promise<IPortfolioItem> {
  try {
    // if (getEnv("SERVICE_MOCK_RESPONSE")) {
    //   const item =
    //     mockedPortfolioItems.find((item) => item.id === portfolioItemId) ||
    //     mockedPortfolioItems[0];

    //   return Promise.resolve({
    //     ...item,
    //     currentStatus: PortfolioItemStatus.FINISHED,
    //   });
    // }

    const fetch = Fetch();
    const data = await fetch.put(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/${portfolioItemId}/finish`
    );

    return data as IPortfolioItem;
  } catch (error) {
    console.error("Error fetching portfolio performance:", error);
    throw error;
  }
}

export async function setPortfolioItemStatusToSucceed(
  portfolioItemId: string
): Promise<IPortfolioItem> {
  try {
    // if (getEnv("SERVICE_MOCK_RESPONSE")) {
    //   const item =
    //     mockedPortfolioItems.find((item) => item.id === portfolioItemId) ||
    //     mockedPortfolioItems[0];

    //   return Promise.resolve({
    //     ...item,
    //     currentStatus: PortfolioItemStatus.SUCCEED,
    //   });
    // }

    const fetch = Fetch();
    const data = await fetch.put(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/${portfolioItemId}/succeed`
    );

    return data as IPortfolioItem;
  } catch (error) {
    console.error("Error fetching portfolio performance:", error);
    throw error;
  }
}

export async function getPortfolioAverageTicket(
  portfolioId: string,
  startDate?: string,
  endDate?: string
): Promise<AverageTicketResponse> {
  try {
    const fetch = Fetch();

    let queryParams = "";
    if (startDate && endDate) {
      // Importar o utilitário se necessário
      const { buildLocalDayRange } = await import("@/utils/formatDate");
      const { startDate: localStart, endDate: localEnd } = buildLocalDayRange(startDate, endDate);
      queryParams = `?startDate=${localStart}&endDate=${localEnd}`;
    } else if (startDate || endDate) {
      queryParams = `?startDate=${startDate || ""}&endDate=${endDate || ""}`;
    }

    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/portfolio/average-ticket/${portfolioId}${queryParams}`
    );
    return data as AverageTicketResponse;
  } catch (error) {
    console.error(
      "Error fetching average ticket for portfolio: " + portfolioId,
      error
    );
    throw error;
  }
}

// src/services/portfolioService.ts
export async function searchPortfolios(
  name?: string,
  page = 1,
  limit = 10
): Promise<{
  items: Portfolio[];
  total: number;
  page: number;
  totalPages: number;
}> {
  const params = new URLSearchParams();
  if (name) params.append("name", name);
  params.append("page", String(page));
  params.append("limit", String(limit));

  const fetch = Fetch();
  const resp: any = await fetch.getRaw(
    `${transcendenceServiceEndpoint}/v1/business-base/portfolios/search?${params.toString()}`
  );
  if (resp.data && resp.total !== undefined) {
    return {
      items: resp.data,
      total: resp.total,
      page: resp.page,
      totalPages: resp.totalPages,
    };
  }

  if (Array.isArray(resp)) {
    return {
      items: resp,
      total: resp.length,
      page,
      totalPages: 1,
    };
  }

  return {
    items: [],
    total: 0,
    page,
    totalPages: 1,
  };
}
