import getEnv from "@/hooks/getEnv";
import { Fetch } from "@/interceptors/Fetch";
import { ICustomerWorkflow } from "@/types/worflow";

const transcendenceServiceEndpoint = getEnv("TRANSCENDENCE_SERVICE_URL");

export async function getAllWorkflows(
  customerId: string
): Promise<ICustomerWorkflow[]> {
  const fetch = Fetch();
  const data = await fetch.get(
    `${transcendenceServiceEndpoint}/v1/business-base/customers/${customerId}/workflows`
  );

  return data as ICustomerWorkflow[];
}

export async function getWorkflowVariables(
  workflowId: string
): Promise<string[]> {
  const fetch = Fetch();
  const data = await fetch.get(
    `${transcendenceServiceEndpoint}/v1/orchestrator/workflows/${workflowId}/variables`
  );
  return data as string[];
}

export async function downloadTemplateCsv(workflowId: string): Promise<Blob> {
  const fetch = Fetch();
  return (await fetch.get(
    `${transcendenceServiceEndpoint}/v1/orchestrator/workflows/${workflowId}/template-csv`
  )) as Blob;
}
