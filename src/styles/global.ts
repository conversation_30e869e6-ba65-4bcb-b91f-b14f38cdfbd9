import { createGlobalStyle } from "styled-components";

export const GlobalStyles = createGlobalStyle`

  @font-face {
    font-family: 'Switzer';
    src: url('/fonts/Switzer-Regular.woff2') format('woff2'),
         url('/fonts/Switzer-Regular.woff') format('woff'),
         url('/fonts/Switzer-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: block;
  }

  @font-face {
    font-family: 'Switzer';
    src: url('/fonts/Switzer-Medium.woff2') format('woff2'),
         url('/fonts/Switzer-Medium.woff') format('woff'),
         url('/fonts/Switzer-Medium.ttf') format('truetype');
    font-weight: 500;
    font-style: normal;
    font-display: block;
  }

  /* @font-face {
    font-family: 'Switzer';
    src: url('/styles/Switzer/Fonts/OTF/Switzer-Regular.otf') format('opentype');
    font-weight: 500;
    font-style: regular;
  } */
  @font-face {
    font-family: 'Switzer';
    src: url('/fonts/Switzer-Semibold.woff2') format('woff2'),
         url('/fonts/Switzer-Semibold.woff') format('woff'),
         url('/fonts/Switzer-Semibold.ttf') format('truetype');
    font-weight: 600;
    font-style: normal;
    font-display: block;
  }

  *, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html, body {
    height: 100%;
    font-family: 'Switzer', sans-serif;
    background-color: ${({ theme }) => theme.colors.background};
    color: ${({ theme }) => theme.colors.neutral900};
    -webkit-font-smoothing: antialiased;
  }

  body {
    min-height: 100vh;
    width: 100%;
  }

  a {
    text-decoration: none;
    color: inherit;
  }

  button {
    font-family: inherit;
    cursor: pointer;
    border: none;
    background: none;
  }
`;
