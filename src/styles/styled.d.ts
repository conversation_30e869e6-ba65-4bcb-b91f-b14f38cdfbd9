import "styled-components";

declare module "styled-components" {
  export interface DefaultTheme {
    colors: {
      primary: string;
      background: string;
      neutral100: string;
      neutral200: string;
      neutral300: string;
      neutral400: string;
      neutral500: string;
      neutral600: string;
      neutral700: string;
      neutral800: string;
      neutral900: string;
      success: string;
      error: string;
      labelInput: string;
      pending: string;
      purple: string;
      disabled: string;
      disabledText: string;
      link: string;
      border: {
        pending: string;
        success: string;
        error: string;
      };
    };

    spacing: {
      sm: string;
      md: string;
      lg: string;
    };
    fontSize: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
    fontWeight: {
      light: string;
      regular: string;
      medium: string;
      bold: string;
    };
    media: {
      hover: string;
    };
  }
}
