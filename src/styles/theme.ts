import { DefaultTheme } from "styled-components";

export const theme: DefaultTheme = {
  colors: {
    primary: "#111C9D",
    background: "#FEFEFE",
    success: "#399D54",
    neutral100: "#FFFFFF",
    neutral200: "#F5F5F5",
    neutral300: "#F0F0F0",
    neutral400: "#CACACA",
    neutral500: "#E8E8E8",
    neutral600: "#E9E9E9",
    neutral700: "#9E9E9E",
    neutral800: "#898989",
    neutral900: "#212529",
    error: "#A40000",
    labelInput: "#B5B6C2",
    pending: "#FBB03B",
    purple: "#4B3CFF",
    disabled: "#EAEAEA",
    disabledText: "#B2B3BE",
    link: "#2757FF",
    border: {
      pending: "#C08425",
      success: "#097927",
      error: "#5E0000",
    },
  },
  spacing: {
    sm: "4px",
    md: "8px",
    lg: "16px",
  },
  fontSize: {
    xs: "12px",
    sm: "14px",
    md: "16px",
    lg: "24px",
    xl: "32px",
  },
  fontWeight: {
    light: "300",
    regular: "400",
    medium: "500",
    bold: "600",
  },
  media: {
    hover: "@media (hover: hover)",
  },
};
