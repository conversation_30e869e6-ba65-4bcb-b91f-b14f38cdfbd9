// src/types/portfolio.ts
export enum RecordStatus {
  ACTIVE = "ACTIVE",
  DELETED = "DELETED",
}

export enum PortfolioImportStatus {
  UPLOADED = "UPLOADED",
  PROCESSING = "PROCESSING",
  SUCCESS = "SUCCESS",
  UNKNOWN = "UNKNOWN",
}

export enum PortfolioExecutionStatus {
  EXECUTING = "EXECUTING",
  QUEUED = "QUEUED",
  PAUSED = "PAUSED",
  WAITING = "WAITING",
  CANCELLED = "CANCELLED",
  INBOUND = "INBOUND_EXECUTING",
  FINISHED = "FINISHED",
}

export enum PortfolioItemStatus {
  PENDING = "PENDING", // aguardando envio da primeira mensagem
  IN_PROGRESS = "IN_PROGRESS", //envio da primeira mensagem com sucesso
  PAUSED = "PAUSED", // parado pelo customer e pode voltar a in progress
  SUCCEED = "SUCCEED", // negociação finalizou com sucesso
  FAILED = "FAILED", // negociação finalizou sem sucesso
  CANCELLED = "CANCELLED", // item cancelado pelo customer por resolução externa
  UNLINKED = "UNLINKED", // O customer desvinculou a fala da IA e passa a digitar as mensagens por conta própria
  IDLE = "IDLE", // This status will be used when last_interaction is greater than portfolio.idle_after
  FOLLOWED_UP = "FOLLOWED_UP", //foi feito um envio de follow-up e está aguardando resposta
  OPTED_OUT = "OPTED_OUT", // cliente optou por não continuar a conversa
  FINISHED = "FINISHED", //Atendimento encerrado após os follow-ups
  SCHEDULED_FOLLOW_UP = "SCHEDULED_FOLLOW_UP", // acompanhamento agendado/follow-up agendado
  NUMBER_NOT_EXISTS = "NUMBER_NOT_EXISTS", // número não existe
}

export type Portfolio = {
  id: string;
  name: string;
  workflowId: string;
  workExpression: string;
  executeImmediately: boolean;
  customerId: string;
  executionStatus: PortfolioExecutionStatus;
  importStatus: PortfolioImportStatus;
  originalFileName: string;
  totalQuantity: number;
  processedQuantity: number;
  totalSuccessQuantity: number;
  totalFailedQuantity: number;
  importFinishedAt: Date;
  createdAt: Date;
  updatedAt: Date;
  status: RecordStatus;
  fileUrl: string;
  dealValue: string;
};

export interface IPortfolioUpload {
  file: File;
  name: string;
  workflowId: string;
  executeImmediately?: boolean;
  communicationChannel: string;
}

export interface IPortfolioItem {
  id: string;
  portfolioId: string;
  portfolioName?: string;
  phoneNumber: string;
  customData?: Record<string, string>;
  contactName: string;
  customDataId: string;
  line: number;
  lastInteraction?: Date;
  waitingBusinessUserResponse?: boolean;
  currentStatus: PortfolioItemStatus;
  createdAt?: Date;
  updatedAt?: Date;
  insights?: string;
}
export type Paginated<T> = {
  items: T[];
  total: number;
  limit: number;
  page: number;
  totalPages: number;
};

export type AverageTicketResponse = {
  averageTicket: string;
  startDate?: string;
  endDate?: string;
};
