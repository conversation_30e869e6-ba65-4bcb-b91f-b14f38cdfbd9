import { buildLocalDayRange, buildLocalDayRangeSafe } from '../formatDate';

describe('buildLocalDayRange', () => {
  it('should build correct day boundaries with -03:00 timezone', () => {
    const { startDate, endDate } = buildLocalDayRange('2025-09-05', '2025-09-12');
    
    // Verificar que as datas terminam com -03:00
    expect(startDate.endsWith('-03:00')).toBe(true);
    expect(endDate.endsWith('-03:00')).toBe(true);
    
    // Verificar que o início do dia é 00:00:00.000
    expect(startDate.includes('T00:00:00.000')).toBe(true);
    
    // Verificar que o fim do dia é 23:59:59.999
    expect(endDate.includes('T23:59:59.999')).toBe(true);
  });

  it('should handle ISO datetime strings correctly', () => {
    const { startDate, endDate } = buildLocalDayRange(
      '2025-09-05T15:30:45.123Z', 
      '2025-09-12T08:15:30.456Z'
    );
    
    // Deve sempre usar início e fim do dia no fuso local, independente da hora original
    expect(startDate.includes('T00:00:00.000-03:00')).toBe(true);
    expect(endDate.includes('T23:59:59.999-03:00')).toBe(true);
  });

  it('should handle same day range correctly', () => {
    const { startDate, endDate } = buildLocalDayRange('2025-09-05', '2025-09-05');
    
    expect(startDate.includes('T00:00:00.000-03:00')).toBe(true);
    expect(endDate.includes('T23:59:59.999-03:00')).toBe(true);
    
    // Ambas devem ter a mesma data
    expect(startDate.substring(0, 10)).toBe('2025-09-05');
    expect(endDate.substring(0, 10)).toBe('2025-09-05');
  });

  it('should throw error for invalid dates', () => {
    expect(() => buildLocalDayRange('invalid-date', '2025-09-05')).toThrow();
    expect(() => buildLocalDayRange('2025-09-05', 'invalid-date')).toThrow();
  });

  it('should throw error for empty strings', () => {
    expect(() => buildLocalDayRange('', '2025-09-05')).toThrow();
    expect(() => buildLocalDayRange('2025-09-05', '')).toThrow();
  });

  it('should throw error when start date is after end date', () => {
    expect(() => buildLocalDayRange('2025-09-12', '2025-09-05')).toThrow();
  });
});

describe('buildLocalDayRangeSafe', () => {
  it('should return valid range for valid dates', () => {
    const { startDate, endDate } = buildLocalDayRangeSafe('2025-09-05', '2025-09-12');
    
    expect(startDate.endsWith('-03:00')).toBe(true);
    expect(endDate.endsWith('-03:00')).toBe(true);
  });

  it('should fallback gracefully for invalid dates', () => {
    const { startDate, endDate } = buildLocalDayRangeSafe('invalid-date', '2025-09-05');
    
    // Deve retornar algum valor (fallback)
    expect(typeof startDate).toBe('string');
    expect(typeof endDate).toBe('string');
  });

  it('should not throw errors', () => {
    expect(() => buildLocalDayRangeSafe('', '')).not.toThrow();
    expect(() => buildLocalDayRangeSafe('invalid', 'invalid')).not.toThrow();
  });
});
