import { buildLocalDayRange } from '../formatDate';

describe('buildLocalDayRange', () => {
  it('should build correct day boundaries with -03:00 timezone', () => {
    const { startDate, endDate } = buildLocalDayRange('2025-09-05', '2025-09-12');
    
    // Verificar que as datas terminam com -03:00
    expect(startDate.endsWith('-03:00')).toBe(true);
    expect(endDate.endsWith('-03:00')).toBe(true);
    
    // Verificar que o início do dia é 00:00:00.000
    expect(startDate.includes('T00:00:00.000')).toBe(true);
    
    // Verificar que o fim do dia é 23:59:59.999
    expect(endDate.includes('T23:59:59.999')).toBe(true);
  });

  it('should handle ISO datetime strings correctly', () => {
    const { startDate, endDate } = buildLocalDayRange(
      '2025-09-05T15:30:45.123Z', 
      '2025-09-12T08:15:30.456Z'
    );
    
    // Deve sempre usar início e fim do dia no fuso local, independente da hora original
    expect(startDate.includes('T00:00:00.000-03:00')).toBe(true);
    expect(endDate.includes('T23:59:59.999-03:00')).toBe(true);
  });

  it('should handle same day range correctly', () => {
    const { startDate, endDate } = buildLocalDayRange('2025-09-05', '2025-09-05');
    
    expect(startDate.includes('T00:00:00.000-03:00')).toBe(true);
    expect(endDate.includes('T23:59:59.999-03:00')).toBe(true);
    
    // Ambas devem ter a mesma data
    expect(startDate.substring(0, 10)).toBe('2025-09-05');
    expect(endDate.substring(0, 10)).toBe('2025-09-05');
  });
});
