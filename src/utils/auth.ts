export async function verifyRefreshToken(token: string) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_TRANSCENDENCE_SERVICE_URL}/v1/auth/session/token-is-valid`,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + token,
        },
        method: "GET",
      }
    );

    const { data } = await response.json();

    if (!response.ok) {
      console.error("Error:", data.message);
      return false;
    } else {
      return data.valid;
    }
  } catch (error) {
    console.error("Error:", error);
    return false;
  }
}
