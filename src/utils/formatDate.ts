export function formatRelativeDate(createdAt: string): string {
  const createdDate = new Date(createdAt);
  const now = new Date();
  const diffMs = now.getTime() - createdDate.getTime();

  const minutes = Math.floor(diffMs / (1000 * 60)) % 60;
  const hours = Math.floor(diffMs / (1000 * 60 * 60));
  const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (minutes < 1) {
    return "1 minuto";
  } else if (hours < 1) {
    return `${minutes} minutos`;
  } else if (days < 1) {
    const hourStr = hours === 1 ? "1 hora" : `${hours} horas`;
    const minuteStr = minutes === 1 ? "1 minuto" : `${minutes} minutos`;
    return `${hourStr} e ${minuteStr}`;
  } else {
    return days === 1 ? "1 dia" : `${days} dias`;
  }
}

export function formatDate(date: Date | string) {
  if (!date) return "";
  const d = new Date(date);

  const day = d.getDate().toString().padStart(2, "0");
  const month = d
    .toLocaleDateString("pt-BR", { month: "long" })
    .toUpperCase()
    .substring(0, 3);
  const year = d.getFullYear();
  const hours = d.getHours().toString().padStart(2, "0");
  const minutes = d.getMinutes().toString().padStart(2, "0");

  return `${day}-${month}-${year}, ${hours}:${minutes}`;
}

export function formatDateDefault(date: Date | string) {
  if (!date) return "";
  const d = new Date(date);

  const day = d.getDate().toString().padStart(2, "0");
  const month = (d.getMonth() + 1).toString().padStart(2, "0");
  const year = d.getFullYear();
  const hours = d.getHours().toString().padStart(2, "0");
  const minutes = d.getMinutes().toString().padStart(2, "0");

  return `${day}/${month}/${year}  ${hours}:${minutes}`;
}

export function formatDateLong(date: Date | string) {
  if (!date) return "";
  const d = new Date(date);

  const day = d.getDate().toString().padStart(2, "0");
  const month = d.toLocaleDateString("pt-BR", { month: "long" });
  const year = d.getFullYear();

  return `${day} de ${month.charAt(0).toUpperCase() + month.slice(1)}, ${year}`;
}

export function formatDateToHour(date: Date | string) {
  if (!date) return "";
  const d = new Date(date);
  const hours = d.getHours().toString().padStart(2, "0");
  const minutes = d.getMinutes().toString().padStart(2, "0");
  return `${hours}h${minutes}`;
}

export function timeAgo(from: Date) {
  const now = new Date();
  const diffMs = now.getTime() - from.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffHoursRaw = diffMs / (1000 * 60 * 60);
  const diffHours = Math.floor(diffHoursRaw);
  const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

  if (diffDays > 0) {
    if (diffDays === 1) {
      if (diffHours % 24 === 1) return "1 dia e 1 hora";
      if (diffHours % 24 > 1) return `1 dia e ${diffHours % 24} horas`;
      return "1 dia";
    } else {
      if (diffHours % 24 === 1) return `${diffDays} dias e 1 hora`;
      if (diffHours % 24 > 1) return `${diffDays}d e ${diffHours % 24} horas`;
      return `${diffDays} dias`;
    }
  }

  if (diffHoursRaw >= 1) {
    if (diffHoursRaw < 2) {
      return diffHours === 1 ? "1 hora" : `${diffHours} horas`;
    } else {
      const arredondado = Math.round(diffHoursRaw);
      return arredondado === 1 ? "1 hora" : `${arredondado} horas`;
    }
  }

  if (diffMins > 0) {
    return diffMins === 1 ? "1 minuto" : `${diffMins} minutos`;
  }

  return "Agora";
}

export function formatDateLongWithTime(date: Date | string) {
  if (!date) return "";
  const d = new Date(date);
  const day = d.getDate().toString().padStart(2, "0");
  const month = d.toLocaleDateString("pt-BR", { month: "long" });
  const year = d.getFullYear();
  const hours = d.getHours().toString().padStart(2, "0");
  const minutes = d.getMinutes().toString().padStart(2, "0");
  return `${day} de ${month}, ${year} às ${hours}h${minutes}`;
}

export function formatDateShort(date: Date | string) {
  if (!date) return "";
  const d = new Date(date);
  const day = d.getDate().toString().padStart(2, "0");
  const month = (d.getMonth() + 1).toString().padStart(2, "0");
  const year = d.getFullYear();
  return `${day}/${month}/${year}`;
}

import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
dayjs.extend(utc);
dayjs.extend(timezone);

const TZ = "America/Sao_Paulo";

/**
 * Constrói um range de datas com início e fim do dia no fuso horário local (-03:00)
 * @param fromISO Data de início (formato ISO ou YYYY-MM-DD)
 * @param toISO Data de fim (formato ISO ou YYYY-MM-DD)
 * @returns Objeto com startDate e endDate formatadas para envio à API
 * @throws Error se as datas forem inválidas
 */
export function buildLocalDayRange(fromISO: string, toISO: string) {
  // Validação de entrada
  if (!fromISO || !toISO) {
    throw new Error('buildLocalDayRange: fromISO e toISO são obrigatórios');
  }

  if (typeof fromISO !== 'string' || typeof toISO !== 'string') {
    throw new Error('buildLocalDayRange: fromISO e toISO devem ser strings');
  }

  // Parsing com validação
  const start = dayjs.tz(fromISO, TZ);
  const end = dayjs.tz(toISO, TZ);

  // Verificar se as datas são válidas
  if (!start.isValid()) {
    throw new Error(`buildLocalDayRange: Data de início inválida: ${fromISO}`);
  }

  if (!end.isValid()) {
    throw new Error(`buildLocalDayRange: Data de fim inválida: ${toISO}`);
  }

  // Verificar se a data de início não é posterior à data de fim
  if (start.isAfter(end)) {
    throw new Error('buildLocalDayRange: Data de início não pode ser posterior à data de fim');
  }

  const startOfDay = start.startOf('day');
  const endOfDay = end.endOf('day');

  return {
    startDate: startOfDay.format('YYYY-MM-DD[T]HH:mm:ss.SSSZ'),
    endDate: endOfDay.format('YYYY-MM-DD[T]HH:mm:ss.SSSZ'),
  };
}

/**
 * Versão segura que retorna o comportamento antigo em caso de erro
 * @param fromISO Data de início
 * @param toISO Data de fim
 * @returns Range de datas ou fallback para formatDateWithTimezone
 */
export function buildLocalDayRangeSafe(fromISO: string, toISO: string) {
  try {
    return buildLocalDayRange(fromISO, toISO);
  } catch (error) {
    console.warn('buildLocalDayRange falhou, usando fallback:', error);
    // Fallback para comportamento anterior
    return {
      startDate: dayjs.utc(fromISO).tz(TZ).format(),
      endDate: dayjs.utc(toISO).tz(TZ).format(),
    };
  }
}

export function formatDateShortTZ(d?: string | Date | null) {
  if (!d) return "";
  return dayjs.utc(d).tz(TZ).format("DD/MM/YYYY HH:mm");
}

export function debugTZ(label: string, d: string | Date) {
  console.log(
    label,
    "raw:",
    d,
    "| BR:",
    dayjs.utc(d).tz(TZ).format("DD/MM/YYYY HH:mm:ss")
  );
}
