export function formatRelativeDate(createdAt: string): string {
  const createdDate = new Date(createdAt);
  const now = new Date();
  const diffMs = now.getTime() - createdDate.getTime();

  const minutes = Math.floor(diffMs / (1000 * 60)) % 60;
  const hours = Math.floor(diffMs / (1000 * 60 * 60));
  const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (minutes < 1) {
    return "1 minuto";
  } else if (hours < 1) {
    return `${minutes} minutos`;
  } else if (days < 1) {
    const hourStr = hours === 1 ? "1 hora" : `${hours} horas`;
    const minuteStr = minutes === 1 ? "1 minuto" : `${minutes} minutos`;
    return `${hourStr} e ${minuteStr}`;
  } else {
    return days === 1 ? "1 dia" : `${days} dias`;
  }
}

export function formatDate(date: Date | string) {
  if (!date) return "";
  const d = new Date(date);

  const day = d.getDate().toString().padStart(2, "0");
  const month = d
    .toLocaleDateString("pt-BR", { month: "long" })
    .toUpperCase()
    .substring(0, 3);
  const year = d.getFullYear();
  const hours = d.getHours().toString().padStart(2, "0");
  const minutes = d.getMinutes().toString().padStart(2, "0");

  return `${day}-${month}-${year}, ${hours}:${minutes}`;
}

export function formatDateDefault(date: Date | string) {
  if (!date) return "";
  const d = new Date(date);

  const day = d.getDate().toString().padStart(2, "0");
  const month = (d.getMonth() + 1).toString().padStart(2, "0");
  const year = d.getFullYear();
  const hours = d.getHours().toString().padStart(2, "0");
  const minutes = d.getMinutes().toString().padStart(2, "0");

  return `${day}/${month}/${year}  ${hours}:${minutes}`;
}

export function formatDateLong(date: Date | string) {
  if (!date) return "";
  const d = new Date(date);

  const day = d.getDate().toString().padStart(2, "0");
  const month = d.toLocaleDateString("pt-BR", { month: "long" });
  const year = d.getFullYear();

  return `${day} de ${month.charAt(0).toUpperCase() + month.slice(1)}, ${year}`;
}

export function formatDateToHour(date: Date | string) {
  if (!date) return "";
  const d = new Date(date);
  const hours = d.getHours().toString().padStart(2, "0");
  const minutes = d.getMinutes().toString().padStart(2, "0");
  return `${hours}h${minutes}`;
}

export function timeAgo(from: Date) {
  const now = new Date();
  const diffMs = now.getTime() - from.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffHoursRaw = diffMs / (1000 * 60 * 60);
  const diffHours = Math.floor(diffHoursRaw);
  const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

  if (diffDays > 0) {
    if (diffDays === 1) {
      if (diffHours % 24 === 1) return "1 dia e 1 hora";
      if (diffHours % 24 > 1) return `1 dia e ${diffHours % 24} horas`;
      return "1 dia";
    } else {
      if (diffHours % 24 === 1) return `${diffDays} dias e 1 hora`;
      if (diffHours % 24 > 1) return `${diffDays}d e ${diffHours % 24} horas`;
      return `${diffDays} dias`;
    }
  }

  if (diffHoursRaw >= 1) {
    if (diffHoursRaw < 2) {
      return diffHours === 1 ? "1 hora" : `${diffHours} horas`;
    } else {
      const arredondado = Math.round(diffHoursRaw);
      return arredondado === 1 ? "1 hora" : `${arredondado} horas`;
    }
  }

  if (diffMins > 0) {
    return diffMins === 1 ? "1 minuto" : `${diffMins} minutos`;
  }

  return "Agora";
}

export function formatDateLongWithTime(date: Date | string) {
  if (!date) return "";
  const d = new Date(date);
  const day = d.getDate().toString().padStart(2, "0");
  const month = d.toLocaleDateString("pt-BR", { month: "long" });
  const year = d.getFullYear();
  const hours = d.getHours().toString().padStart(2, "0");
  const minutes = d.getMinutes().toString().padStart(2, "0");
  return `${day} de ${month}, ${year} às ${hours}h${minutes}`;
}

export function formatDateShort(date: Date | string) {
  if (!date) return "";
  const d = new Date(date);
  const day = d.getDate().toString().padStart(2, "0");
  const month = (d.getMonth() + 1).toString().padStart(2, "0");
  const year = d.getFullYear();
  return `${day}/${month}/${year}`;
}

import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
dayjs.extend(utc);
dayjs.extend(timezone);

const TZ = "America/Sao_Paulo";

/**
 * Constrói um range de datas com início e fim do dia no fuso horário local (-03:00)
 * @param fromISO Data de início (formato ISO ou YYYY-MM-DD)
 * @param toISO Data de fim (formato ISO ou YYYY-MM-DD)
 * @returns Objeto com startDate e endDate formatadas para envio à API
 */
export function buildLocalDayRange(fromISO: string, toISO: string) {
  const start = dayjs.tz(fromISO, TZ).startOf('day');
  const end = dayjs.tz(toISO, TZ).endOf('day');

  return {
    startDate: start.format('YYYY-MM-DD[T]HH:mm:ss.SSSZ'),
    endDate: end.format('YYYY-MM-DD[T]HH:mm:ss.SSSZ'),
  };
}

export function formatDateShortTZ(d?: string | Date | null) {
  if (!d) return "";
  return dayjs.utc(d).tz(TZ).format("DD/MM/YYYY HH:mm");
}

export function debugTZ(label: string, d: string | Date) {
  console.log(
    label,
    "raw:",
    d,
    "| BR:",
    dayjs.utc(d).tz(TZ).format("DD/MM/YYYY HH:mm:ss")
  );
}
