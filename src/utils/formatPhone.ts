// src/utils/formatPhone.ts

export function formatPhone(phone: string): string {
  if (!phone) return "";

  const digits = onlyNumbers(phone);

  let ddi: string;
  let ddd: string;
  let subscriber: string;

  if (digits.length === 11) {
    ddi = "55";
    ddd = digits.slice(0, 2);
    subscriber = digits.slice(2);
  } else if (digits.length >= 12) {
    ddi = digits.slice(0, 2);
    ddd = digits.slice(2, 4);
    subscriber = digits.slice(4);
  } else {
    return phone;
  }

  let firstPart: string;
  let secondPart: string;

  if (subscriber.length === 9) {
    firstPart = subscriber.slice(0, 5);
    secondPart = subscriber.slice(5);
  } else if (subscriber.length === 8) {
    firstPart = subscriber.slice(0, 4);
    secondPart = subscriber.slice(4);
  } else {
    const half = Math.ceil(subscriber.length / 2);
    firstPart = subscriber.slice(0, half);
    secondPart = subscriber.slice(half);
  }

  return `+${ddi} (${ddd}) ${firstPart}-${secondPart}`;
}

export function onlyNumbers(str: string) {
  return str.replace(/\D/g, "");
}
