import { Clock, CircleCheckBig } from "lucide-react";
import { theme } from "@/styles/theme";
import { MdKeyboardDoubleArrowRight } from "react-icons/md";

export function getImportStatusInfo(status: string) {
  switch (status) {
    case "success":
      return {
        label: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        icon: <CircleCheckBig size={24} color={theme.colors.success} />,
      };
    case "uploaded":
      return {
        label: "Em fila",
        icon: <Clock size={24} color={theme.colors.purple} />,
      };
    case "processing":
      return {
        label: "Em processamento",
        icon: (
          <MdKeyboardDoubleArrowRight size={24} color={theme.colors.pending} />
        ),
      };
  }
}
