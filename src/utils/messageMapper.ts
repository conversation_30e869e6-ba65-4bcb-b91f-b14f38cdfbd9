import { IMessageHistory } from "@/services/portfolioItemService";
import { MessageProps } from "@/components/NegotiationModal/ChatArea/Message";
import { formatDateDefault } from "./formatDate";
import { getMessageKey } from "./getMessageKey";

export function mapApiMessageToMessageProps(
  msg: IMessageHistory,
  portfolioItemId?: string
): MessageProps {
  const createdAtFormatted = formatDateDefault(msg.createdAt);
  const timeToGoFormatted = msg.time_to_go
    ? formatDateDefault(msg.time_to_go)
    : undefined;
  const sentAtFormatted =
    msg.sent_at === null
      ? null
      : msg.sent_at
        ? formatDateDefault(msg.sent_at)
        : undefined;

  const uid = getMessageKey(msg);
  const mapRole = (r: string) =>
    r === "attendant" ? "assistant" : (r as "user" | "assistant");

  function extractFilename(url: string) {
    return url.split("/").pop() ?? "arquivo.pdf";
  }

  const getPreferredDate = (
    sentAtFmt: string | undefined | null,
    timeToGoFmt: string | undefined,
    createdFmt: string
  ): { date: string; isScheduled: boolean } => {
    if (sentAtFmt !== undefined) {
      if (sentAtFmt !== null) return { date: sentAtFmt, isScheduled: false };
      if (timeToGoFmt) return { date: timeToGoFmt, isScheduled: true };
    }
    return { date: createdFmt, isScheduled: false };
  };

  // AUDIO
  if (
    (msg.messageType === "AUDIO" || /^audio\//i.test(msg.messageType)) &&
    msg.fileUrl
  ) {
    const { date, isScheduled } = getPreferredDate(
      sentAtFormatted,
      timeToGoFormatted,
      createdAtFormatted
    );
    return {
      id: uid,
      type: "audio",
      role: mapRole(msg.role),
      src: msg.fileUrl,
      name:
        msg.role === "attendant"
          ? "Operador"
          : msg.role === "assistant"
            ? "DigAI Negocia"
            : "Contato",
      date,
      sent: msg.sent,
      time_to_go: timeToGoFormatted,
      isScheduled,
      portfolioItemId,
    };
  }

  // IMAGE
  if (
    msg.messageType &&
    ["image/jpeg", "image/jpg", "image/png", "image/gif", "IMAGE"].includes(
      msg.messageType
    ) &&
    !msg.fileUrl
  ) {
    const { date, isScheduled } = getPreferredDate(
      sentAtFormatted,
      timeToGoFormatted,
      createdAtFormatted
    );
    return {
      id: uid,
      type: "image",
      role: mapRole(msg.role),
      url: "",
      filename: "",
      messageText: msg.messageText,
      name:
        msg.role === "attendant"
          ? "Operador"
          : msg.role === "assistant"
            ? "DigAI Negocia"
            : "Contato",
      date,
      sent: msg.sent,
      time_to_go: timeToGoFormatted,
      isScheduled,
      portfolioItemId,
    };
  }

  // IMAGE
  if (
    msg.fileUrl &&
    (["image/jpeg", "image/jpg", "image/png", "image/gif", "IMAGE"].includes(
      msg.messageType
    ) ||
      /\.(jpg|jpeg|png|gif)$/i.test(msg.fileUrl))
  ) {
    const { date, isScheduled } = getPreferredDate(
      sentAtFormatted,
      timeToGoFormatted,
      createdAtFormatted
    );
    return {
      id: uid,
      type: "image",
      role: mapRole(msg.role),
      url: msg.fileUrl,
      filename: extractFilename(msg.fileUrl),
      messageText: msg.messageText,
      name:
        msg.role === "attendant"
          ? "Operador"
          : msg.role === "assistant"
            ? "DigAI Negocia"
            : "Contato",
      date,
      sent: msg.sent,
      time_to_go: timeToGoFormatted,
      isScheduled,
      portfolioItemId,
    };
  }

  // PDF
  if (
    msg.fileUrl &&
    (msg.messageType === "application/pdf" ||
      msg.messageType === "PDF" ||
      /\.pdf$/i.test(msg.fileUrl))
  ) {
    const { date, isScheduled } = getPreferredDate(
      sentAtFormatted,
      timeToGoFormatted,
      createdAtFormatted
    );
    return {
      id: uid,
      type: "file",
      role: mapRole(msg.role),
      url: msg.fileUrl,
      filename: extractFilename(msg.fileUrl),
      messageText: msg.messageText,
      name:
        msg.role === "attendant"
          ? "Operador"
          : msg.role === "assistant"
            ? "DigAI Negocia"
            : "Contato",
      date,
      sent: msg.sent,
      time_to_go: timeToGoFormatted,
      isScheduled,
      portfolioItemId,
    };
  }

  // TEXT
  const { date, isScheduled } = getPreferredDate(
    sentAtFormatted,
    timeToGoFormatted,
    createdAtFormatted
  );
  return {
    id: uid,
    type: "text",
    role: mapRole(msg.role),
    text: msg.messageText,
    name:
      msg.role === "attendant"
        ? "Operador"
        : msg.role === "assistant"
          ? "DigAI Negocia"
          : "Contato",
    date,
    sent: msg.sent,
    time_to_go: timeToGoFormatted,
    isScheduled,
  };
}
