/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { Portfolio, PortfolioImportStatus } from "@/types/portfolio";
import { getImportStatusInfo } from "@/utils/importStatus";
import { theme } from "@/styles/theme";
import { FileText, CircleArrowDown } from "lucide-react";
import { MoreOptionsButton } from "@/components/MoreOptionsButton";
import { InfoGrid } from "@/components/InfoGrid";
import ProgressFooter from "@/components/ProgressFooter";
import { formatRelativeDate, formatDateDefault } from "@/utils/formatDate";
import { downloadFile } from "@/utils/downloadFile";
import { downloadOriginalFile } from "@/services/portfolioService";
import StatusTag from "@/components/StatusTag";
import styled from "styled-components";

export function portfolioToCardData(
  portfolio: Portfolio,
  { onFailClick }: { onFailClick: () => void }
) {
  const statusInfo = getImportStatusInfo(portfolio.importStatus.toLowerCase());
  let subtitle = "Não há dados";
  if (portfolio.importStatus === PortfolioImportStatus.UPLOADED) {
    subtitle = "Não há dados";
  } else if (portfolio.importStatus === PortfolioImportStatus.PROCESSING) {
    subtitle = `Importado há ${formatRelativeDate(portfolio.createdAt.toString())} `;
  } else if (portfolio.importStatus === PortfolioImportStatus.SUCCESS) {
    subtitle = `Concluído: ${formatDateDefault(portfolio.importFinishedAt?.toString() || "")}`;
  }

  const navItems = [
    {
      key: "download",
      label: "Baixar arquivo original",
      onClick: async () => {
        try {
          const blob = await downloadOriginalFile(portfolio.id);
          downloadFile(blob, portfolio.originalFileName || "arquivo.csv");
        } catch {
          alert("Erro ao baixar o arquivo");
        }
      },
      icon: <CircleArrowDown size={16} color={theme.colors.neutral700} />,
    },
  ];

  const isUploaded = portfolio.importStatus === PortfolioImportStatus.UPLOADED;
  const hasFail = !isUploaded && portfolio.totalFailedQuantity > 0;

  const rows = [
    [
      {
        label: "Data da importação",
        value: formatDateDefault(portfolio.createdAt),
      },
      { label: "Total de itens", value: portfolio.totalQuantity },
    ],
    [
      {
        label: "Adicionados",
        value: isUploaded ? (
          <StatusTag label="Não há dados" variant="neutral" size="small" />
        ) : (
          portfolio.totalSuccessQuantity
        ),
        valueColor: !isUploaded ? theme.colors.success : undefined,
      },
      {
        label: "Falharam",
        value: isUploaded ? (
          <StatusTag label="Não há dados" variant="neutral" size="small" />
        ) : (
          portfolio.totalFailedQuantity
        ),
        valueColor: !isUploaded ? theme.colors.error : undefined,
        icon: <FileText size={16} />,
        onClick: hasFail ? onFailClick : undefined,
        clickable: hasFail,
      },
    ],
  ];

  const percent =
    portfolio.totalQuantity > 0
      ? Math.round(
          (portfolio.processedQuantity / portfolio.totalQuantity) * 100
        )
      : 0;

  const PortfolioLink = styled.span`
    cursor: pointer;
    text-decoration: none;
    &:hover {
      text-decoration: underline;
    }
  `;

  return {
    id: portfolio.id,
    originalImportStatus: portfolio.importStatus,
    originalExecutionStatus: portfolio.executionStatus,
    header: {
      title:
        portfolio.importStatus === PortfolioImportStatus.SUCCESS ? (
          <PortfolioLink
            onClick={() =>
              window.location.replace(`/portfolio/${portfolio.id}`)
            }
          >
            {portfolio.name}
          </PortfolioLink>
        ) : (
          portfolio.name
        ),
      subtitle,
      statusLabel: statusInfo?.label || "Status desconhecido",
      icon: statusInfo?.icon,
      actions: (
        <MoreOptionsButton
          navItems={navItems}
          placement="bottomRight"
          arrowPlacement="right"
        />
      ),
    },
    sections: [<InfoGrid key={`info-${portfolio.id}`} rows={rows} />],
    footer: <ProgressFooter percent={percent} />,
  };
}
