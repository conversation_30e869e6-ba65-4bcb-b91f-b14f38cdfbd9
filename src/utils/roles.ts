import jwt from "jsonwebtoken";

interface RolesInPages {
  [key: string]: { account: string[]; userInAccount: string[] };
}

const rolesInPages: RolesInPages = {
  "/imports": {
    account: ["BASIC"],
    userInAccount: ["ADMIN"],
  },
  "/imports/create": {
    account: ["BASIC"],
    userInAccount: ["ADMIN"],
  },
  "/portfolio": {
    account: ["BASIC"],
    userInAccount: ["ADMIN"],
  },
  "/portfolio/[id]": {
    account: ["BASIC"],
    userInAccount: ["ADMIN"],
  },
};

export function getJWTPayloadInfo(token: string): {
  accountRole: string;
  userRoleInAccount: string;
  accountId: string;
  userId: string;
} {
  const decoded = jwt.decode(token) as {
    accountRole: string;
    roleInAccount: string;
    accountId: string;
    userId: string;
  };
  return {
    accountRole: decoded.accountRole,
    userRoleInAccount: decoded.roleInAccount,
    accountId: decoded.accountId,
    userId: decoded.userId,
  };
}

export function validateAccountRoleAccess(role: string, path: string) {
  let redirectUser = false;
  for (const route in rolesInPages) {
    if (path.includes(route) && !rolesInPages[route].account.includes(role)) {
      redirectUser = true;
      break;
    }
  }
  return redirectUser;
}

export function validateUserInAccountRoleAccess(role: string, path: string) {
  let redirectUser = false;
  for (const route in rolesInPages) {
    if (
      path.includes(route) &&
      !rolesInPages[route].userInAccount.includes(role)
    ) {
      redirectUser = true;
      break;
    }
  }
  return redirectUser;
}
